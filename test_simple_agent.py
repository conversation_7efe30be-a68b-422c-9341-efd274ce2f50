#!/usr/bin/env python3
"""Simple test to verify agent workflow step by step."""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.agent import PaperAgent
from app.models.state import SessionInfo, PaperInfo


async def test_simple_workflow():
    """Test the agent workflow step by step."""
    print("🧪 Testing Simple Agent Workflow")
    
    # Initialize agent
    agent = PaperAgent()
    
    # Test 1: Simple message without tools
    print("\n1️⃣ Testing simple message...")
    result1 = await agent.process_message(
        message="Hello, can you help me?",
        session_id="test-simple",
        paper_id=None
    )
    print(f"✅ Simple message result: {result1.get('success', False)}")
    
    # Test 2: Web search only
    print("\n2️⃣ Testing web search only...")
    result2 = await agent.process_message(
        message="Do a web search on molten carbonate fuel cells",
        session_id="test-simple",
        paper_id=None
    )
    print(f"✅ Web search result: {result2.get('success', False)}")
    
    # Test 3: Web search with paper update
    print("\n3️⃣ Testing web search with paper update...")
    result3 = await agent.process_message(
        message="Do a web search on molten carbonate fuel cells and update the paper with the results",
        session_id="test-simple",
        paper_id="rec5XXDUxN2RM1L3A"  # Use the existing paper ID
    )
    print(f"✅ Web search + update result: {result3.get('success', False)}")
    print(f"📄 Paper updated: {result3.get('paper_updated', False)}")
    print(f"📄 Updated paper ID: {result3.get('updated_paper_id')}")
    
    # Test 4: Direct tool calls
    print("\n4️⃣ Testing direct tool sequence...")
    
    # Step 1: Search
    print("   🔍 Step 1: Performing web search...")
    from app.tools.research import firecrawl_search_tool
    search_result = firecrawl_search_tool._run(query="molten carbonate fuel cells", limit=3)
    print(f"   ✅ Search success: {search_result.get('success', False)}")
    
    # Step 2: Save user changes
    print("   💾 Step 2: Saving user changes...")
    from app.tools.airtable import save_user_changes_tool
    save_result = save_user_changes_tool._run(reason="Testing direct tool calls")
    print(f"   ✅ Save success: {save_result.get('success', False)}")
    
    # Step 3: Update paper content
    print("   📝 Step 3: Updating paper content...")
    from app.tools.airtable import paper_update_tool
    
    # Format the search results
    if search_result.get('success') and search_result.get('results'):
        content_to_add = "\n\n## Research on Molten Carbonate Fuel Cells\n\n"
        for i, result in enumerate(search_result['results'][:3], 1):
            title = result.get('title', 'No title')
            url = result.get('url', '')
            content_to_add += f"{i}. **{title}**\n   Source: {url}\n\n"
        
        update_result = paper_update_tool._run(
            paper_id="rec5XXDUxN2RM1L3A",
            content_to_add=content_to_add,
            insertion_point="current_position"
        )
        print(f"   ✅ Update success: {update_result.get('success', False)}")
        if not update_result.get('success'):
            print(f"   ❌ Update error: {update_result.get('error')}")
    else:
        print("   ❌ No search results to add")
    
    print("\n🎉 Test completed!")


if __name__ == "__main__":
    asyncio.run(test_simple_workflow())
