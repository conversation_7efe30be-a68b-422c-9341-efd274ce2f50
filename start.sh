#!/bin/bash

# Paper Agent Startup Script
# This script starts the backend, frontend, and checks Redis status

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pids=$(lsof -ti :$port)
    if [ ! -z "$pids" ]; then
        print_warning "Killing existing processes on port $port"
        echo $pids | xargs kill -9
        sleep 2
    fi
}

# Function to check Redis
check_redis() {
    print_status "Checking Redis status..."
    
    if command_exists redis-cli; then
        if redis-cli ping >/dev/null 2>&1; then
            print_success "Redis is running and responding to ping"
            
            # Get Redis info
            redis_version=$(redis-cli info server | grep redis_version | cut -d: -f2 | tr -d '\r')
            redis_memory=$(redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
            redis_clients=$(redis-cli info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
            
            echo "  Redis Version: $redis_version"
            echo "  Memory Usage: $redis_memory"
            echo "  Connected Clients: $redis_clients"
            
            return 0
        else
            print_error "Redis is not responding to ping"
            return 1
        fi
    else
        print_error "redis-cli not found. Please install Redis."
        return 1
    fi
}

# Function to start Redis if not running
start_redis() {
    print_status "Starting Redis..."
    
    if command_exists redis-server; then
        # Check if Redis is already running
        if ! redis-cli ping >/dev/null 2>&1; then
            print_status "Starting Redis server..."
            redis-server --daemonize yes
            sleep 3
            
            if redis-cli ping >/dev/null 2>&1; then
                print_success "Redis started successfully"
            else
                print_error "Failed to start Redis"
                return 1
            fi
        else
            print_success "Redis is already running"
        fi
    else
        print_error "redis-server not found. Please install Redis."
        print_status "On Ubuntu/Debian: sudo apt-get install redis-server"
        print_status "On macOS: brew install redis"
        return 1
    fi
}

# Function to check Python environment
check_python_env() {
    print_status "Checking Python environment..."
    
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found. Please run: python -m venv venv"
        return 1
    fi
    
    if [ ! -f "venv/bin/activate" ]; then
        print_error "Virtual environment activation script not found"
        return 1
    fi
    
    print_success "Python virtual environment found"
    return 0
}

# Function to check Node.js environment
check_node_env() {
    print_status "Checking Node.js environment..."
    
    if ! command_exists node; then
        print_error "Node.js not found. Please install Node.js"
        return 1
    fi
    
    if ! command_exists npm; then
        print_error "npm not found. Please install npm"
        return 1
    fi
    
    if [ ! -d "frontend/node_modules" ]; then
        print_warning "Frontend dependencies not installed"
        print_status "Installing frontend dependencies..."
        cd frontend && npm install && cd ..
    fi
    
    print_success "Node.js environment ready"
    return 0
}

# Function to start backend
start_backend() {
    print_status "Starting backend server..."
    
    # Kill any existing backend process
    kill_port 8000
    
    # Activate virtual environment and start backend
    source venv/bin/activate
    
    print_status "Starting Python backend on port 8000..."
    python main.py &
    BACKEND_PID=$!
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/api/v1/health >/dev/null 2>&1; then
            print_success "Backend started successfully (PID: $BACKEND_PID)"
            return 0
        fi
        sleep 1
    done
    
    print_error "Backend failed to start within 30 seconds"
    return 1
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend server..."
    
    # Kill any existing frontend process
    kill_port 3000
    
    cd frontend
    
    print_status "Starting React frontend on port 3000..."
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Frontend started successfully (PID: $FRONTEND_PID)"
            return 0
        fi
        sleep 1
    done
    
    print_error "Frontend failed to start within 60 seconds"
    return 1
}

# Function to show status
show_status() {
    echo
    print_status "=== Paper Agent Status ==="
    
    # Check Redis
    if redis-cli ping >/dev/null 2>&1; then
        print_success "✓ Redis: Running"
    else
        print_error "✗ Redis: Not running"
    fi
    
    # Check Backend
    if curl -s http://localhost:8000/api/v1/health >/dev/null 2>&1; then
        print_success "✓ Backend: Running (http://localhost:8000)"
    else
        print_error "✗ Backend: Not running"
    fi
    
    # Check Frontend
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        print_success "✓ Frontend: Running (http://localhost:3000)"
    else
        print_error "✗ Frontend: Not running"
    fi
    
    echo
    print_status "=== Quick Links ==="
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8000/api/v1"
    echo "  Health Check: http://localhost:8000/api/v1/health"
    echo
}

# Main execution
main() {
    echo
    print_status "=== Paper Agent Startup Script ==="
    echo
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Please create one with your configuration."
    fi
    
    # Check and start Redis
    if ! check_redis; then
        start_redis || exit 1
    fi
    
    # Check Python environment
    check_python_env || exit 1
    
    # Check Node.js environment
    check_node_env || exit 1
    
    # Start backend
    start_backend || exit 1
    
    # Start frontend
    start_frontend || exit 1
    
    # Show final status
    show_status
    
    print_success "All services started successfully!"
    print_status "Press Ctrl+C to stop all services"
    
    # Wait for user interrupt
    trap 'echo; print_status "Shutting down services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT
    wait
}

# Run main function
main "$@"
