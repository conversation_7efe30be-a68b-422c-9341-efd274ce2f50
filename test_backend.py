"""Test script for the Paper Agent backend."""

import asyncio
import json
from app.core.agent import PaperAgent
from app.services.paper_service import PaperService
from app.core.memory import Session<PERSON>anager


async def test_basic_functionality():
    """Test basic functionality of the backend."""
    print("🧪 Testing Paper Agent Backend...")
    
    # Test 1: Session Management
    print("\n1. Testing Session Management...")
    session_manager = SessionManager()
    session_id = session_manager.create_session()
    print(f"✅ Created session: {session_id}")
    
    session_info = session_manager.get_session(session_id)
    print(f"✅ Retrieved session: {session_info.session_id}")
    
    # Test 2: Paper Service
    print("\n2. Testing Paper Service...")
    paper_service = PaperService()
    
    # Create a test paper
    paper = paper_service.create_paper(
        title="Test Research Paper",
        content="""# Introduction

This is a test paper for the Paper Agent system.

## Background

The Paper Agent uses LangGraph for better state management.

### Key Features

- Session management
- Real-time streaming
- Tool integration
- Paper rendering

## Methodology

We implemented the following tools:
1. Research tools (FireCrawl, Deep Research)
2. Data management (Airtable)
3. Visualization (QuickChart)

## Results

The system shows improved flexibility over N8N.

## Conclusion

LangGraph provides better control flow and state management.
""",
        session_id=session_id
    )
    print(f"✅ Created paper: {paper.paper_id}")
    
    # Test paper rendering
    rendered_result = paper_service.get_paper_with_rendering(paper.paper_id)
    if rendered_result["success"]:
        print("✅ Paper rendering successful")
        print(f"   - Word count: {rendered_result['rendered']['word_count']}")
        print(f"   - Sections: {rendered_result['outline']['section_count']}")
    else:
        print(f"❌ Paper rendering failed: {rendered_result['error']}")
    
    # Test 3: Agent (without external APIs)
    print("\n3. Testing Agent (basic functionality)...")
    try:
        agent = PaperAgent()
        print("✅ Agent initialized successfully")
        
        # Test message processing (this will fail without API keys, but we can test initialization)
        print("✅ Agent tools loaded:")
        for tool in agent.tools:
            print(f"   - {tool.name}")
            
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
    
    print("\n🎉 Basic functionality tests completed!")
    print("\n📝 Next steps:")
    print("1. Set up environment variables in .env file")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Run the server: python main.py")
    print("4. Test API endpoints with curl or Postman")


def test_tool_configurations():
    """Test tool configurations without making actual API calls."""
    print("\n🔧 Testing Tool Configurations...")
    
    from app.tools.research import firecrawl_search_tool, deep_research_tool
    from app.tools.airtable import airtable_overview_tool
    from app.tools.visualization import line_chart_tool
    
    tools_to_test = [
        firecrawl_search_tool,
        deep_research_tool,
        airtable_overview_tool,
        line_chart_tool
    ]
    
    for tool in tools_to_test:
        print(f"✅ {tool.name}: {tool.description[:50]}...")
    
    print("✅ All tool configurations are valid")


def test_paper_rendering():
    """Test markdown rendering functionality."""
    print("\n📄 Testing Paper Rendering...")
    
    paper_service = PaperService()
    
    test_content = """# Test Paper

## Abstract
This is a test abstract with **bold** and *italic* text.

## Introduction
Here's some code:

```python
def hello_world():
    print("Hello, World!")
```

## Data
| Metric | Value |
|--------|-------|
| Accuracy | 95% |
| Precision | 92% |

## References
- [@smith2023] - Important paper
- (Johnson et al., 2022) - Another reference
"""
    
    # Test rendering
    rendered = paper_service.renderer.render_markdown(test_content)
    if rendered["success"]:
        print("✅ Markdown rendering successful")
        print(f"   - Word count: {rendered['word_count']}")
        print(f"   - Character count: {rendered['char_count']}")
    
    # Test citation extraction
    citations = paper_service.renderer.extract_citations(test_content)
    print(f"✅ Found {len(citations)} citations")
    
    # Test outline generation
    outline = paper_service.renderer.generate_paper_outline(test_content)
    print(f"✅ Generated outline with {outline['section_count']} sections")


if __name__ == "__main__":
    print("🚀 Starting Paper Agent Backend Tests")
    
    # Run synchronous tests
    test_tool_configurations()
    test_paper_rendering()
    
    # Run async tests
    asyncio.run(test_basic_functionality())
    
    print("\n✨ All tests completed!")
