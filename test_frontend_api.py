#!/usr/bin/env python3

import asyncio
import aiohttp
import json

async def test_frontend_api():
    """Test the chat API exactly like the frontend does."""
    
    url = "http://localhost:8000/api/v1/chat/stream"
    
    payload = {
        "message": "Could you do a websearch on molten carbonate fuel cells and update the paper?",
        "session_id": "frontend-test-session",
        "paper_id": "recPwNZXLI4mrSYTI",
        "cursor_position": None
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print(f"🧪 Testing frontend API call...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                print(f"Response status: {response.status}")
                print(f"Response headers: {dict(response.headers)}")
                
                if response.status != 200:
                    text = await response.text()
                    print(f"Error response: {text}")
                    return
                
                print("\n🔄 Streaming response:")
                print("-" * 50)
                
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    if line_str:
                        print(f"Raw line: {repr(line_str)}")
                        
                        # Parse SSE format
                        if line_str.startswith('event: '):
                            event_type = line_str[7:]
                            print(f"  Event: {event_type}")
                        elif line_str.startswith('data: '):
                            data_str = line_str[6:]
                            try:
                                data = json.loads(data_str)
                                print(f"  Data: {json.dumps(data, indent=4)}")
                            except json.JSONDecodeError:
                                print(f"  Data (raw): {data_str}")
                        elif line_str == '':
                            print("  (empty line)")
                        else:
                            print(f"  Other: {line_str}")
                
                print("-" * 50)
                print("✅ Test completed")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_frontend_api())
