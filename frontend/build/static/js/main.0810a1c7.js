/*! For license information please see main.0810a1c7.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),a=n(43),l=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),k=Symbol.for("react.consumer"),w=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),C=Symbol.for("react.lazy");Symbol.for("react.scope");var _=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var j=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var T=Symbol.iterator;function P(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=T&&e[T]||e["@@iterator"])?e:null}var z=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===z?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case S:return"Suspense";case E:return"SuspenseList";case _:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case w:return(e.displayName||"Context")+".Provider";case k:return(e._context.displayName||"Context")+".Consumer";case x:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return R(e(t))}catch(n){}}return null}var L=Array.isArray,O=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,M={pending:!1,data:null,method:null,action:null},D=[],I=-1;function $(e){return{current:e}}function F(e){0>I||(e.current=D[I],D[I]=null,I--)}function U(e,t){I++,D[I]=e.current,e.current=t}var H=$(null),B=$(null),W=$(null),q=$(null);function V(e,t){switch(U(W,t),U(B,e),U(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ld(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(H),U(H,e)}function Q(){F(H),F(B),F(W)}function G(e){null!==e.memoizedState&&U(q,e);var t=H.current,n=ld(t,e.type);t!==n&&(U(B,e),U(H,n))}function K(e){B.current===e&&(F(H),F(B)),q.current===e&&(F(q),Gd._currentValue=M)}var Y=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,Z=r.unstable_cancelCallback,J=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,le=r.unstable_NormalPriority,ie=r.unstable_LowPriority,oe=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof se&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(he(e)/me|0)|0},he=Math.log,me=Math.LN2;var ge=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var o=134217727&r;return 0!==o?0!==(r=o&~l)?a=ve(r):0!==(i&=o)?a=ve(i):n||0!==(n=o&~e)&&(a=ve(n)):0!==(o=r&~l)?a=ve(o):0!==i?a=ve(i):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&0!==(4194048&n))?t:a}function ke(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function xe(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ne(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ce(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function _e(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function je(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Te(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Pe(){var e=A.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var ze=Math.random().toString(36).slice(2),Re="__reactFiber$"+ze,Le="__reactProps$"+ze,Oe="__reactContainer$"+ze,Ae="__reactEvents$"+ze,Me="__reactListeners$"+ze,De="__reactHandles$"+ze,Ie="__reactResources$"+ze,$e="__reactMarker$"+ze;function Fe(e){delete e[Re],delete e[Le],delete e[Ae],delete e[Me],delete e[De]}function Ue(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Oe]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Re])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Re]||e[Oe]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Be(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function We(e){var t=e[Ie];return t||(t=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function qe(e){e[$e]=!0}var Ve=new Set,Qe={};function Ge(e,t){Ke(e,t),Ke(e+"Capture",t)}function Ke(e,t){for(Qe[e]=t,e=0;e<t.length;e++)Ve.add(t[e])}var Ye,Xe,Ze=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},et={};function tt(e,t,n){if(a=t,Y.call(et,a)||!Y.call(Je,a)&&(Ze.test(a)?et[a]=!0:(Je[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ye)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ye=t&&t[1]||"",Xe=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ye+e+Xe}var lt=!1;function it(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){r=l}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(o){if(o&&r&&"string"===typeof o.stack)return[o.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),i=l[0],o=l[1];if(i&&o){var s=i.split("\n"),u=o.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function mt(e){return e.replace(ht,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,a,l,i,o){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?vt(e,i,ut(t)):null!=n?vt(e,i,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.name=""+ut(o):e.removeAttribute("name")}function yt(e,t,n,r,a,l,i,o){if(null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.type=l),null!=t||null!=n){if(!("submit"!==l&&"reset"!==l||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,o||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=o?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function kt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(L(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function xt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Nt(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Et(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&Et(e,l,t[l])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _t=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),jt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tt(e){return jt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Pt=null;function zt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,Lt=null;function Ot(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Le]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Le]||null;if(!a)throw Error(i(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":kt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var At=!1;function Mt(e,t,n){if(At)return e(t,n);At=!0;try{return e(t)}finally{if(At=!1,(null!==Rt||null!==Lt)&&(Uu(),Rt&&(t=Rt,e=Lt,Lt=Rt=null,Ot(t),e)))for(t=0;t<e.length;t++)Ot(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Le]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var It=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),$t=!1;if(It)try{var Ft={};Object.defineProperty(Ft,"passive",{get:function(){$t=!0}}),window.addEventListener("test",Ft,Ft),window.removeEventListener("test",Ft,Ft)}catch(Of){$t=!1}var Ut=null,Ht=null,Bt=null;function Wt(){if(Bt)return Bt;var e,t,n=Ht,r=n.length,a="value"in Ut?Ut.value:Ut.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[l-t];t++);return Bt=a.slice(e,1<t?1-t:void 0)}function qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Vt(){return!0}function Qt(){return!1}function Gt(e){function t(t,n,r,a,l){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Vt:Qt,this.isPropagationStopped=Qt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Kt,Yt,Xt,Zt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Jt=Gt(Zt),en=f({},Zt,{view:0,detail:0}),tn=Gt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Kt=e.screenX-Xt.screenX,Yt=e.screenY-Xt.screenY):Yt=Kt=0,Xt=e),Kt)},movementY:function(e){return"movementY"in e?e.movementY:Yt}}),rn=Gt(nn),an=Gt(f({},nn,{dataTransfer:0})),ln=Gt(f({},en,{relatedTarget:0})),on=Gt(f({},Zt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Gt(f({},Zt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Gt(f({},Zt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function hn(){return pn}var mn=Gt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Gt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Gt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),vn=Gt(f({},Zt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Gt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),kn=Gt(f({},Zt,{newState:0,oldState:0})),wn=[9,13,27,32],xn=It&&"CompositionEvent"in window,Sn=null;It&&"documentMode"in document&&(Sn=document.documentMode);var En=It&&"TextEvent"in window&&!Sn,Nn=It&&(!xn||Sn&&8<Sn&&11>=Sn),Cn=String.fromCharCode(32),_n=!1;function jn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Pn=!1;var zn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!zn[e.type]:"textarea"===t}function Ln(e,t,n,r){Rt?Lt?Lt.push(r):Lt=[r]:Rt=r,0<(t=Wc(t,"onChange")).length&&(n=new Jt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var On=null,An=null;function Mn(e){Mc(e,0)}function Dn(e){if(ft(Be(e)))return e}function In(e,t){if("change"===e)return t}var $n=!1;if(It){var Fn;if(It){var Un="oninput"in document;if(!Un){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Un="function"===typeof Hn.oninput}Fn=Un}else Fn=!1;$n=Fn&&(!document.documentMode||9<document.documentMode)}function Bn(){On&&(On.detachEvent("onpropertychange",Wn),An=On=null)}function Wn(e){if("value"===e.propertyName&&Dn(An)){var t=[];Ln(t,An,e,zt(e)),Mt(Mn,t)}}function qn(e,t,n){"focusin"===e?(Bn(),An=n,(On=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&Bn()}function Vn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(An)}function Qn(e,t){if("click"===e)return Dn(t)}function Gn(e,t){if("input"===e||"change"===e)return Dn(t)}var Kn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Yn(e,t){if(Kn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Y.call(t,a)||!Kn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Jn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Jn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=It&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,lr=null,ir=!1;function or(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Yn(lr,r)||(lr=r,0<(r=Wc(ar,"onSelect")).length&&(t=new Jt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}It&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),hr=fr("animationiteration"),mr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),kr=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xr(e,t){kr.set(e,t),Ge(t,[e])}wr.push("scrollEnd");var Sr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Nr=[],Cr=0,_r=0;function jr(){for(var e=Cr,t=_r=Cr=0;t<e;){var n=Nr[t];Nr[t++]=null;var r=Nr[t];Nr[t++]=null;var a=Nr[t];Nr[t++]=null;var l=Nr[t];if(Nr[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==l&&Rr(n,a,l)}}function Tr(e,t,n,r){Nr[Cr++]=e,Nr[Cr++]=t,Nr[Cr++]=n,Nr[Cr++]=r,_r|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return Tr(e,t,n,r),Lr(e)}function zr(e,t){return Tr(e,null,null,t),Lr(e)}function Rr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Lr(e){if(50<Ru)throw Ru=0,Lu=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Or={};function Ar(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mr(e,t,n,r){return new Ar(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,t){var n=e.alternate;return null===n?((n=Mr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function $r(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Fr(e,t,n,r,a,l){var o=0;if(r=e,"function"===typeof e)Dr(e)&&(o=1);else if("string"===typeof e)o=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case _:return(e=Mr(31,n,t,a)).elementType=_,e.lanes=l,e;case g:return Ur(n.children,a,l,t);case y:o=8,a|=24;break;case v:return(e=Mr(12,n,t,2|a)).elementType=v,e.lanes=l,e;case S:return(e=Mr(13,n,t,a)).elementType=S,e.lanes=l,e;case E:return(e=Mr(19,n,t,a)).elementType=E,e.lanes=l,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case w:o=10;break e;case k:o=9;break e;case x:o=11;break e;case N:o=14;break e;case C:o=16,r=null;break e}o=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=Mr(o,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Ur(e,t,n,r){return(e=Mr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Mr(6,e,null,t)).lanes=n,e}function Br(e,t,n){return(t=Mr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],qr=0,Vr=null,Qr=0,Gr=[],Kr=0,Yr=null,Xr=1,Zr="";function Jr(e,t){Wr[qr++]=Qr,Wr[qr++]=Vr,Vr=e,Qr=t}function ea(e,t,n){Gr[Kr++]=Xr,Gr[Kr++]=Zr,Gr[Kr++]=Yr,Yr=e;var r=Xr;e=Zr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var l=32-pe(t)+a;if(30<l){var i=a-a%5;l=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Xr=1<<32-pe(t)+a|n<<a|r,Zr=l+e}else Xr=1<<l|n<<a|r,Zr=e}function ta(e){null!==e.return&&(Jr(e,1),ea(e,1,0))}function na(e){for(;e===Vr;)Vr=Wr[--qr],Wr[qr]=null,Qr=Wr[--qr],Wr[qr]=null;for(;e===Yr;)Yr=Gr[--Kr],Gr[Kr]=null,Zr=Gr[--Kr],Gr[Kr]=null,Xr=Gr[--Kr],Gr[Kr]=null}var ra=null,aa=null,la=!1,ia=null,oa=!1,sa=Error(i(519));function ua(e){throw ma(Er(Error(i(418,"")),e)),sa}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Re]=e,t[Le]=r,n){case"dialog":Dc("cancel",t),Dc("close",t);break;case"iframe":case"object":case"embed":Dc("load",t);break;case"video":case"audio":for(n=0;n<Oc.length;n++)Dc(Oc[n],t);break;case"source":Dc("error",t);break;case"img":case"image":case"link":Dc("error",t),Dc("load",t);break;case"details":Dc("toggle",t);break;case"input":Dc("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Dc("invalid",t);break;case"textarea":Dc("invalid",t),wt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Yc(t.textContent,n)?(null!=r.popover&&(Dc("beforetoggle",t),Dc("toggle",t)),null!=r.onScroll&&Dc("scroll",t),null!=r.onScrollEnd&&Dc("scrollend",t),null!=r.onClick&&(t.onclick=Xc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(oa=!1);case 27:case 3:return void(oa=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!la)return da(e),la=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,pd(e.type)?(e=vd,vd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,la=!1}function ha(){var e=ia;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),ia=null),e}function ma(e){null===ia?ia=[e]:ia.push(e)}var ga=$(null),ya=null,va=null;function ba(e,t,n){U(ga,t._currentValue),t._currentValue=n}function ka(e){e._currentValue=ga.current,F(ga)}function wa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var o=a.child;l=l.firstContext;e:for(;null!==l;){var s=l;l=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),wa(l.return,n,e),r||(o=null);break e}l=s.next}}else if(18===a.tag){if(null===(o=a.return))throw Error(i(341));o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),wa(o,n,e),o=null}else o=a.child;if(null!==o)o.return=a;else for(o=a;null!==o;){if(o===e){o=null;break}if(null!==(a=o.sibling)){a.return=o.return,o=a;break}o=o.return}a=o}}function Sa(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(0!==(524288&a.flags))l=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var o=a.alternate;if(null===o)throw Error(i(387));if(null!==(o=o.memoizedProps)){var s=a.type;Kn(a.pendingProps.value,o.value)||(null!==e?e.push(s):e=[s])}}else if(a===q.current){if(null===(o=a.alternate))throw Error(i(387));o.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Gd):e=[Gd])}a=a.return}null!==e&&xa(t,e,n,r),t.flags|=262144}function Ea(e){for(e=e.firstContext;null!==e;){if(!Kn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Na(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ca(e){return ja(ya,e)}function _a(e,t){return null===ya&&Na(e),ja(e,t)}function ja(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(i(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Ta="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Pa=r.unstable_scheduleCallback,za=r.unstable_NormalPriority,Ra={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function La(){return{controller:new Ta,data:new Map,refCount:0}}function Oa(e){e.refCount--,0===e.refCount&&Pa(za,function(){e.controller.abort()})}var Aa=null,Ma=0,Da=0,Ia=null;function $a(){if(0===--Ma&&null!==Aa){null!==Ia&&(Ia.status="fulfilled");var e=Aa;Aa=null,Da=0,Ia=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Fa=O.S;O.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Aa){var n=Aa=[];Ma=0,Da=Tc(),Ia={status:"pending",value:void 0,then:function(e){n.push(e)}}}Ma++,t.then($a,$a)}(0,t),null!==Fa&&Fa(e,t)};var Ua=$(null);function Ha(){var e=Ua.current;return null!==e?e:ru.pooledCache}function Ba(e,t){U(Ua,null===t?Ua.current:t.pool)}function Wa(){var e=Ha();return null===e?null:{parent:Ra._currentValue,pool:e}}var qa=Error(i(460)),Va=Error(i(474)),Qa=Error(i(542)),Ga={then:function(){}};function Ka(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ya(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ya,Ya),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ya,Ya);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e}throw Za=t,qa}}var Za=null;function Ja(){if(null===Za)throw Error(i(459));var e=Za;return Za=null,e}function el(e){if(e===qa||e===Qa)throw Error(i(483))}var tl=!1;function nl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function al(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ll(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Lr(e),Rr(e,null,n),t}return Tr(e,r,t,n),Lr(e)}function il(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,_e(e,n)}}function ol(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var sl=!1;function ul(){if(sl){if(null!==Ia)throw Ia}}function cl(e,t,n,r){sl=!1;var a=e.updateQueue;tl=!1;var l=a.firstBaseUpdate,i=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,u=s.next;s.next=null,null===i?l=u:i.next=u,i=s;var c=e.alternate;null!==c&&((o=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===o?c.firstBaseUpdate=u:o.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(i=0,c=u=s=null,o=l;;){var p=-536870913&o.lane,h=p!==o.lane;if(h?(lu&p)===p:(r&p)===p){0!==p&&p===Da&&(sl=!0),null!==c&&(c=c.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var m=e,g=o;p=t;var y=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(y,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(p="function"===typeof(m=g.payload)?m.call(y,d,p):m)||void 0===p)break e;d=f({},d,p);break e;case 2:tl=!0}}null!==(p=o.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===c?(u=c=h,s=d):c=c.next=h,i|=p;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(h=o).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===l&&(a.shared.lanes=0),pu|=i,e.lanes=i,e.memoizedState=d}}function dl(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function fl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)dl(n[e],t)}var pl=$(null),hl=$(0);function ml(e,t){U(hl,e=du),U(pl,t),du=e|t.baseLanes}function gl(){U(hl,du),U(pl,pl.current)}function yl(){du=hl.current,F(pl),F(hl)}var vl=0,bl=null,kl=null,wl=null,xl=!1,Sl=!1,El=!1,Nl=0,Cl=0,_l=null,jl=0;function Tl(){throw Error(i(321))}function Pl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Kn(e[n],t[n]))return!1;return!0}function zl(e,t,n,r,a,l){return vl=l,bl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=null===e||null===e.memoizedState?qi:Vi,El=!1,l=n(r,a),El=!1,Sl&&(l=Ll(t,n,r,a)),Rl(e),l}function Rl(e){O.H=Wi;var t=null!==kl&&null!==kl.next;if(vl=0,wl=kl=bl=null,xl=!1,Cl=0,_l=null,t)throw Error(i(300));null===e||_o||null!==(e=e.dependencies)&&Ea(e)&&(_o=!0)}function Ll(e,t,n,r){bl=e;var a=0;do{if(Sl&&(_l=null),Cl=0,Sl=!1,25<=a)throw Error(i(301));if(a+=1,wl=kl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}O.H=Qi,l=t(n,r)}while(Sl);return l}function Ol(){var e=O.H,t=e.useState()[0];return t="function"===typeof t.then?Fl(t):t,e=e.useState()[0],(null!==kl?kl.memoizedState:null)!==e&&(bl.flags|=1024),t}function Al(){var e=0!==Nl;return Nl=0,e}function Ml(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Dl(e){if(xl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}xl=!1}vl=0,wl=kl=bl=null,Sl=!1,Cl=Nl=0,_l=null}function Il(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===wl?bl.memoizedState=wl=e:wl=wl.next=e,wl}function $l(){if(null===kl){var e=bl.alternate;e=null!==e?e.memoizedState:null}else e=kl.next;var t=null===wl?bl.memoizedState:wl.next;if(null!==t)wl=t,kl=e;else{if(null===e){if(null===bl.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(kl=e).memoizedState,baseState:kl.baseState,baseQueue:kl.baseQueue,queue:kl.queue,next:null},null===wl?bl.memoizedState=wl=e:wl=wl.next=e}return wl}function Fl(e){var t=Cl;return Cl+=1,null===_l&&(_l=[]),e=Xa(_l,e,t),t=bl,null===(null===wl?t.memoizedState:wl.next)&&(t=t.alternate,O.H=null===t||null===t.memoizedState?qi:Vi),e}function Ul(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Fl(e);if(e.$$typeof===w)return Ca(e)}throw Error(i(438,String(e)))}function Hl(e){var t=null,n=bl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=bl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=j;return t.index++,n}function Bl(e,t){return"function"===typeof t?t(e):t}function Wl(e){return ql($l(),kl,e)}function ql(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var o=a.next;a.next=l.next,l.next=o}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var s=o=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(lu&f)===f:(vl&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Da&&(d=!0);else{if((vl&p)===p){c=c.next,p===Da&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,o=l):u=u.next=f,bl.lanes|=p,pu|=p}f=c.action,El&&n(l,f),l=c.hasEagerState?c.eagerState:n(l,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,o=l):u=u.next=p,bl.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?o=l:u.next=s,!Kn(l,e.memoizedState)&&(_o=!0,d&&null!==(n=Ia)))throw n;e.memoizedState=l,e.baseState=o,e.baseQueue=u,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Vl(e){var t=$l(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{l=e(l,o.action),o=o.next}while(o!==a);Kn(l,t.memoizedState)||(_o=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Ql(e,t,n){var r=bl,a=$l(),l=la;if(l){if(void 0===n)throw Error(i(407));n=n()}else n=t();var o=!Kn((kl||a).memoizedState,n);if(o&&(a.memoizedState=n,_o=!0),a=a.queue,gi(2048,8,Yl.bind(null,r,a,e),[e]),a.getSnapshot!==t||o||null!==wl&&1&wl.memoizedState.tag){if(r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Kl.bind(null,r,a,n,t),null),null===ru)throw Error(i(349));l||0!==(124&vl)||Gl(r,t,n)}return n}function Gl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=bl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Kl(e,t,n,r){t.value=n,t.getSnapshot=r,Xl(t)&&Zl(e)}function Yl(e,t,n){return n(function(){Xl(t)&&Zl(e)})}function Xl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Kn(e,n)}catch(r){return!0}}function Zl(e){var t=zr(e,2);null!==t&&Mu(t,e,2)}function Jl(e){var t=Il();if("function"===typeof e){var n=e;if(e=n(),El){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Bl,lastRenderedState:e},t}function ei(e,t,n,r){return e.baseState=n,ql(e,kl,"function"===typeof r?r:Bl)}function ti(e,t,n,r,a){if(Ui(e))throw Error(i(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==O.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,ni(t,l)):(l.next=n.next,t.pending=n.next=l)}}function ni(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=O.T,i={};O.T=i;try{var o=n(a,r),s=O.S;null!==s&&s(i,o),ri(e,t,o)}catch(u){li(e,t,u)}finally{O.T=l}}else try{ri(e,t,l=n(a,r))}catch(c){li(e,t,c)}}function ri(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){ai(e,t,n)},function(n){return li(e,t,n)}):ai(e,t,n)}function ai(e,t,n){t.status="fulfilled",t.value=n,ii(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ni(e,n)))}function li(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,ii(t),t=t.next}while(t!==r)}e.action=null}function ii(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oi(e,t){return t}function si(e,t){if(la){var n=ru.formState;if(null!==n){e:{var r=bl;if(la){if(aa){t:{for(var a=aa,l=oa;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Il()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:t},n.queue=r,n=Ii.bind(null,bl,r),r.dispatch=n,r=Jl(!1),l=Fi.bind(null,bl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Il()).queue=a,n=ti.bind(null,bl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ui(e){return ci($l(),kl,e)}function ci(e,t,n){if(t=ql(e,t,oi)[0],e=Wl(Bl)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Fl(t)}catch(i){if(i===qa)throw Qa;throw i}else r=t;var a=(t=$l()).queue,l=a.dispatch;return n!==t.memoizedState&&(bl.flags|=2048,pi(9,{destroy:void 0,resource:void 0},di.bind(null,a,n),null)),[r,l,e]}function di(e,t){e.action=t}function fi(e){var t=$l(),n=kl;if(null!==n)return ci(t,n,e);$l(),t=t.memoizedState;var r=(n=$l()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function pi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=bl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function hi(){return $l().memoizedState}function mi(e,t,n,r){var a=Il();r=void 0===r?null:r,bl.flags|=e,a.memoizedState=pi(1|t,{destroy:void 0,resource:void 0},n,r)}function gi(e,t,n,r){var a=$l();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==kl&&null!==r&&Pl(r,kl.memoizedState.deps)?a.memoizedState=pi(t,l,n,r):(bl.flags|=e,a.memoizedState=pi(1|t,l,n,r))}function yi(e,t){mi(8390656,8,e,t)}function vi(e,t){gi(2048,8,e,t)}function bi(e,t){return gi(4,2,e,t)}function ki(e,t){return gi(4,4,e,t)}function wi(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function xi(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4,4,wi.bind(null,t,e),n)}function Si(){}function Ei(e,t){var n=$l();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Pl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ni(e,t){var n=$l();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Pl(t,r[1]))return r[0];if(r=e(),El){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Ci(e,t,n){return void 0===n||0!==(1073741824&vl)?e.memoizedState=t:(e.memoizedState=n,e=Au(),bl.lanes|=e,pu|=e,n)}function _i(e,t,n,r){return Kn(n,t)?n:null!==pl.current?(e=Ci(e,n,r),Kn(e,t)||(_o=!0),e):0===(42&vl)?(_o=!0,e.memoizedState=n):(e=Au(),bl.lanes|=e,pu|=e,t)}function ji(e,t,n,r,a){var l=A.p;A.p=0!==l&&8>l?l:8;var i=O.T,o={};O.T=o,Fi(e,!1,t,n);try{var s=a(),u=O.S;if(null!==u&&u(o,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)$i(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(s,r),Ou());else $i(e,t,r,Ou())}catch(c){$i(e,t,{then:function(){},status:"rejected",reason:c},Ou())}finally{A.p=l,O.T=i}}function Ti(){}function Pi(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=zi(e).queue;ji(e,a,t,M,null===n?Ti:function(){return Ri(e),n(r)})}function zi(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:M,baseState:M,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Bl,lastRenderedState:M},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Bl,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ri(e){$i(e,zi(e).next.queue,{},Ou())}function Li(){return Ca(Gd)}function Oi(){return $l().memoizedState}function Ai(){return $l().memoizedState}function Mi(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Ou(),r=ll(t,e=al(n),n);return null!==r&&(Mu(r,t,n),il(r,t,n)),t={cache:La()},void(e.payload=t)}t=t.return}}function Di(e,t,n){var r=Ou();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ui(e)?Hi(t,n):null!==(n=Pr(e,t,n,r))&&(Mu(n,e,r),Bi(n,t,r))}function Ii(e,t,n){$i(e,t,n,Ou())}function $i(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ui(e))Hi(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,o=l(i,n);if(a.hasEagerState=!0,a.eagerState=o,Kn(o,i))return Tr(e,t,a,0),null===ru&&jr(),!1}catch(s){}if(null!==(n=Pr(e,t,a,r)))return Mu(n,e,r),Bi(n,t,r),!0}return!1}function Fi(e,t,n,r){if(r={lane:2,revertLane:Tc(),action:r,hasEagerState:!1,eagerState:null,next:null},Ui(e)){if(t)throw Error(i(479))}else null!==(t=Pr(e,n,r,2))&&Mu(t,e,2)}function Ui(e){var t=e.alternate;return e===bl||null!==t&&t===bl}function Hi(e,t){Sl=xl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Bi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,_e(e,n)}}var Wi={readContext:Ca,use:Ul,useCallback:Tl,useContext:Tl,useEffect:Tl,useImperativeHandle:Tl,useLayoutEffect:Tl,useInsertionEffect:Tl,useMemo:Tl,useReducer:Tl,useRef:Tl,useState:Tl,useDebugValue:Tl,useDeferredValue:Tl,useTransition:Tl,useSyncExternalStore:Tl,useId:Tl,useHostTransitionStatus:Tl,useFormState:Tl,useActionState:Tl,useOptimistic:Tl,useMemoCache:Tl,useCacheRefresh:Tl},qi={readContext:Ca,use:Ul,useCallback:function(e,t){return Il().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:yi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,mi(4194308,4,wi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){mi(4,2,e,t)},useMemo:function(e,t){var n=Il();t=void 0===t?null:t;var r=e();if(El){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Il();if(void 0!==n){var a=n(t);if(El){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Di.bind(null,bl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Il().memoizedState=e},useState:function(e){var t=(e=Jl(e)).queue,n=Ii.bind(null,bl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Si,useDeferredValue:function(e,t){return Ci(Il(),e,t)},useTransition:function(){var e=Jl(!1);return e=ji.bind(null,bl,e.queue,!0,!1),Il().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=bl,a=Il();if(la){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===ru)throw Error(i(349));0!==(124&lu)||Gl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,yi(Yl.bind(null,r,l,e),[e]),r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Kl.bind(null,r,l,n,t),null),n},useId:function(){var e=Il(),t=ru.identifierPrefix;if(la){var n=Zr;t="\xab"+t+"R"+(n=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+n),0<(n=Nl++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=jl++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Li,useFormState:si,useActionState:si,useOptimistic:function(e){var t=Il();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Fi.bind(null,bl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Hl,useCacheRefresh:function(){return Il().memoizedState=Mi.bind(null,bl)}},Vi={readContext:Ca,use:Ul,useCallback:Ei,useContext:Ca,useEffect:vi,useImperativeHandle:xi,useInsertionEffect:bi,useLayoutEffect:ki,useMemo:Ni,useReducer:Wl,useRef:hi,useState:function(){return Wl(Bl)},useDebugValue:Si,useDeferredValue:function(e,t){return _i($l(),kl.memoizedState,e,t)},useTransition:function(){var e=Wl(Bl)[0],t=$l().memoizedState;return["boolean"===typeof e?e:Fl(e),t]},useSyncExternalStore:Ql,useId:Oi,useHostTransitionStatus:Li,useFormState:ui,useActionState:ui,useOptimistic:function(e,t){return ei($l(),0,e,t)},useMemoCache:Hl,useCacheRefresh:Ai},Qi={readContext:Ca,use:Ul,useCallback:Ei,useContext:Ca,useEffect:vi,useImperativeHandle:xi,useInsertionEffect:bi,useLayoutEffect:ki,useMemo:Ni,useReducer:Vl,useRef:hi,useState:function(){return Vl(Bl)},useDebugValue:Si,useDeferredValue:function(e,t){var n=$l();return null===kl?Ci(n,e,t):_i(n,kl.memoizedState,e,t)},useTransition:function(){var e=Vl(Bl)[0],t=$l().memoizedState;return["boolean"===typeof e?e:Fl(e),t]},useSyncExternalStore:Ql,useId:Oi,useHostTransitionStatus:Li,useFormState:fi,useActionState:fi,useOptimistic:function(e,t){var n=$l();return null!==kl?ei(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Hl,useCacheRefresh:Ai},Gi=null,Ki=0;function Yi(e){var t=Ki;return Ki+=1,null===Gi&&(Gi=[]),Xa(Gi,e,t)}function Xi(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===p)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ji(e){return(0,e._init)(e._payload)}function eo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Ir(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===C&&Ji(l)===t.type)?(Xi(t=a(t,n.props),n),t.return=e,t):(Xi(t=Fr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Br(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Ur(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case h:return Xi(n=Fr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Br(t,e.mode,n)).return=e,t;case C:return f(e,t=(0,t._init)(t._payload),n)}if(L(t)||P(t))return(t=Ur(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Yi(t),n);if(t.$$typeof===w)return f(e,_a(e,t),n);Zi(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===a?u(e,t,n,r):null;case m:return n.key===a?c(e,t,n,r):null;case C:return p(e,t,n=(a=n._init)(n._payload),r)}if(L(n)||P(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Yi(n),r);if(n.$$typeof===w)return p(e,t,_a(e,n),r);Zi(e,n)}return null}function y(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(L(r)||P(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return y(e,t,n,Yi(r),a);if(r.$$typeof===w)return y(e,t,n,_a(t,r),a);Zi(t,r)}return null}function v(s,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case h:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===g){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===C&&Ji(b)===u.type){n(s,u.sibling),Xi(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===g?((d=Ur(c.props.children,s.mode,d,c.key)).return=s,s=d):(Xi(d=Fr(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return o(s);case m:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Br(c,s.mode,d)).return=s,s=d}return o(s);case C:return v(s,u,c=(b=c._init)(c._payload),d)}if(L(c))return function(a,i,o,s){for(var u=null,c=null,d=i,h=i=0,m=null;null!==d&&h<o.length;h++){d.index>h?(m=d,d=null):m=d.sibling;var g=p(a,d,o[h],s);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(a,d),i=l(g,i,h),null===c?u=g:c.sibling=g,c=g,d=m}if(h===o.length)return n(a,d),la&&Jr(a,h),u;if(null===d){for(;h<o.length;h++)null!==(d=f(a,o[h],s))&&(i=l(d,i,h),null===c?u=d:c.sibling=d,c=d);return la&&Jr(a,h),u}for(d=r(d);h<o.length;h++)null!==(m=y(d,a,h,o[h],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?h:m.key),i=l(m,i,h),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach(function(e){return t(a,e)}),la&&Jr(a,h),u}(s,u,c,d);if(P(c)){if("function"!==typeof(b=P(c)))throw Error(i(150));return function(a,o,s,u){if(null==s)throw Error(i(151));for(var c=null,d=null,h=o,m=o=0,g=null,v=s.next();null!==h&&!v.done;m++,v=s.next()){h.index>m?(g=h,h=null):g=h.sibling;var b=p(a,h,v.value,u);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&t(a,h),o=l(b,o,m),null===d?c=b:d.sibling=b,d=b,h=g}if(v.done)return n(a,h),la&&Jr(a,m),c;if(null===h){for(;!v.done;m++,v=s.next())null!==(v=f(a,v.value,u))&&(o=l(v,o,m),null===d?c=v:d.sibling=v,d=v);return la&&Jr(a,m),c}for(h=r(h);!v.done;m++,v=s.next())null!==(v=y(h,a,m,v.value,u))&&(e&&null!==v.alternate&&h.delete(null===v.key?m:v.key),o=l(v,o,m),null===d?c=v:d.sibling=v,d=v);return e&&h.forEach(function(e){return t(a,e)}),la&&Jr(a,m),c}(s,u,c=b.call(c),d)}if("function"===typeof c.then)return v(s,u,Yi(c),d);if(c.$$typeof===w)return v(s,u,_a(s,c),d);Zi(s,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Hr(c,s.mode,d)).return=s,s=d),o(s)):n(s,u)}return function(e,t,n,r){try{Ki=0;var a=v(e,t,n,r);return Gi=null,a}catch(i){if(i===qa||i===Qa)throw i;var l=Mr(29,i,null,e.mode);return l.lanes=r,l.return=e,l}}}var to=eo(!0),no=eo(!1),ro=$(null),ao=null;function lo(e){var t=e.alternate;U(uo,1&uo.current),U(ro,e),null===ao&&(null===t||null!==pl.current||null!==t.memoizedState)&&(ao=e)}function io(e){if(22===e.tag){if(U(uo,uo.current),U(ro,e),null===ao){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ao=e)}}else oo()}function oo(){U(uo,uo.current),U(ro,ro.current)}function so(e){F(ro),ao===e&&(ao=null),F(uo)}var uo=$(0);function co(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fo(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var po={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ou(),a=al(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Mu(t,e,r),il(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ou(),a=al(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Mu(t,e,r),il(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ou(),r=al(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ll(e,r,n))&&(Mu(t,e,n),il(t,e,n))}};function ho(e,t,n,r,a,l,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!Yn(n,r)||!Yn(a,l))}function mo(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&po.enqueueReplaceState(t,t.state,null)}function go(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yo="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vo(e){yo(e)}function bo(e){console.error(e)}function ko(e){yo(e)}function wo(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function xo(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function So(e,t,n){return(n=al(n)).tag=3,n.payload={element:null},n.callback=function(){wo(e,t)},n}function Eo(e){return(e=al(e)).tag=3,e}function No(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){xo(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){xo(t,n,r),"function"!==typeof a&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Co=Error(i(461)),_o=!1;function jo(e,t,n,r){t.child=null===e?no(t,null,n,r):to(t,e.child,n,r)}function To(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var i={};for(var o in r)"ref"!==o&&(i[o]=r[o])}else i=r;return Na(t),r=zl(e,t,n,i,l,a),o=Al(),null===e||_o?(la&&o&&ta(t),t.flags|=1,jo(e,t,r,a),t.child):(Ml(e,t,a),Ko(e,t,a))}function Po(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Dr(l)||void 0!==l.defaultProps||null!==n.compare?((e=Fr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,zo(e,t,l,r,a))}if(l=e.child,!Yo(e,a)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:Yn)(i,r)&&e.ref===t.ref)return Ko(e,t,a)}return t.flags|=1,(e=Ir(l,r)).ref=t.ref,e.return=t,t.child=e}function zo(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Yn(l,r)&&e.ref===t.ref){if(_o=!1,t.pendingProps=r=l,!Yo(e,a))return t.lanes=e.lanes,Ko(e,t,a);0!==(131072&e.flags)&&(_o=!0)}}return Ao(e,t,n,r,a)}function Ro(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Lo(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Lo(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ba(0,null!==l?l.cachePool:null),null!==l?ml(t,l):gl(),io(t)}else null!==l?(Ba(0,l.cachePool),ml(t,l),oo(),t.memoizedState=null):(null!==e&&Ba(0,null),gl(),oo());return jo(e,t,a,n),t.child}function Lo(e,t,n,r){var a=Ha();return a=null===a?null:{parent:Ra._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ba(0,null),gl(),io(t),null!==e&&Sa(e,t,r,!0),null}function Oo(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ao(e,t,n,r,a){return Na(t),n=zl(e,t,n,r,void 0,a),r=Al(),null===e||_o?(la&&r&&ta(t),t.flags|=1,jo(e,t,n,a),t.child):(Ml(e,t,a),Ko(e,t,a))}function Mo(e,t,n,r,a,l){return Na(t),t.updateQueue=null,n=Ll(t,r,n,a),Rl(e),r=Al(),null===e||_o?(la&&r&&ta(t),t.flags|=1,jo(e,t,n,l),t.child):(Ml(e,t,l),Ko(e,t,l))}function Do(e,t,n,r,a){if(Na(t),null===t.stateNode){var l=Or,i=n.contextType;"object"===typeof i&&null!==i&&(l=Ca(i)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=po,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},nl(t),i=n.contextType,l.context="object"===typeof i&&null!==i?Ca(i):Or,l.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(fo(t,n,i,r),l.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof l.getSnapshotBeforeUpdate||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||(i=l.state,"function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),i!==l.state&&po.enqueueReplaceState(l,l.state,null),cl(t,r,l,a),ul(),l.state=t.memoizedState),"function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var o=t.memoizedProps,s=go(n,o);l.props=s;var u=l.context,c=n.contextType;i=Or,"object"===typeof c&&null!==c&&(i=Ca(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof l.getSnapshotBeforeUpdate,o=t.pendingProps!==o,c||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o||u!==i)&&mo(t,l,r,i),tl=!1;var f=t.memoizedState;l.state=f,cl(t,r,l,a),ul(),u=t.memoizedState,o||f!==u||tl?("function"===typeof d&&(fo(t,n,d,r),u=t.memoizedState),(s=tl||ho(t,n,s,r,f,u,i))?(c||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=i,r=s):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,rl(e,t),c=go(n,i=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,s=Or,"object"===typeof u&&null!==u&&(s=Ca(u)),(u="function"===typeof(o=n.getDerivedStateFromProps)||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==s)&&mo(t,l,r,s),tl=!1,f=t.memoizedState,l.state=f,cl(t,r,l,a),ul();var p=t.memoizedState;i!==d||f!==p||tl||null!==e&&null!==e.dependencies&&Ea(e.dependencies)?("function"===typeof o&&(fo(t,n,o,r),p=t.memoizedState),(c=tl||ho(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&Ea(e.dependencies))?(u||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,p,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),l.props=r,l.state=p,l.context=s,r=c):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Oo(e,t),r=0!==(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=to(t,e.child,null,a),t.child=to(t,null,n,a)):jo(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Ko(e,t,a),e}function Io(e,t,n,r){return pa(),t.flags|=256,jo(e,t,n,r),t.child}var $o={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Fo(e){return{baseLanes:e,cachePool:Wa()}}function Uo(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gu),e}function Ho(e,t,n){var r,a=t.pendingProps,l=!1,o=0!==(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&0!==(2&uo.current)),r&&(l=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(la){if(l?lo(t):oo(),la){var s,u=aa;if(s=u){e:{for(s=u,u=oa;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Yr?{id:Xr,overflow:Zr}:null,retryLane:536870912,hydrationErrors:null},(s=Mr(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?t.lanes=32:t.lanes=536870912,null;so(t)}return u=a.children,a=a.fallback,l?(oo(),u=Wo({mode:"hidden",children:u},l=t.mode),a=Ur(a,l,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(l=t.child).memoizedState=Fo(n),l.childLanes=Uo(e,r,n),t.memoizedState=$o,a):(lo(t),Bo(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(o)256&t.flags?(lo(t),t.flags&=-257,t=qo(e,t,n)):null!==t.memoizedState?(oo(),t.child=e.child,t.flags|=128,t=null):(oo(),l=a.fallback,u=t.mode,a=Wo({mode:"visible",children:a.children},u),(l=Ur(l,u,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,to(t,e.child,null,n),(a=t.child).memoizedState=Fo(n),a.childLanes=Uo(e,r,n),t.memoizedState=$o,t=l);else if(lo(t),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(i(419))).stack="",a.digest=r,ma({value:a,source:null,stack:null}),t=qo(e,t,n)}else if(_o||Sa(e,t,n,!1),r=0!==(n&e.childLanes),_o||r){if(null!==(r=ru)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:je(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,zr(e,a),Mu(r,e,a),Co;"$?"===u.data||Qu(),t=qo(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=yd(u.nextSibling),ra=t,la=!0,ia=null,oa=!1,null!==e&&(Gr[Kr++]=Xr,Gr[Kr++]=Zr,Gr[Kr++]=Yr,Xr=e.id,Zr=e.overflow,Yr=t),(t=Bo(t,a.children)).flags|=4096);return t}return l?(oo(),l=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Ir(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?l=Ir(c,l):(l=Ur(l,u,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(u=e.child.memoizedState)?u=Fo(n):(null!==(s=u.cachePool)?(c=Ra._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Wa(),u={baseLanes:u.baseLanes|n,cachePool:s}),l.memoizedState=u,l.childLanes=Uo(e,r,n),t.memoizedState=$o,a):(lo(t),e=(n=e.child).sibling,(n=Ir(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Bo(e,t){return(t=Wo({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Wo(e,t){return(e=Mr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qo(e,t,n){return to(t,e.child,null,n),(e=Bo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wa(e.return,t,n)}function Qo(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Go(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(jo(e,t,r.children,n),0!==(2&(r=uo.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vo(e,n,t);else if(19===e.tag)Vo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(U(uo,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===co(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qo(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===co(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qo(t,!0,n,null,l);break;case"together":Qo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ko(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Ir(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ir(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Yo(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ea(e))}function Xo(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)_o=!0;else{if(!Yo(e,n)&&0===(128&t.flags))return _o=!1,function(e,t,n){switch(t.tag){case 3:V(t,t.stateNode.containerInfo),ba(0,Ra,e.memoizedState.cache),pa();break;case 27:case 5:G(t);break;case 4:V(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(lo(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Ho(e,t,n):(lo(t),null!==(e=Ko(e,t,n))?e.sibling:null);lo(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Go(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),U(uo,uo.current),r)break;return null;case 22:case 23:return t.lanes=0,Ro(e,t,n);case 24:ba(0,Ra,e.memoizedState.cache)}return Ko(e,t,n)}(e,t,n);_o=0!==(131072&e.flags)}else _o=!1,la&&0!==(1048576&t.flags)&&ea(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===x){t.tag=11,t=To(null,t,r,e,n);break e}if(a===N){t.tag=14,t=Po(null,t,r,e,n);break e}}throw t=R(r)||r,Error(i(306,t,""))}Dr(r)?(e=go(r,e),t.tag=1,t=Do(null,t,r,e,n)):(t.tag=0,t=Ao(null,t,r,e,n))}return t;case 0:return Ao(e,t,t.type,t.pendingProps,n);case 1:return Do(e,t,r=t.type,a=go(r,t.pendingProps),n);case 3:e:{if(V(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,rl(e,t),cl(t,r,null,n);var o=t.memoizedState;if(r=o.cache,ba(0,Ra,r),r!==l.cache&&xa(t,[Ra],n,!0),ul(),r=o.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Io(e,t,r,n);break e}if(r!==a){ma(a=Er(Error(i(424)),t)),t=Io(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,la=!0,ia=null,oa=!0,n=no(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Ko(e,t,n);break e}jo(e,t,r,n)}t=t.child}return t;case 26:return Oo(e,t),null===e?(n=jd(t.type,null,t.pendingProps,null))?t.memoizedState=n:la||(n=t.type,e=t.pendingProps,(r=rd(W.current).createElement(n))[Re]=t,r[Le]=e,ed(r,n,e),qe(r),t.stateNode=r):t.memoizedState=jd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return G(t),null===e&&la&&(r=t.stateNode=kd(t.type,t.pendingProps,W.current),ra=t,oa=!0,a=aa,pd(t.type)?(vd=a,aa=yd(r.firstChild)):aa=a),jo(e,t,t.pendingProps.children,n),Oo(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&la&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[$e])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,oa))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),oa=!1,a=!0):a=!1),a||ua(t)),G(t),a=t.type,l=t.pendingProps,o=null!==e?e.memoizedProps:null,r=l.children,id(a,l)?r=null:null!==o&&id(a,o)&&(t.flags|=32),null!==t.memoizedState&&(a=zl(e,t,Ol,null,null,n),Gd._currentValue=a),Oo(e,t),jo(e,t,r,n),t.child;case 6:return null===e&&la&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,oa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return Ho(e,t,n);case 4:return V(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=to(t,null,r,n):jo(e,t,r,n),t.child;case 11:return To(e,t,t.type,t.pendingProps,n);case 7:return jo(e,t,t.pendingProps,n),t.child;case 8:case 12:return jo(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),jo(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Na(t),r=r(a=Ca(a)),t.flags|=1,jo(e,t,r,n),t.child;case 14:return Po(e,t,t.type,t.pendingProps,n);case 15:return zo(e,t,t.type,t.pendingProps,n);case 19:return Go(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Wo(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Ir(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ro(e,t,n);case 24:return Na(t),r=Ca(Ra),null===e?(null===(a=Ha())&&(a=ru,l=La(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},nl(t),ba(0,Ra,a)):(0!==(e.lanes&n)&&(rl(e,t),cl(t,null,null,n),ul()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Ra,r)):(r=l.cache,ba(0,Ra,r),r!==a.cache&&xa(t,[Ra],n,!0))),jo(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Zo(e){e.flags|=4}function Jo(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Ud(t)){if(null!==(t=ro.current)&&((4194048&lu)===lu?null!==ao:(62914560&lu)!==lu&&0===(536870912&lu)||t!==ao))throw Za=Ga,Va;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,yu|=t)}function ts(e,t){if(!la)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),ka(Ra),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Zo(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,ha())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Zo(t),null!==n?(ns(t),Jo(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zo(t),ns(t),Jo(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Zo(t),ns(t),t.flags&=-16777217),null;case 27:K(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zo(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}e=H.current,fa(t)?ca(t):(e=kd(a,r,n),t.stateNode=e,Zo(t))}return ns(t),null;case 5:if(K(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zo(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}if(e=H.current,fa(t))ca(t);else{switch(a=rd(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Re]=t,e[Le]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zo(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Zo(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=W.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Yc(e.nodeValue,n)))||ua(t)}else(e=rd(e).createTextNode(r))[Re]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[Re]=t}else pa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),a=!1}else a=ha(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(so(t),t):(so(t),null)}if(so(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return Q(),null===e&&Fc(t.stateNode.containerInfo),ns(t),null;case 10:return ka(t.type),ns(t),null;case 19:if(F(uo),null===(a=t.memoizedState))return ns(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)ts(a,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=co(e))){for(t.flags|=128,ts(a,!1),e=l.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)$r(n,e),n=n.sibling;return U(uo,1&uo.current|2),t.child}e=e.sibling}null!==a.tail&&te()>xu&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=co(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!la)return ns(t),null}else 2*te()-a.renderingStartTime>xu&&536870912!==n&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=uo.current,U(uo,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return so(t),yl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&F(Ua),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ka(Ra),ns(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function as(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ka(Ra),Q(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return K(t),null;case 13:if(so(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return F(uo),null;case 4:return Q(),null;case 10:return ka(t.type),null;case 22:case 23:return so(t),yl(),null!==e&&F(Ua),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ka(Ra),null;default:return null}}function ls(e,t){switch(na(t),t.tag){case 3:ka(Ra),Q();break;case 26:case 27:case 5:K(t);break;case 4:Q();break;case 13:so(t);break;case 19:F(uo);break;case 10:ka(t.type);break;case 22:case 23:so(t),yl(),null!==e&&F(Ua);break;case 24:ka(Ra)}}function is(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,i=n.inst;r=l(),i.destroy=r}n=n.next}while(n!==a)}}catch(o){cc(t,t.return,o)}}function os(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var i=r.inst,o=i.destroy;if(void 0!==o){i.destroy=void 0,a=t;var s=n,u=o;try{u()}catch(c){cc(a,s,c)}}}r=r.next}while(r!==l)}}catch(c){cc(t,t.return,c)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fl(t,n)}catch(r){cc(e,e.return,r)}}}function us(e,t,n){n.props=go(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){cc(e,t,a)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){cc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(l){cc(e,t,l)}else n.current=null}function fs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){cc(e,e.return,a)}}function ps(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,o=null,s=null,u=null,c=null,d=null;for(h in n){var f=n[h];if(n.hasOwnProperty(h)&&null!=f)switch(h){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(h)||Zc(e,t,h,null,r,f)}}for(var p in r){var h=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=h||null!=f))switch(p){case"type":l=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":d=h;break;case"value":o=h;break;case"defaultValue":s=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(i(137,t));break;default:h!==f&&Zc(e,t,p,h,r,f)}}return void gt(e,o,s,u,c,d,l,a);case"select":for(l in h=o=s=p=null,n)if(u=n[l],n.hasOwnProperty(l)&&null!=u)switch(l){case"value":break;case"multiple":h=u;default:r.hasOwnProperty(l)||Zc(e,t,l,null,r,u)}for(a in r)if(l=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=l||null!=u))switch(a){case"value":p=l;break;case"defaultValue":s=l;break;case"multiple":o=l;default:l!==u&&Zc(e,t,a,l,r,u)}return t=s,n=o,r=h,void(null!=p?bt(e,!!n,p,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(s in h=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Zc(e,t,s,null,r,a)}for(o in r)if(a=r[o],l=n[o],r.hasOwnProperty(o)&&(null!=a||null!=l))switch(o){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==l&&Zc(e,t,o,a,r,l)}return void kt(e,p,h);case"option":for(var m in n)if(p=n[m],n.hasOwnProperty(m)&&null!=p&&!r.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Zc(e,t,m,null,r,p);for(u in r)if(p=r[u],h=n[u],r.hasOwnProperty(u)&&p!==h&&(null!=p||null!=h))if("selected"===u)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Zc(e,t,u,p,r,h);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Zc(e,t,g,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:Zc(e,t,c,p,r,h)}return;default:if(Ct(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Jc(e,t,y,void 0,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||void 0===p&&void 0===h||Jc(e,t,d,p,r,h);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Zc(e,t,v,null,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||null==p&&null==h||Zc(e,t,f,p,r,h)}(r,e.type,n,t),r[Le]=t}catch(a){cc(e,e.return,a)}}function hs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function ms(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xc));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function vs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Re]=e,t[Le]=n}catch(l){cc(e,e.return,l)}}var bs=!1,ks=!1,ws=!1,xs="function"===typeof WeakSet?WeakSet:Set,Ss=null;function Es(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Ds(e,n),4&r&&is(5,n);break;case 1:if(Ds(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){cc(n,n.return,i)}else{var a=go(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){cc(n,n.return,o)}}64&r&&ss(n),512&r&&cs(n,n.return);break;case 3:if(Ds(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fl(e,t)}catch(i){cc(n,n.return,i)}}break;case 27:null===t&&4&r&&vs(n);case 26:case 5:Ds(e,n),null===t&&4&r&&fs(n),512&r&&cs(n,n.return);break;case 12:Ds(e,n);break;case 13:Ds(e,n),4&r&&Ps(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bs)){t=null!==t&&null!==t.memoizedState||ks,a=bs;var l=ks;bs=r,(ks=t)&&!l?$s(e,n,0!==(8772&n.subtreeFlags)):Ds(e,n),bs=a,ks=l}break;case 30:break;default:Ds(e,n)}}function Ns(e){var t=e.alternate;null!==t&&(e.alternate=null,Ns(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Fe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cs=null,_s=!1;function js(e,t,n){for(n=n.child;null!==n;)Ts(e,t,n),n=n.sibling}function Ts(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(l){}switch(n.tag){case 26:ks||ds(n,t),js(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ks||ds(n,t);var r=Cs,a=_s;pd(n.type)&&(Cs=n.stateNode,_s=!1),js(e,t,n),wd(n.stateNode),Cs=r,_s=a;break;case 5:ks||ds(n,t);case 6:if(r=Cs,a=_s,Cs=null,js(e,t,n),_s=a,null!==(Cs=r))if(_s)try{(9===Cs.nodeType?Cs.body:"HTML"===Cs.nodeName?Cs.ownerDocument.body:Cs).removeChild(n.stateNode)}catch(i){cc(n,t,i)}else try{Cs.removeChild(n.stateNode)}catch(i){cc(n,t,i)}break;case 18:null!==Cs&&(_s?(hd(9===(e=Cs).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),jf(e)):hd(Cs,n.stateNode));break;case 4:r=Cs,a=_s,Cs=n.stateNode.containerInfo,_s=!0,js(e,t,n),Cs=r,_s=a;break;case 0:case 11:case 14:case 15:ks||os(2,n,t),ks||os(4,n,t),js(e,t,n);break;case 1:ks||(ds(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&us(n,t,r)),js(e,t,n);break;case 21:js(e,t,n);break;case 22:ks=(r=ks)||null!==n.memoizedState,js(e,t,n),ks=r;break;default:js(e,t,n)}}function Ps(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{jf(e)}catch(n){cc(t,t.return,n)}}function zs(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new xs),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new xs),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=mc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Rs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){Cs=s.stateNode,_s=!1;break e}break;case 5:Cs=s.stateNode,_s=!1;break e;case 3:case 4:Cs=s.stateNode.containerInfo,_s=!0;break e}s=s.return}if(null===Cs)throw Error(i(160));Ts(l,o,a),Cs=null,_s=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Os(t,e),t=t.sibling}var Ls=null;function Os(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Rs(t,e),As(e),4&r&&(os(3,e,e.return),is(3,e),os(5,e,e.return));break;case 1:Rs(t,e),As(e),512&r&&(ks||null===n||ds(n,n.return)),64&r&&bs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Ls;if(Rs(t,e),As(e),512&r&&(ks||null===n||ds(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[$e]||l[Re]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),ed(l,r,n),l[Re]=e,qe(l),r=l;break e;case"link":var o=$d("link","href",a).get(r+(n.href||""));if(o)for(var s=0;s<o.length;s++)if((l=o[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){o.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(o=$d("meta","content",a).get(r+(n.content||"")))for(s=0;s<o.length;s++)if((l=o[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){o.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(i(468,r))}l[Re]=e,qe(l),r=l}e.stateNode=r}else Fd(a,e.type,e.stateNode);else e.stateNode=Od(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?Fd(a,e.type,e.stateNode):Od(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,n.memoizedProps)}break;case 27:Rs(t,e),As(e),512&r&&(ks||null===n||ds(n,n.return)),null!==n&&4&r&&ps(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Rs(t,e),As(e),512&r&&(ks||null===n||ds(n,n.return)),32&e.flags){a=e.stateNode;try{xt(a,"")}catch(h){cc(e,e.return,h)}}4&r&&null!=e.stateNode&&ps(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(ws=!0);break;case 6:if(Rs(t,e),As(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){cc(e,e.return,h)}}break;case 3:if(Id=null,a=Ls,Ls=Ed(t.containerInfo),Rs(t,e),Ls=a,As(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{jf(t.containerInfo)}catch(h){cc(e,e.return,h)}ws&&(ws=!1,Ms(e));break;case 4:r=Ls,Ls=Ed(e.stateNode.containerInfo),Rs(t,e),As(e),Ls=r;break;case 12:default:Rs(t,e),As(e);break;case 13:Rs(t,e),As(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(wu=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,zs(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bs,d=ks;if(bs=c||a,ks=d||u,Rs(t,e),ks=d,bs=c,As(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||bs||ks||Is(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(l=u.stateNode,a)"function"===typeof(o=l.style).setProperty?o.setProperty("display","none","important"):o.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(h){cc(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(h){cc(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,zs(e,n))));break;case 19:Rs(t,e),As(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,zs(e,r)));case 30:case 21:}}function As(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hs(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,ms(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(xt(l,""),n.flags&=-33),ys(e,ms(e),l);break;case 3:case 4:var o=n.stateNode.containerInfo;gs(e,ms(e),o);break;default:throw Error(i(161))}}catch(s){cc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ms(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ms(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Ds(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Es(e,t.alternate,t),t=t.sibling}function Is(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:os(4,t,t.return),Is(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&us(t,t.return,n),Is(t);break;case 27:wd(t.stateNode);case 26:case 5:ds(t,t.return),Is(t);break;case 22:null===t.memoizedState&&Is(t);break;default:Is(t)}e=e.sibling}}function $s(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,i=l.flags;switch(l.tag){case 0:case 11:case 15:$s(a,l,n),is(4,l);break;case 1:if($s(a,l,n),"function"===typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(a=(r=l).updateQueue)){var o=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)dl(s[a],o)}catch(u){cc(r,r.return,u)}}n&&64&i&&ss(l),cs(l,l.return);break;case 27:vs(l);case 26:case 5:$s(a,l,n),n&&null===r&&4&i&&fs(l),cs(l,l.return);break;case 12:$s(a,l,n);break;case 13:$s(a,l,n),n&&4&i&&Ps(a,l);break;case 22:null===l.memoizedState&&$s(a,l,n),cs(l,l.return);break;case 30:break;default:$s(a,l,n)}t=t.sibling}}function Fs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Oa(n))}function Us(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Oa(e))}function Hs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Bs(e,t,n,r),t=t.sibling}function Bs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Hs(e,t,n,r),2048&a&&is(9,t);break;case 1:case 13:default:Hs(e,t,n,r);break;case 3:Hs(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Oa(e)));break;case 12:if(2048&a){Hs(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,i=l.id,o=l.onPostCommit;"function"===typeof o&&o(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){cc(t,t.return,s)}}else Hs(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,i=t.alternate,null!==t.memoizedState?2&l._visibility?Hs(e,t,n,r):qs(e,t):2&l._visibility?Hs(e,t,n,r):(l._visibility|=2,Ws(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Fs(i,t);break;case 24:Hs(e,t,n,r),2048&a&&Us(t.alternate,t)}}function Ws(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,i=t,o=n,s=r,u=i.flags;switch(i.tag){case 0:case 11:case 15:Ws(l,i,o,s,a),is(8,i);break;case 23:break;case 22:var c=i.stateNode;null!==i.memoizedState?2&c._visibility?Ws(l,i,o,s,a):qs(l,i):(c._visibility|=2,Ws(l,i,o,s,a)),a&&2048&u&&Fs(i.alternate,i);break;case 24:Ws(l,i,o,s,a),a&&2048&u&&Us(i.alternate,i);break;default:Ws(l,i,o,s,a)}t=t.sibling}}function qs(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:qs(n,r),2048&a&&Fs(r.alternate,r);break;case 24:qs(n,r),2048&a&&Us(r.alternate,r);break;default:qs(n,r)}t=t.sibling}}var Vs=8192;function Qs(e){if(e.subtreeFlags&Vs)for(e=e.child;null!==e;)Gs(e),e=e.sibling}function Gs(e){switch(e.tag){case 26:Qs(e),e.flags&Vs&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(i(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Td(n.href),l=e.querySelector(Pd(a));if(l)return null!==(e=l._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Wd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void qe(l);l=e.ownerDocument||e,n=zd(n),(a=xd.get(a))&&Md(n,a),qe(l=l.createElement("link"));var o=l;o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),ed(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Wd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ls,e.memoizedState,e.memoizedProps);break;case 5:default:Qs(e);break;case 3:case 4:var t=Ls;Ls=Ed(e.stateNode.containerInfo),Qs(e),Ls=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Vs,Vs=16777216,Qs(e),Vs=t):Qs(e))}}function Ks(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ys(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Js(r,e)}Ks(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xs(e),e=e.sibling}function Xs(e){switch(e.tag){case 0:case 11:case 15:Ys(e),2048&e.flags&&os(9,e,e.return);break;case 3:case 12:default:Ys(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Zs(e)):Ys(e)}}function Zs(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Js(r,e)}Ks(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:os(8,t,t.return),Zs(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Zs(t));break;default:Zs(t)}e=e.sibling}}function Js(e,t){for(;null!==Ss;){var n=Ss;switch(n.tag){case 0:case 11:case 15:os(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Oa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ss=r;else e:for(n=e;null!==Ss;){var a=(r=Ss).sibling,l=r.return;if(Ns(r),r===n){Ss=null;break e}if(null!==a){a.return=l,Ss=a;break e}Ss=l}}}var eu={getCacheForType:function(e){var t=Ca(Ra),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,au=null,lu=0,iu=0,ou=null,su=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,hu=0,mu=0,gu=0,yu=0,vu=null,bu=null,ku=!1,wu=0,xu=1/0,Su=null,Eu=null,Nu=0,Cu=null,_u=null,ju=0,Tu=0,Pu=null,zu=null,Ru=0,Lu=null;function Ou(){if(0!==(2&nu)&&0!==lu)return lu&-lu;if(null!==O.T){return 0!==Da?Da:Tc()}return Pe()}function Au(){0===gu&&(gu=0===(536870912&lu)||la?xe():536870912);var e=ro.current;return null!==e&&(e.flags|=32),gu}function Mu(e,t,n){(e!==ru||2!==iu&&9!==iu)&&null===e.cancelPendingCommit||(Bu(e,0),Fu(e,lu,gu,!1)),Ne(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(hu|=n),4===fu&&Fu(e,lu,gu,!1)),xc(e))}function Du(e,t,n){if(0!==(6&nu))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||ke(e,t),a=r?function(e,t){var n=nu;nu|=2;var r=qu(),a=Vu();ru!==e||lu!==t?(Su=null,xu=te()+500,Bu(e,t)):uu=ke(e,t);e:for(;;)try{if(0!==iu&&null!==au){t=au;var l=ou;t:switch(iu){case 1:iu=0,ou=null,Ju(e,t,l,1);break;case 2:case 9:if(Ka(l)){iu=0,ou=null,Zu(t);break}t=function(){2!==iu&&9!==iu||ru!==e||(iu=7),xc(e)},l.then(t,t);break e;case 3:iu=7;break e;case 4:iu=5;break e;case 7:Ka(l)?(iu=0,ou=null,Zu(t)):(iu=0,ou=null,Ju(e,t,l,7));break;case 5:var o=null;switch(au.tag){case 26:o=au.memoizedState;case 5:case 27:var s=au;if(!o||Ud(o)){iu=0,ou=null;var u=s.sibling;if(null!==u)au=u;else{var c=s.return;null!==c?(au=c,ec(c)):au=null}break t}}iu=0,ou=null,Ju(e,t,l,5);break;case 6:iu=0,ou=null,Ju(e,t,l,6);break;case 8:Hu(),fu=6;break e;default:throw Error(i(462))}}Yu();break}catch(d){Wu(e,d)}return va=ya=null,O.H=r,O.A=a,nu=n,null!==au?0:(ru=null,lu=0,jr(),fu)}(e,t):Gu(e,t,!0),l=r;;){if(0===a){uu&&!r&&Fu(e,t,0,!1);break}if(n=e.current.alternate,!l||$u(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var o=0;else o=0!==(o=-536870913&e.pendingLanes)?o:536870912&o?536870912:0;if(0!==o){t=o;e:{var s=e;a=vu;var u=s.current.memoizedState.isDehydrated;if(u&&(Bu(s,o).flags|=256),2!==(o=Gu(s,o,!1))){if(cu&&!u){s.errorRecoveryDisabledLanes|=l,hu|=l,a=4;break e}l=bu,bu=a,null!==l&&(null===bu?bu=l:bu.push.apply(bu,l))}a=o}if(l=!1,2!==a)continue}}if(1===a){Bu(e,0),Fu(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Fu(r,t,gu,!su);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=wu+300-te())){if(Fu(r,t,gu,!su),0!==be(r,0,!0))break e;r.timeoutHandle=sd(Iu.bind(null,r,n,bu,Su,ku,t,gu,hu,yu,su,l,2,-0,0),a)}else Iu(r,n,bu,Su,ku,t,gu,hu,yu,su,l,0,-0,0)}break}a=Gu(e,t,!1),l=!1}xc(e)}function Iu(e,t,n,r,a,l,o,s,u,c,d,f,p,h){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Hd={stylesheets:null,count:0,unsuspend:Bd},Gs(t),null!==(f=function(){if(null===Hd)throw Error(i(475));var e=Hd;return e.stylesheets&&0===e.count&&Vd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Vd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,l,n,r,a,o,s,u,d,1,p,h)),void Fu(e,l,o,!c);nc(e,t,l,n,r,a,o,s,u)}function $u(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Kn(l(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Fu(e,t,n,r){t&=~mu,t&=~hu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-pe(a),i=1<<l;r[l]=-1,a&=~i}0!==n&&Ce(e,n,t)}function Uu(){return 0!==(6&nu)||(Sc(0,!1),!1)}function Hu(){if(null!==au){if(0===iu)var e=au.return;else va=ya=null,Dl(e=au),Gi=null,Ki=0,e=au;for(;null!==e;)ls(e.alternate,e),e=e.return;au=null}}function Bu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hu(),ru=e,au=n=Ir(e.current,null),lu=t,iu=0,ou=null,su=!1,uu=ke(e,t),cu=!1,yu=gu=mu=hu=pu=fu=0,bu=vu=null,ku=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),l=1<<a;t|=e[a],r&=~l}return du=t,jr(),n}function Wu(e,t){bl=null,O.H=Wi,t===qa||t===Qa?(t=Ja(),iu=3):t===Va?(t=Ja(),iu=4):iu=t===Co?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,ou=t,null===au&&(fu=1,wo(e,Er(t,e.current)))}function qu(){var e=O.H;return O.H=Wi,null===e?Wi:e}function Vu(){var e=O.A;return O.A=eu,e}function Qu(){fu=4,su||(4194048&lu)!==lu&&null!==ro.current||(uu=!0),0===(134217727&pu)&&0===(134217727&hu)||null===ru||Fu(ru,lu,gu,!1)}function Gu(e,t,n){var r=nu;nu|=2;var a=qu(),l=Vu();ru===e&&lu===t||(Su=null,Bu(e,t)),t=!1;var i=fu;e:for(;;)try{if(0!==iu&&null!==au){var o=au,s=ou;switch(iu){case 8:Hu(),i=6;break e;case 3:case 2:case 9:case 6:null===ro.current&&(t=!0);var u=iu;if(iu=0,ou=null,Ju(e,o,s,u),n&&uu){i=0;break e}break;default:u=iu,iu=0,ou=null,Ju(e,o,s,u)}}Ku(),i=fu;break}catch(c){Wu(e,c)}return t&&e.shellSuspendCounter++,va=ya=null,nu=r,O.H=a,O.A=l,null===au&&(ru=null,lu=0,jr()),i}function Ku(){for(;null!==au;)Xu(au)}function Yu(){for(;null!==au&&!J();)Xu(au)}function Xu(e){var t=Xo(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Zu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Mo(n,t,t.pendingProps,t.type,void 0,lu);break;case 11:t=Mo(n,t,t.pendingProps,t.type.render,t.ref,lu);break;case 5:Dl(t);default:ls(n,t),t=Xo(n,t=au=$r(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Ju(e,t,n,r){va=ya=null,Dl(t),Gi=null,Ki=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=ro.current)){switch(n.tag){case 13:return null===ao?Qu():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ga?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,a)),!1;case 22:return n.flags|=65536,r===Ga?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,a)),!1}throw Error(i(435,n.tag))}return dc(e,r,a),Qu(),!1}if(la)return null!==(t=ro.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ma(Er(e=Error(i(422),{cause:r}),n))):(r!==sa&&ma(Er(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Er(r,n),ol(e,a=So(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var l=Error(i(520),{cause:r});if(l=Er(l,n),null===vu?vu=[l]:vu.push(l),4!==fu&&(fu=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ol(n,e=So(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==l&&"function"===typeof l.componentDidCatch&&(null===Eu||!Eu.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,No(a=Eo(a),e,n,r),ol(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,lu))return fu=1,wo(e,Er(n,e.current)),void(au=null)}catch(l){if(null!==a)throw au=a,l;return fu=1,wo(e,Er(n,e.current)),void(au=null)}32768&t.flags?(la||1===r?e=!0:uu||0!==(536870912&lu)?e=!1:(su=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ro.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,su);e=t.return;var n=rs(t.alternate,t,du);if(null!==n)return void(au=n);if(null!==(t=t.sibling))return void(au=t);au=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=as(e.alternate,e);if(null!==n)return n.flags&=32767,void(au=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(au=e);au=e=n}while(null!==e);fu=6,au=null}function nc(e,t,n,r,a,l,o,s,u){e.cancelPendingCommit=null;do{oc()}while(0!==Nu);if(0!==(6&nu))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var o=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var c=31-pe(n),d=1<<c;o[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ce(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(i&~t))}(e,n,l|=_r,o,s,u),e===ru&&(au=ru=null,lu=0),_u=t,Cu=e,ju=n,Tu=l,Pu=a,zu=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,X(le,function(){return sc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=O.T,O.T=null,a=A.p,A.p=2,o=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(g){n=null;break e}var o=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=o+a),f!==l||0!==r&&3!==f.nodeType||(u=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=o),p===l&&++d===r&&(u=o),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Ss=t;null!==Ss;)if(e=(t=Ss).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Ss=e;else for(;null!==Ss;){switch(l=(t=Ss).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var m=go(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(m,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){cc(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))md(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Ss=e;break}Ss=t.return}}(e,t)}finally{nu=o,A.p=a,O.T=r}}Nu=1,rc(),ac(),lc()}}function rc(){if(1===Nu){Nu=0;var e=Cu,t=_u,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=O.T,O.T=null;var r=A.p;A.p=2;var a=nu;nu|=4;try{Os(t,e);var l=nd,i=er(e.containerInfo),o=l.focusedElem,s=l.selectionRange;if(i!==o&&o&&o.ownerDocument&&Jn(o.ownerDocument.documentElement,o)){if(null!==s&&tr(o)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in o)o.selectionStart=u,o.selectionEnd=Math.min(c,o.value.length);else{var d=o.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),h=o.textContent.length,m=Math.min(s.start,h),g=void 0===s.end?m:Math.min(s.end,h);!p.extend&&m>g&&(i=g,g=m,m=i);var y=Zn(o,m),v=Zn(o,g);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),m>g?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=o;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof o.focus&&o.focus(),o=0;o<d.length;o++){var k=d[o];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}nf=!!td,nd=td=null}finally{nu=a,A.p=r,O.T=n}}e.current=t,Nu=2}}function ac(){if(2===Nu){Nu=0;var e=Cu,t=_u,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=O.T,O.T=null;var r=A.p;A.p=2;var a=nu;nu|=4;try{Es(e,t.alternate,t)}finally{nu=a,A.p=r,O.T=n}}Nu=3}}function lc(){if(4===Nu||3===Nu){Nu=0,ee();var e=Cu,t=_u,n=ju,r=zu;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Nu=5:(Nu=0,_u=Cu=null,ic(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Eu=null),Te(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=O.T,a=A.p,A.p=2,O.T=null;try{for(var l=e.onRecoverableError,i=0;i<r.length;i++){var o=r[i];l(o.value,{componentStack:o.stack})}}finally{O.T=t,A.p=a}}0!==(3&ju)&&oc(),xc(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Lu?Ru++:(Ru=0,Lu=e):Ru=0,Sc(0,!1)}}function ic(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Oa(t)))}function oc(e){return rc(),ac(),lc(),sc()}function sc(){if(5!==Nu)return!1;var e=Cu,t=Tu;Tu=0;var n=Te(ju),r=O.T,a=A.p;try{A.p=32>n?32:n,O.T=null,n=Pu,Pu=null;var l=Cu,o=ju;if(Nu=0,_u=Cu=null,ju=0,0!==(6&nu))throw Error(i(331));var s=nu;if(nu|=4,Xs(l.current),Bs(l,l.current,o,n),nu=s,Sc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,l)}catch(u){}return!0}finally{A.p=a,O.T=r,ic(e,t)}}function uc(e,t,n){t=Er(n,t),null!==(e=ll(e,t=So(e.stateNode,t,2),2))&&(Ne(e,2),xc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=Er(n,e),null!==(r=ll(t,n=Eo(2),2))&&(No(n,r,t,e),Ne(r,2),xc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cu=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(lu&n)===n&&(4===fu||3===fu&&(62914560&lu)===lu&&300>te()-wu?0===(2&nu)&&Bu(e,0):mu|=n,yu===lu&&(yu=0)),xc(e)}function pc(e,t){0===t&&(t=Se()),null!==(e=zr(e,t))&&(Ne(e,t),xc(e))}function hc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function mc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),pc(e,n)}var gc=null,yc=null,vc=!1,bc=!1,kc=!1,wc=0;function xc(e){e!==yc&&null===e.next&&(null===yc?gc=yc=e:yc=yc.next=e),bc=!0,vc||(vc=!0,dd(function(){0!==(6&nu)?X(re,Ec):Nc()}))}function Sc(e,t){if(!kc&&bc){kc=!0;do{for(var n=!1,r=gc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var i=r.suspendedLanes,o=r.pingedLanes;l=(1<<31-pe(42|e)+1)-1,l=201326741&(l&=a&~(i&~o))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,jc(r,l))}else l=lu,0===(3&(l=be(r,r===ru?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||ke(r,l)||(n=!0,jc(r,l));r=r.next}}while(n);kc=!1}}function Ec(){Nc()}function Nc(){bc=vc=!1;var e=0;0!==wc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==od&&(od=e,!0);return od=null,!1}()&&(e=wc),wc=0);for(var t=te(),n=null,r=gc;null!==r;){var a=r.next,l=Cc(r,t);0===l?(r.next=null,null===n?gc=a:n.next=a,null===a&&(yc=n)):(n=r,(0!==e||0!==(3&l))&&(bc=!0)),r=a}Sc(e,!1)}function Cc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var i=31-pe(l),o=1<<i,s=a[i];-1===s?0!==(o&n)&&0===(o&r)||(a[i]=we(o,t)):s<=t&&(e.expiredLanes|=o),l&=~o}if(n=lu,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===iu||9===iu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Z(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||ke(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&Z(r),Te(n)){case 2:case 8:n=ae;break;case 32:default:n=le;break;case 268435456:n=oe}return r=_c.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&Z(r),e.callbackPriority=2,e.callbackNode=null,2}function _c(e,t){if(0!==Nu&&5!==Nu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(oc()&&e.callbackNode!==n)return null;var r=lu;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Du(e,r,t),Cc(e,te()),null!=e.callbackNode&&e.callbackNode===n?_c.bind(null,e):null)}function jc(e,t){if(oc())return null;Du(e,t,!0)}function Tc(){return 0===wc&&(wc=xe()),wc}function Pc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Tt(""+e)}function zc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Rc=0;Rc<wr.length;Rc++){var Lc=wr[Rc];xr(Lc.toLowerCase(),"on"+(Lc[0].toUpperCase()+Lc.slice(1)))}xr(pr,"onAnimationEnd"),xr(hr,"onAnimationIteration"),xr(mr,"onAnimationStart"),xr("dblclick","onDoubleClick"),xr("focusin","onFocus"),xr("focusout","onBlur"),xr(gr,"onTransitionRun"),xr(yr,"onTransitionStart"),xr(vr,"onTransitionCancel"),xr(br,"onTransitionEnd"),Ke("onMouseEnter",["mouseout","mouseover"]),Ke("onMouseLeave",["mouseout","mouseover"]),Ke("onPointerEnter",["pointerout","pointerover"]),Ke("onPointerLeave",["pointerout","pointerover"]),Ge("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ge("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ge("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ge("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ge("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Oc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ac=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Oc));function Mc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],s=o.instance,u=o.currentTarget;if(o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=u;try{l(a)}catch(c){yo(c)}a.currentTarget=null,l=s}else for(i=0;i<r.length;i++){if(s=(o=r[i]).instance,u=o.currentTarget,o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=u;try{l(a)}catch(c){yo(c)}a.currentTarget=null,l=s}}}}function Dc(e,t){var n=t[Ae];void 0===n&&(n=t[Ae]=new Set);var r=e+"__bubble";n.has(r)||(Uc(t,e,2,!1),n.add(r))}function Ic(e,t,n){var r=0;t&&(r|=4),Uc(n,e,r,t)}var $c="_reactListening"+Math.random().toString(36).slice(2);function Fc(e){if(!e[$c]){e[$c]=!0,Ve.forEach(function(t){"selectionchange"!==t&&(Ac.has(t)||Ic(t,!1,e),Ic(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$c]||(t[$c]=!0,Ic("selectionchange",!1,t))}}function Uc(e,t,n,r){switch(cf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=lf}n=a.bind(null,t,n,e),a=void 0,!$t||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hc(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=Ue(o)))return;if(5===(u=i.tag)||6===u||26===u||27===u){r=l=i;continue e}o=o.parentNode}}r=r.return}Mt(function(){var r=l,a=zt(n),i=[];e:{var o=kr.get(e);if(void 0!==o){var u=Jt,c=e;switch(e){case"keypress":if(0===qt(n))break e;case"keydown":case"keyup":u=mn;break;case"focusin":c="focus",u=ln;break;case"focusout":c="blur",u=ln;break;case"beforeblur":case"afterblur":u=ln;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case pr:case hr:case mr:u=on;break;case br:u=vn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gn;break;case"toggle":case"beforetoggle":u=kn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==o?o+"Capture":null:o;d=[];for(var h,m=r;null!==m;){var g=m;if(h=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===h||null===p||null!=(g=Dt(m,p))&&d.push(Bc(m,g,h)),f)break;m=m.return}0<d.length&&(o=new u(o,c,null,n,a),i.push({event:o,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===Pt||!(c=n.relatedTarget||n.fromElement)||!Ue(c)&&!c[Oe])&&(u||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Ue(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,g="onMouseLeave",p="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",m="pointer"),f=null==u?o:Be(u),h=null==c?o:Be(c),(o=new d(g,m+"leave",u,n,a)).target=f,o.relatedTarget=h,g=null,Ue(a)===r&&((d=new d(p,m+"enter",c,n,a)).target=h,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,m=0,h=d=u;h;h=qc(h))m++;for(h=0,g=p;g;g=qc(g))h++;for(;0<m-h;)d=qc(d),m--;for(;0<h-m;)p=qc(p),h--;for(;m--;){if(d===p||null!==p&&d===p.alternate)break e;d=qc(d),p=qc(p)}d=null}else d=null;null!==u&&Vc(i,o,u,d,!1),null!==c&&null!==f&&Vc(i,f,c,d,!0)}if("select"===(u=(o=r?Be(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===u&&"file"===o.type)var y=In;else if(Rn(o))if($n)y=Gn;else{y=Vn;var v=qn}else!(u=o.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&Ct(r.elementType)&&(y=In):y=Qn;switch(y&&(y=y(e,r))?Ln(i,y,n,a):(v&&v(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&vt(o,"number",o.value)),v=r?Be(r):window,e){case"focusin":(Rn(v)||"true"===v.contentEditable)&&(rr=v,ar=r,lr=null);break;case"focusout":lr=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,or(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":or(i,n,a)}var b;if(xn)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Pn?jn(e,n)&&(k="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(k="onCompositionStart");k&&(Nn&&"ko"!==n.locale&&(Pn||"onCompositionStart"!==k?"onCompositionEnd"===k&&Pn&&(b=Wt()):(Ht="value"in(Ut=a)?Ut.value:Ut.textContent,Pn=!0)),0<(v=Wc(r,k)).length&&(k=new un(k,e,null,n,a),i.push({event:k,listeners:v}),b?k.data=b:null!==(b=Tn(n))&&(k.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return Tn(t);case"keypress":return 32!==t.which?null:(_n=!0,Cn);case"textInput":return(e=t.data)===Cn&&_n?null:e;default:return null}}(e,n):function(e,t){if(Pn)return"compositionend"===e||!xn&&jn(e,t)?(e=Wt(),Bt=Ht=Ut=null,Pn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(k=Wc(r,"onBeforeInput")).length&&(v=new un("onBeforeInput","beforeinput",null,n,a),i.push({event:v,listeners:k}),v.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Pc((a[Le]||null).action),i=r.submitter;i&&null!==(t=(t=i[Le]||null)?Pc(t.formAction):i.getAttribute("formAction"))&&(l=t,i=null);var o=new Jt("action","action",null,r,a);e.push({event:o,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==wc){var e=i?zc(a,i):new FormData(a);Pi(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"===typeof l&&(o.preventDefault(),e=i?zc(a,i):new FormData(a),Pi(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(i,e,r,n,a)}Mc(i,t)})}function Bc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Dt(e,n))&&r.unshift(Bc(e,a,l)),null!=(a=Dt(e,t))&&r.push(Bc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function qc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Vc(e,t,n,r,a){for(var l=t._reactName,i=[];null!==n&&n!==r;){var o=n,s=o.alternate,u=o.stateNode;if(o=o.tag,null!==s&&s===r)break;5!==o&&26!==o&&27!==o||null===u||(s=u,a?null!=(u=Dt(n,l))&&i.unshift(Bc(n,u,s)):a||null!=(u=Dt(n,l))&&i.push(Bc(n,u,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Qc=/\r\n?/g,Gc=/\u0000|\uFFFD/g;function Kc(e){return("string"===typeof e?e:""+e).replace(Qc,"\n").replace(Gc,"")}function Yc(e,t){return t=Kc(t),Kc(e)===t}function Xc(){}function Zc(e,t,n,r,a,l){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||xt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&xt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Nt(e,r,l);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof l&&("formAction"===n?("input"!==t&&Zc(e,t,"name",a.name,a,null),Zc(e,t,"formEncType",a.formEncType,a,null),Zc(e,t,"formMethod",a.formMethod,a,null),Zc(e,t,"formTarget",a.formTarget,a,null)):(Zc(e,t,"encType",a.encType,a,null),Zc(e,t,"method",a.method,a,null),Zc(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Tt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Dc("beforetoggle",e),Dc("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=_t.get(n)||n,r)}}function Jc(e,t,n,r,a,l){switch(n){case"style":Nt(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?xt(e,r):("number"===typeof r||"bigint"===typeof r)&&xt(e,""+r);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(l=null!=(l=e[Le]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dc("error",e),Dc("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var o=n[r];if(null!=o)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Zc(e,t,r,o,n,null)}}return l&&Zc(e,t,"srcSet",n.srcSet,n,null),void(a&&Zc(e,t,"src",n.src,n,null));case"input":Dc("invalid",e);var s=r=o=l=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":l=d;break;case"type":o=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Zc(e,t,a,d,n,null)}}return yt(e,r,s,u,c,o,l,!1),void dt(e);case"select":for(l in Dc("invalid",e),a=o=r=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":r=s;break;case"defaultValue":o=s;break;case"multiple":a=s;default:Zc(e,t,l,s,n,null)}return t=r,n=o,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(o in Dc("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":a=s;break;case"defaultValue":l=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(i(91));break;default:Zc(e,t,o,s,n,null)}return wt(e,a,l,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Zc(e,t,u,a,n,null);return;case"dialog":Dc("beforetoggle",e),Dc("toggle",e),Dc("cancel",e),Dc("close",e);break;case"iframe":case"object":Dc("load",e);break;case"video":case"audio":for(a=0;a<Oc.length;a++)Dc(Oc[a],e);break;case"image":Dc("error",e),Dc("load",e);break;case"details":Dc("toggle",e);break;case"embed":case"source":case"link":Dc("error",e),Dc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Zc(e,t,c,a,n,null)}return;default:if(Ct(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Jc(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&Zc(e,t,s,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ld(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var od=null;var sd="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function hd(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&wd(i.documentElement),2&n&&wd(i.body),4&n)for(wd(n=i.head),i=n.firstChild;i;){var o=i.nextSibling,s=i.nodeName;i[$e]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=o}}if(0===a)return e.removeChild(l),void jf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);jf(t)}function md(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":md(n),Fe(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function kd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function wd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Fe(e)}var xd=new Map,Sd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Nd=A.d;A.d={f:function(){var e=Nd.f(),t=Uu();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Ri(t):Nd.r(e)},D:function(e){Nd.D(e),_d("dns-prefetch",e,null)},C:function(e,t){Nd.C(e,t),_d("preconnect",e,t)},L:function(e,t,n){Nd.L(e,t,n);var r=Cd;if(r&&e&&t){var a='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+mt(n.imageSizes)+'"]')):a+='[href="'+mt(e)+'"]';var l=a;switch(t){case"style":l=Td(e);break;case"script":l=Rd(e)}xd.has(l)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),xd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Pd(l))||"script"===t&&r.querySelector(Ld(l))||(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}},m:function(e,t){Nd.m(e,t);var n=Cd;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+mt(r)+'"][href="'+mt(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Rd(e)}if(!xd.has(l)&&(e=f({rel:"modulepreload",href:e},t),xd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ld(l)))return}ed(r=n.createElement("link"),"link",e),qe(r),n.head.appendChild(r)}}},X:function(e,t){Nd.X(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),l=r.get(a);l||((l=n.querySelector(Ld(a)))||(e=f({src:e,async:!0},t),(t=xd.get(a))&&Dd(e,t),qe(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Nd.S(e,t,n);var r=Cd;if(r&&e){var a=We(r).hoistableStyles,l=Td(e);t=t||"default";var i=a.get(l);if(!i){var o={loading:0,preload:null};if(i=r.querySelector(Pd(l)))o.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=xd.get(l))&&Md(e,n);var s=i=r.createElement("link");qe(s),ed(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){o.loading|=1}),s.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Ad(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:o},a.set(l,i)}}},M:function(e,t){Nd.M(e,t);var n=Cd;if(n&&e){var r=We(n).hoistableScripts,a=Rd(e),l=r.get(a);l||((l=n.querySelector(Ld(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=xd.get(a))&&Dd(e,t),qe(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Cd="undefined"===typeof document?null:document;function _d(e,t,n){var r=Cd;if(r&&"string"===typeof t&&t){var a=mt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}}function jd(e,t,n,r){var a,l,o,s,u=(u=W.current)?Ed(u):null;if(!u)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Td(n.href),(r=(n=We(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Td(n.href);var c=We(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Pd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),xd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xd.set(e,n),c||(a=u,l=e,o=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?s.loading=1:(l=a.createElement("link"),s.preload=l,l.addEventListener("load",function(){return s.loading|=1}),l.addEventListener("error",function(){return s.loading|=2}),ed(l,"link",o),qe(l),a.head.appendChild(l))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Rd(n),(r=(n=We(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Td(e){return'href="'+mt(e)+'"'}function Pd(e){return'link[rel="stylesheet"]['+e+"]"}function zd(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+mt(e)+'"]'}function Ld(e){return"script[async]"+e}function Od(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(r)return t.instance=r,qe(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return qe(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ad(r,n.precedence,e),t.instance=r;case"stylesheet":a=Td(n.href);var l=e.querySelector(Pd(a));if(l)return t.state.loading|=4,t.instance=l,qe(l),l;r=zd(n),(a=xd.get(a))&&Md(r,a),qe(l=(e.ownerDocument||e).createElement("link"));var o=l;return o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),ed(l,"link",r),t.state.loading|=4,Ad(l,n.precedence,e),t.instance=l;case"script":return l=Rd(n.src),(a=e.querySelector(Ld(l)))?(t.instance=a,qe(a),a):(r=n,(a=xd.get(l))&&Dd(r=f({},n),a),qe(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Ad(r,n.precedence,e));return t.instance}function Ad(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,i=0;i<r.length;i++){var o=r[i];if(o.dataset.precedence===t)l=o;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Md(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function $d(e,t,n){if(null===Id){var r=new Map,a=Id=new Map;a.set(n,r)}else(r=(a=Id).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[$e]||l[Re]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var i=l.getAttribute(t)||"";i=e+i;var o=r.get(i);o?o.push(l):r.set(i,[l])}}return r}function Fd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Ud(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function Bd(){}function Wd(){if(this.count--,0===this.count)if(this.stylesheets)Vd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var qd=null;function Vd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,qd=new Map,t.forEach(Qd,e),qd=null,Wd.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=qd.get(e);if(n)var r=n.get(null);else{n=new Map,qd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var i=a[l];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(l=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Wd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gd={$$typeof:w,Provider:null,Consumer:null,_currentValue:M,_currentValue2:M,_threadCount:0};function Kd(e,t,n,r,a,l,i,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Yd(e,t,n,r,a,l,i,o,s,u,c,d){return e=new Kd(e,t,n,i,o,s,u,d),t=1,!0===l&&(t|=24),l=Mr(3,null,null,t),e.current=l,l.stateNode=e,(t=La()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},nl(l),e}function Xd(e){return e?e=Or:Or}function Zd(e,t,n,r,a,l){a=Xd(a),null===r.context?r.context=a:r.pendingContext=a,(r=al(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=ll(e,r,t))&&(Mu(n,0,t),il(n,e,t))}function Jd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Jd(e,t),(e=e.alternate)&&Jd(e,t)}function tf(e){if(13===e.tag){var t=zr(e,67108864);null!==t&&Mu(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=O.T;O.T=null;var l=A.p;try{A.p=2,lf(e,t,n,r)}finally{A.p=l,O.T=a}}function af(e,t,n,r){var a=O.T;O.T=null;var l=A.p;try{A.p=8,lf(e,t,n,r)}finally{A.p=l,O.T=a}}function lf(e,t,n,r){if(nf){var a=of(r);if(null===a)Hc(e,t,r,sf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=kf(ff,e,t,n,r,a),!0;case"dragenter":return pf=kf(pf,e,t,n,r,a),!0;case"mouseover":return hf=kf(hf,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return mf.set(l,kf(mf.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,gf.set(l,kf(gf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var l=He(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var i=ve(l.pendingLanes);if(0!==i){var o=l;for(o.pendingLanes|=2,o.entangledLanes|=2;i;){var s=1<<31-pe(i);o.entanglements[1]|=s,i&=~s}xc(l),0===(6&nu)&&(xu=te()+500,Sc(0,!1))}}break;case 13:null!==(o=zr(l,2))&&Mu(o,0,2),Uu(),ef(l,2)}if(null===(l=of(r))&&Hc(e,t,r,sf,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Hc(e,t,r,null,n)}}function of(e){return uf(e=zt(e))}var sf=null;function uf(e){if(sf=null,null!==(e=Ue(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case le:case ie:return 32;case oe:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,hf=null,mf=new Map,gf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":hf=null;break;case"pointerover":case"pointerout":mf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function kf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function wf(e){var t=Ue(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=A.p;try{return A.p=e,t()}finally{A.p=n}}(e.priority,function(){if(13===n.tag){var e=Ou();e=je(e);var t=zr(n,e);null!==t&&Mu(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function xf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=of(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Pt=r,n.target.dispatchEvent(r),Pt=null,t.shift()}return!0}function Sf(e,t,n){xf(e)&&n.delete(t)}function Ef(){df=!1,null!==ff&&xf(ff)&&(ff=null),null!==pf&&xf(pf)&&(pf=null),null!==hf&&xf(hf)&&(hf=null),mf.forEach(Sf),gf.forEach(Sf)}function Nf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var Cf=null;function _f(e){Cf!==e&&(Cf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Cf===e&&(Cf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var l=He(n);null!==l&&(e.splice(t,3),t-=3,Pi(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function jf(e){function t(t){return Nf(t,e)}null!==ff&&Nf(ff,e),null!==pf&&Nf(pf,e),null!==hf&&Nf(hf,e),mf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)wf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],i=a[Le]||null;if("function"===typeof l)i||_f(n);else if(i){var o=null;if(l&&l.hasAttribute("formAction")){if(a=l,i=l[Le]||null)o=i.formAction;else if(null!==uf(a))continue}else o=i.action;"function"===typeof o?n[r+1]=o:(n.splice(r,3),r-=3),_f(n)}}}function Tf(e){this._internalRoot=e}function Pf(e){this._internalRoot=e}Pf.prototype.render=Tf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Zd(t.current,Ou(),e,t,null,null)},Pf.prototype.unmount=Tf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Zd(e.current,2,null,e,null,null),Uu(),t[Oe]=null}},Pf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&wf(e)}};var zf=a.version;if("19.1.0"!==zf)throw Error(i(527,zf,"19.1.0"));A.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return c(a),e;if(l===r)return c(a),t;l=l.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=l;else{for(var o=!1,u=a.child;u;){if(u===n){o=!0,n=a,r=l;break}if(u===r){o=!0,r=a,n=l;break}u=u.sibling}if(!o){for(u=l.child;u;){if(u===n){o=!0,n=l,r=a;break}if(u===r){o=!0,r=l,n=a;break}u=u.sibling}if(!o)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Rf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Lf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Lf.isDisabled&&Lf.supportsFiber)try{ce=Lf.inject(Rf),de=Lf}catch(Af){}}t.createRoot=function(e,t){if(!o(e))throw Error(i(299));var n=!1,r="",a=vo,l=bo,s=ko;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Yd(e,1,!1,null,0,n,r,a,l,s,0,null),e[Oe]=t.current,Fc(e),new Tf(t)},t.hydrateRoot=function(e,t,n){if(!o(e))throw Error(i(299));var r=!1,a="",l=vo,s=bo,u=ko,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Yd(e,1,!0,t,0,r,a,l,s,u,0,c)).context=Xd(null),n=t.current,(a=al(r=je(r=Ou()))).callback=null,ll(n,a,r),n=r,t.current.lanes=n,Ne(t,n),xc(t),e[Oe]=t.current,Fc(e),new Pf(t)},t.version="19.1.0"},43:(e,t,n)=>{e.exports=n(288)},237:(e,t,n)=>{e.exports=n(365)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var k=b.prototype=new v;k.constructor=b,m(k,y.prototype),k.isPureReactComponent=!0;var w=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function E(e,t,r,a,l,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function _(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function j(){}function T(e,t,a,l,i){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(o){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return T((c=e._init)(e._payload),t,a,l,i)}}if(c)return i=i(e),c=""===l?"."+_(e,0):l,w(i)?(a="",null!=c&&(a=c.replace(C,"$&/")+"/"),T(i,t,a,"",function(e){return e})):null!=i&&(N(i)&&(s=i,u=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(C,"$&/")+"/")+c,i=E(s.type,u,void 0,0,0,s.props)),t.push(i)),1;c=0;var d,h=""===l?".":l+":";if(w(e))for(var m=0;m<e.length;m++)c+=T(l=e[m],t,a,o=h+_(l,m),i);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(l=e.next()).done;)c+=T(l=l.value,t,a,o=h+_(l,m++),i);else if("object"===o){if("function"===typeof e.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(j,j):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function P(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function z(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function L(){}t.Children={map:P,forEach:function(e,t,n){P(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=m({},e.props),a=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var i=Array(l),o=0;o<l;o++)i[o]=arguments[o+2];r.children=i}return E(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:o,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var o=Array(i),s=0;s<i;s++)o[s]=arguments[s+2];a.children=o}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return E(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:z}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=x.T,n={};x.T=n;try{var r=e(),a=x.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(L,R)}catch(l){R(l)}finally{x.T=t}},t.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},t.use=function(e){return x.H.use(e)},t.useActionState=function(e,t,n){return x.H.useActionState(e,t,n)},t.useCallback=function(e,t){return x.H.useCallback(e,t)},t.useContext=function(e){return x.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return x.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=x.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return x.H.useId()},t.useImperativeHandle=function(e,t,n){return x.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return x.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return x.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return x.H.useMemo(e,t)},t.useOptimistic=function(e,t){return x.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return x.H.useReducer(e,t,n)},t.useRef=function(e){return x.H.useRef(e)},t.useState=function(e){return x.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return x.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return x.H.useTransition()},t.version="19.1.0"},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,l=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function u(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},365:(e,t,n)=>{var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},l=r.useSyncExternalStore,i=r.useRef,o=r.useEffect,s=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=s(function(){function e(e){if(!o){if(o=!0,l=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,a(l,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(l=e,t):(l=e,i=n)}var l,i,o=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,n,r,c]);var p=l(e,d[0],d[1]);return o(function(){f.hasValue=!0,f.value=p},[p]),u(p),p}},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var i={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},o=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=i.p;try{if(s.T=null,i.p=2,e)return e()}finally{s.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,l="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var o=2*(r+1)-1,s=e[o],u=o+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[o]=n,r=o);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,k="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(g=!1,w(e),!m)if(null!==r(u))m=!0,E||(E=!0,S());else{var t=r(c);null!==t&&R(x,t.startTime-e)}}var S,E=!1,N=-1,C=5,_=-1;function j(){return!!y||!(t.unstable_now()-_<C)}function T(){if(y=!1,E){var e=t.unstable_now();_=e;var n=!0;try{e:{m=!1,g&&(g=!1,b(N),N=-1),h=!0;var l=p;try{t:{for(w(e),f=r(u);null!==f&&!(f.expirationTime>e&&j());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var o=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof o){f.callback=o,w(e),n=!0;break t}f===r(u)&&a(u),w(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&R(x,s.startTime-e),n=!1}}break e}finally{f=null,p=l,h=!1}n=void 0}}finally{n?S():E=!1}}}if("function"===typeof k)S=function(){k(T)};else if("undefined"!==typeof MessageChannel){var P=new MessageChannel,z=P.port2;P.port1.onmessage=T,S=function(){z.postMessage(null)}}else S=function(){v(T,0)};function R(e,n){N=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var i=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?i+l:i:l=i,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>i?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(b(N),N=-1):g=!0,R(x,l-i))):(e.sortIndex=o,n(u,e),m||h||(m=!0,E||(E=!0,S()))),e},t.unstable_shouldYield=j,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".670e15c7.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="frontend:";n.l=(r,a,l,i)=>{if(e[r])e[r].push(a);else{var o,s;if(void 0!==l)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+l){o=d;break}}o||(s=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",t+l),o.src=r),e[r]=[a];var f=(t,n)=>{o.onerror=o.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),a&&a.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=f.bind(null,o.onerror),o.onload=f.bind(null,o.onload),s&&document.head.appendChild(o)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var l=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=l);var i=n.p+n.u(t),o=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var l=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+l+": "+i+")",o.name="ChunkLoadError",o.type=l,o.request=i,a[1](o)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,l,i=r[0],o=r[1],s=r[2],u=0;if(i.some(t=>0!==e[t])){for(a in o)n.o(o,a)&&(n.m[a]=o[a]);if(s)s(n)}for(t&&t(r);u<i.length;u++)l=i[u],n.o(e,l)&&e[l]&&e[l][0](),e[l]=0},r=self.webpackChunkfrontend=self.webpackChunkfrontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n(43),a=n(391),l=n(237);function i(e){e()}var o={notify(){},get:()=>[]};function s(e,t){let n,r=o,a=0,l=!1;function s(){d.onStateChange&&d.onStateChange()}function u(){a++,n||(n=t?t.addNestedSub(s):e.subscribe(s),r=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){i(()=>{let t=e;for(;t;)t.callback(),t=t.next})},get(){const t=[];let n=e;for(;n;)t.push(n),n=n.next;return t},subscribe(n){let r=!0;const a=t={callback:n,next:null,prev:t};return a.prev?a.prev.next=a:e=a,function(){r&&null!==e&&(r=!1,a.next?a.next.prev=a.prev:t=a.prev,a.prev?a.prev.next=a.next:e=a.next)}}}}())}function c(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=o)}const d={addNestedSub:function(e){u();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),c())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:s,isSubscribed:function(){return l},trySubscribe:function(){l||(l=!0,u())},tryUnsubscribe:function(){l&&(l=!1,c())},getListeners:()=>r};return d}var u=(()=>!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement))(),c=(()=>"undefined"!==typeof navigator&&"ReactNative"===navigator.product)(),d=(()=>u||c?r.useLayoutEffect:r.useEffect)();Object.defineProperty,Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var f=Symbol.for("react-redux-context"),p="undefined"!==typeof globalThis?globalThis:{};function h(){if(!r.createContext)return{};const e=p[f]??=new Map;let t=e.get(r.createContext);return t||(t=r.createContext(null),e.set(r.createContext,t)),t}var m=h();var g=function(e){const{children:t,context:n,serverState:a,store:l}=e,i=r.useMemo(()=>{const e=s(l);return{store:l,subscription:e,getServerState:a?()=>a:void 0}},[l,a]),o=r.useMemo(()=>l.getState(),[l]);d(()=>{const{subscription:e}=i;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==l.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[i,o]);const u=n||m;return r.createElement(u.Provider,{value:i},t)};function y(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;return function(){return r.useContext(e)}}var v=y();function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;const t=e===m?v:y(e),n=()=>{const{store:e}=t();return e};return Object.assign(n,{withTypes:()=>n}),n}var k=b();function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;const t=e===m?k:b(e),n=()=>t().dispatch;return Object.assign(n,{withTypes:()=>n}),n}var x=w(),S=(e,t)=>e===t;function E(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m;const t=e===m?v:y(e),n=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:a=S}="function"===typeof n?{equalityFn:n}:n;const i=t(),{store:o,subscription:s,getServerState:u}=i,c=(r.useRef(!0),r.useCallback({[e.name]:t=>e(t)}[e.name],[e])),d=(0,l.useSyncExternalStoreWithSelector)(s.addNestedSub,o.getState,u||o.getState,c,a);return r.useDebugValue(d),d};return Object.assign(n,{withTypes:()=>n}),n}var N=E(),C=(n(358),"popstate");function _(){return O(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return z("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:R(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function j(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function T(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function P(e,t){return{usr:e.state,key:e.key,idx:t}}function z(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return{pathname:"string"===typeof e?e:e.pathname,search:"",hash:"",..."string"===typeof t?L(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function R(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function L(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function O(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o="POP",s=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){o="POP";let e=c(),t=null==e?null:e-u;u=e,s&&s({action:o,location:p.location,delta:t})}function f(e){return A(e)}null==u&&(u=0,i.replaceState({...i.state,idx:u},""));let p={get action(){return o},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(C,d),s=e,()=>{a.removeEventListener(C,d),s=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o="PUSH";let r=z(p.location,e,t);n&&n(r,e),u=c()+1;let d=P(r,u),f=p.createHref(r);try{i.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}l&&s&&s({action:o,location:p.location,delta:1})},replace:function(e,t){o="REPLACE";let r=z(p.location,e,t);n&&n(r,e),u=c();let a=P(r,u),d=p.createHref(r);i.replaceState(a,"",d),l&&s&&s({action:o,location:p.location,delta:0})},go:e=>i.go(e)};return p}function A(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),j(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:R(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function M(e,t){return D(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function D(e,t,n,r){let a=Z(("string"===typeof t?L(t):t).pathname||"/",n);if(null==a)return null;let l=I(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let i=null;for(let o=0;null==i&&o<l.length;++o){let e=X(a);i=G(l[o],e,r)}return i}function I(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,l)=>{let i={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(j(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let o=re([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(j(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${o}".`),I(e.children,t,s,o)),(null!=e.path||e.index)&&t.push({path:o,score:Q(o,e.index),routesMeta:s})};return e.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of $(e.path))a(e,t,n);else a(e,t)}),t}function $(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let i=$(r.join("/")),o=[];return o.push(...i.map(e=>""===e?l:[l,e].join("/"))),a&&o.push(...i),o.map(t=>e.startsWith("/")&&""===t?"/":t)}var F=/^:[\w-]+$/,U=3,H=2,B=1,W=10,q=-2,V=e=>"*"===e;function Q(e,t){let n=e.split("/"),r=n.length;return n.some(V)&&(r+=q),t&&(r+=H),n.filter(e=>!V(e)).reduce((e,t)=>e+(F.test(t)?U:""===t?B:W),r)}function G(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let e=r[o],s=o===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=K({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=K({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:re([l,c.pathname]),pathnameBase:ae(re([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=re([l,c.pathnameBase]))}return i}function K(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Y(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1),s=r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=o[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{});return{params:s,pathname:l,pathnameBase:i,pattern:e}}function Y(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];T("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function X(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return T(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function Z(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function J(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function ee(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function te(e){let t=ee(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function ne(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=L(e):(r={...e},j(!r.pathname||!r.pathname.includes("?"),J("?","pathname","search",r)),j(!r.pathname||!r.pathname.includes("#"),J("#","pathname","hash",r)),j(!r.search||!r.search.includes("#"),J("#","search","hash",r)));let l,i=""===e||""===r.pathname,o=i?"/":r.pathname;if(null==o)l=n;else{let e=t.length-1;if(!a&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?L(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:le(r),hash:ie(a)}}(r,l),u=o&&"/"!==o&&o.endsWith("/"),c=(i||"."===o)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}var re=e=>e.join("/").replace(/\/\/+/g,"/"),ae=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),le=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",ie=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function oe(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var se=["POST","PUT","PATCH","DELETE"],ue=(new Set(se),["GET",...se]);new Set(ue),Symbol("ResetLoaderData");var ce=r.createContext(null);ce.displayName="DataRouter";var de=r.createContext(null);de.displayName="DataRouterState";var fe=r.createContext({isTransitioning:!1});fe.displayName="ViewTransition";var pe=r.createContext(new Map);pe.displayName="Fetchers";var he=r.createContext(null);he.displayName="Await";var me=r.createContext(null);me.displayName="Navigation";var ge=r.createContext(null);ge.displayName="Location";var ye=r.createContext({outlet:null,matches:[],isDataRoute:!1});ye.displayName="Route";var ve=r.createContext(null);ve.displayName="RouteError";function be(){return null!=r.useContext(ge)}function ke(){return j(be(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(ge).location}var we="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function xe(e){r.useContext(me).static||r.useLayoutEffect(e)}function Se(){let{isDataRoute:e}=r.useContext(ye);return e?function(){let{router:e}=Re("useNavigate"),t=Oe("useNavigate"),n=r.useRef(!1);xe(()=>{n.current=!0});let a=r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};T(n.current,we),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...a}))},[e,t]);return a}():function(){j(be(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(ce),{basename:t,navigator:n}=r.useContext(me),{matches:a}=r.useContext(ye),{pathname:l}=ke(),i=JSON.stringify(te(a)),o=r.useRef(!1);xe(()=>{o.current=!0});let s=r.useCallback(function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(T(o.current,we),!o.current)return;if("number"===typeof r)return void n.go(r);let s=ne(r,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:re([t,s.pathname])),(a.replace?n.replace:n.push)(s,a.state,a)},[t,n,i,l,e]);return s}()}r.createContext(null);function Ee(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=r.useContext(ye),{pathname:a}=ke(),l=JSON.stringify(te(n));return r.useMemo(()=>ne(e,JSON.parse(l),a,"path"===t),[e,l,a,t])}function Ne(e,t,n,a){j(be(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=r.useContext(me),{matches:i}=r.useContext(ye),o=i[i.length-1],s=o?o.params:{},u=o?o.pathname:"/",c=o?o.pathnameBase:"/",d=o&&o.route;{let e=d&&d.path||"";De(u,!d||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let f,p=ke();if(t){let e="string"===typeof t?L(t):t;j("/"===c||e.pathname?.startsWith(c),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${c}" but pathname "${e.pathname}" was given in the \`location\` prop.`),f=e}else f=p;let h=f.pathname||"/",m=h;if("/"!==c){let e=c.replace(/^\//,"").split("/");m="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=M(e,{pathname:m});T(d||null!=g,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),T(null==g||void 0!==g[g.length-1].route.element||void 0!==g[g.length-1].route.Component||void 0!==g[g.length-1].route.lazy,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let y=Pe(g&&g.map(e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:re([c,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:re([c,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,a);return t&&y?r.createElement(ge.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},y):y}function Ce(){let e=Ae(),t=oe(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=r.createElement(r.Fragment,null,r.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route.")),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:l},n):null,o)}var _e=r.createElement(Ce,null),je=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(ye.Provider,{value:this.props.routeContext},r.createElement(ve.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Te(e){let{routeContext:t,match:n,children:a}=e,l=r.useContext(ce);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(ye.Provider,{value:t},a)}function Pe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let a=e,l=n?.errors;if(null!=l){let e=a.findIndex(e=>e.route.id&&void 0!==l?.[e.route.id]);j(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(l).join(",")}`),a=a.slice(0,Math.min(a.length,e+1))}let i=!1,o=-1;if(n)for(let r=0;r<a.length;r++){let e=a[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(o=r),e.route.id){let{loaderData:t,errors:r}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||l){i=!0,a=o>=0?a.slice(0,o+1):[a[0]];break}}}return a.reduceRight((e,s,u)=>{let c,d=!1,f=null,p=null;n&&(c=l&&s.route.id?l[s.route.id]:void 0,f=s.route.errorElement||_e,i&&(o<0&&0===u?(De("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):o===u&&(d=!0,p=s.route.hydrateFallbackElement||null)));let h=t.concat(a.slice(0,u+1)),m=()=>{let t;return t=c?f:d?p:s.route.Component?r.createElement(s.route.Component,null):s.route.element?s.route.element:e,r.createElement(Te,{match:s,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===u)?r.createElement(je,{location:n.location,revalidation:n.revalidation,component:f,error:c,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()},null)}function ze(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Re(e){let t=r.useContext(ce);return j(t,ze(e)),t}function Le(e){let t=r.useContext(de);return j(t,ze(e)),t}function Oe(e){let t=function(e){let t=r.useContext(ye);return j(t,ze(e)),t}(e),n=t.matches[t.matches.length-1];return j(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Ae(){let e=r.useContext(ve),t=Le("useRouteError"),n=Oe("useRouteError");return void 0!==e?e:t.errors?.[n]}var Me={};function De(e,t,n){t||Me[e]||(Me[e]=!0,T(!1,n))}r.memo(function(e){let{routes:t,future:n,state:r}=e;return Ne(t,void 0,r,n)});function Ie(e){let{basename:t="/",children:n=null,location:a,navigationType:l="POP",navigator:i,static:o=!1}=e;j(!be(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=t.replace(/^\/*/,"/"),u=r.useMemo(()=>({basename:s,navigator:i,static:o,future:{}}),[s,i,o]);"string"===typeof a&&(a=L(a));let{pathname:c="/",search:d="",hash:f="",state:p=null,key:h="default"}=a,m=r.useMemo(()=>{let e=Z(c,s);return null==e?null:{location:{pathname:e,search:d,hash:f,state:p,key:h},navigationType:l}},[s,c,d,f,p,h,l]);return T(null!=m,`<Router basename="${s}"> is not able to match the URL "${c}${d}${f}" because it does not start with the basename, so the <Router> won't render anything.`),null==m?null:r.createElement(me.Provider,{value:u},r.createElement(ge.Provider,{children:n,value:m}))}r.Component;var $e="get",Fe="application/x-www-form-urlencoded";function Ue(e){return null!=e&&"string"===typeof e.tagName}var He=null;var Be=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function We(e){return null==e||Be.has(e)?e:(T(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Fe}"`),null)}function qe(e,t){let n,r,a,l,i;if(Ue(o=e)&&"form"===o.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?Z(i,t):null,n=e.getAttribute("method")||$e,a=We(e.getAttribute("enctype"))||Fe,l=new FormData(e)}else if(function(e){return Ue(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ue(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let o=e.getAttribute("formaction")||i.getAttribute("action");if(r=o?Z(o,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||$e,a=We(e.getAttribute("formenctype"))||We(i.getAttribute("enctype"))||Fe,l=new FormData(i,e),!function(){if(null===He)try{new FormData(document.createElement("form"),0),He=!1}catch(e){He=!0}return He}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";l.append(`${e}x`,"0"),l.append(`${e}y`,"0")}else t&&l.append(t,r)}}else{if(Ue(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=$e,r=null,a=Fe,i=e}var o;return l&&"text/plain"===a&&(i=l,l=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:l,body:i}}function Ve(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Qe(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ge(e){return null!=e&&"string"===typeof e.page}function Ke(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Ye(e,t,n,r,a,l){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,o=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===l?t.filter((e,t)=>i(e,t)||o(e,t)):"data"===l?t.filter((t,l)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,l)||o(t,l))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function Xe(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function Ze(e,t){let n=new Set,r=new Set(t);return e.reduce((e,a)=>{if(t&&!Ge(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let l=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(l)||(n.add(l),e.push({key:l,link:a})),e},[])}function Je(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var et=new Set([100,101,204,205]);function tt(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===Z(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}r.Component;function nt(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let a,l=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(oe(t))return r.createElement(rt,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),l);if(t instanceof Error)a=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);a=new Error(e)}return r.createElement(rt,{title:"Application Error!",isOutsideRemixApp:n},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),l)}function rt(e){let{title:t,renderScripts:n,isOutsideRemixApp:a,children:l}=e,{routeModules:i}=st();return i.root?.Layout&&!a?l:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,t)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},l,n?r.createElement(mt,null):null)))}function at(e,t){return"lazy"===e.mode&&!0===t}function lt(){let e=r.useContext(ce);return Ve(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function it(){let e=r.useContext(de);return Ve(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ot=r.createContext(void 0);function st(){let e=r.useContext(ot);return Ve(e,"You must render this element inside a <HydratedRouter> element"),e}function ut(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function ct(e,t,n){if(n&&!ht)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}function dt(e){let{page:t,...n}=e,{router:a}=lt(),l=r.useMemo(()=>M(a.routes,t,a.basename),[a.routes,t,a.basename]);return l?r.createElement(pt,{page:t,matches:l,...n}):null}function ft(e){let{manifest:t,routeModules:n}=st(),[a,l]=r.useState([]);return r.useEffect(()=>{let r=!1;return async function(e,t,n){return Ze((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await Qe(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(Ke).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}(e,t,n).then(e=>{r||l(e)}),()=>{r=!0}},[e,t,n]),a}function pt(e){let{page:t,matches:n,...a}=e,l=ke(),{manifest:i,routeModules:o}=st(),{basename:s}=lt(),{loaderData:u,matches:c}=it(),d=r.useMemo(()=>Ye(t,n,c,i,l,"data"),[t,n,c,i,l]),f=r.useMemo(()=>Ye(t,n,c,i,l,"assets"),[t,n,c,i,l]),p=r.useMemo(()=>{if(t===l.pathname+l.search+l.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{let n=i.routes[t.route.id];n&&n.hasLoader&&(!d.some(e=>e.route.id===t.route.id)&&t.route.id in u&&o[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let a=tt(t,s);return r&&e.size>0&&a.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[s,u,l,i,d,n,t,o]),h=r.useMemo(()=>Xe(f,i),[f,i]),m=ft(f);return r.createElement(r.Fragment,null,p.map(e=>r.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...a})),h.map(e=>r.createElement("link",{key:e,rel:"modulepreload",href:e,...a})),m.map(e=>{let{key:t,link:n}=e;return r.createElement("link",{key:t,...n})}))}ot.displayName="FrameworkContext";var ht=!1;function mt(e){let{manifest:t,serverHandoffString:n,isSpaMode:a,renderMeta:l,routeDiscovery:i,ssr:o}=st(),{router:s,static:u,staticContext:c}=lt(),{matches:d}=it(),f=at(i,o);l&&(l.didRenderScripts=!0);let p=ct(d,null,a);r.useEffect(()=>{ht=!0},[]);let h=r.useMemo(()=>{let a=c?`window.__reactRouterContext = ${n};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",l=u?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${f?"":`import ${JSON.stringify(t.url)}`};\n${p.map((e,n)=>{let r=`route${n}`,a=t.routes[e.route.id];Ve(a,`Route ${e.route.id} not found in manifest`);let{clientActionModule:l,clientLoaderModule:i,clientMiddlewareModule:o,hydrateFallbackModule:s,module:u}=a,c=[...l?[{module:l,varName:`${r}_clientAction`}]:[],...i?[{module:i,varName:`${r}_clientLoader`}]:[],...o?[{module:o,varName:`${r}_clientMiddleware`}]:[],...s?[{module:s,varName:`${r}_HydrateFallback`}]:[],{module:u,varName:`${r}_main`}];return 1===c.length?`import * as ${r} from ${JSON.stringify(u)};`:[c.map(e=>`import * as ${e.varName} from "${e.module}";`).join("\n"),`const ${r} = {${c.map(e=>`...${e.varName}`).join(",")}};`].join("\n")}).join("\n")}\n  ${f?`window.__reactRouterManifest = ${JSON.stringify(function(e,t){let{sri:n,...r}=e,a=new Set(t.state.matches.map(e=>e.route.id)),l=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(l.pop();l.length>0;)i.push(`/${l.join("/")}`),l.pop();i.forEach(e=>{let n=M(t.routes,e,t.basename);n&&n.forEach(e=>a.add(e.route.id))});let o=[...a].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return{...r,routes:o,sri:!!n||void 0}}(t,s),null,2)};`:""}\n  window.__reactRouterRouteModules = {${p.map((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return r.createElement(r.Fragment,null,r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Je(a),type:void 0}),r.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Je(l),type:"module",async:!0}))},[]),m=ht?[]:(g=t.entry.imports.concat(Xe(p,t,{includeHydrateFallback:!0})),[...new Set(g)]);var g;let y="object"===typeof t.sri?t.sri:{};return ht?null:r.createElement(r.Fragment,null,"object"===typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:y})}}):null,f?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:y[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:y[t.entry.module],suppressHydrationWarning:!0}),m.map(t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:y[t],suppressHydrationWarning:!0})),h)}function gt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}var yt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{yt&&(window.__reactRouterVersion="7.6.3")}catch(gs){}function vt(e){let{basename:t,children:n,window:a}=e,l=r.useRef();null==l.current&&(l.current=_({window:a,v5Compat:!0}));let i=l.current,[o,s]=r.useState({action:i.action,location:i.location}),u=r.useCallback(e=>{r.startTransition(()=>s(e))},[s]);return r.useLayoutEffect(()=>i.listen(u),[i,u]),r.createElement(Ie,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i})}var bt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,kt=r.forwardRef(function(e,t){let n,{onClick:a,discover:l="render",prefetch:i="none",relative:o,reloadDocument:s,replace:u,state:c,target:d,to:f,preventScrollReset:p,viewTransition:h,...m}=e,{basename:g}=r.useContext(me),y="string"===typeof f&&bt.test(f),v=!1;if("string"===typeof f&&y&&(n=f,yt))try{let e=new URL(window.location.href),t=f.startsWith("//")?new URL(e.protocol+f):new URL(f),n=Z(t.pathname,g);t.origin===e.origin&&null!=n?f=n+t.search+t.hash:v=!0}catch(gs){T(!1,`<Link to="${f}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let b=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};j(be(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:a}=r.useContext(me),{hash:l,pathname:i,search:o}=Ee(e,{relative:t}),s=i;return"/"!==n&&(s="/"===i?n:re([n,i])),a.createHref({pathname:s,search:o,hash:l})}(f,{relative:o}),[k,w,x]=function(e,t){let n=r.useContext(ot),[a,l]=r.useState(!1),[i,o]=r.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,p=r.useRef(null);r.useEffect(()=>{if("render"===e&&o(!0),"viewport"===e){let e=e=>{e.forEach(e=>{o(e.isIntersecting)})},t=new IntersectionObserver(e,{threshold:.5});return p.current&&t.observe(p.current),()=>{t.disconnect()}}},[e]),r.useEffect(()=>{if(a){let e=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(e)}}},[a]);let h=()=>{l(!0)},m=()=>{l(!1),o(!1)};return n?"intent"!==e?[i,p,{}]:[i,p,{onFocus:ut(s,h),onBlur:ut(u,m),onMouseEnter:ut(c,h),onMouseLeave:ut(d,m),onTouchStart:ut(f,h)}]:[!1,p,{}]}(i,m),S=function(e){let{target:t,replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=Se(),u=ke(),c=Ee(e,{relative:i});return r.useCallback(r=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(r,t)){r.preventDefault();let t=void 0!==n?n:R(u)===R(c);s(e,{replace:t,state:a,preventScrollReset:l,relative:i,viewTransition:o})}},[u,s,c,n,a,t,e,l,i,o])}(f,{replace:u,state:c,target:d,preventScrollReset:p,relative:o,viewTransition:h});let E=r.createElement("a",{...m,...x,href:n||b,onClick:v||s?a:function(e){a&&a(e),e.defaultPrevented||S(e)},ref:gt(t,w),target:d,"data-discover":y||"render"!==l?void 0:"true"});return k&&!y?r.createElement(r.Fragment,null,E,r.createElement(dt,{page:b})):E});kt.displayName="Link";var wt=r.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:a=!1,className:l="",end:i=!1,style:o,to:s,viewTransition:u,children:c,...d}=e,f=Ee(s,{relative:d.relative}),p=ke(),h=r.useContext(de),{navigator:m,basename:g}=r.useContext(me),y=null!=h&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.useContext(fe);j(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Et("useViewTransitionState"),l=Ee(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=Z(n.currentLocation.pathname,a)||n.currentLocation.pathname,o=Z(n.nextLocation.pathname,a)||n.nextLocation.pathname;return null!=K(l.pathname,o)||null!=K(l.pathname,i)}(f)&&!0===u,v=m.encodeLocation?m.encodeLocation(f).pathname:f.pathname,b=p.pathname,k=h&&h.navigation&&h.navigation.location?h.navigation.location.pathname:null;a||(b=b.toLowerCase(),k=k?k.toLowerCase():null,v=v.toLowerCase()),k&&g&&(k=Z(k,g)||k);const w="/"!==v&&v.endsWith("/")?v.length-1:v.length;let x,S=b===v||!i&&b.startsWith(v)&&"/"===b.charAt(w),E=null!=k&&(k===v||!i&&k.startsWith(v)&&"/"===k.charAt(v.length)),N={isActive:S,isPending:E,isTransitioning:y},C=S?n:void 0;x="function"===typeof l?l(N):[l,S?"active":null,E?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let _="function"===typeof o?o(N):o;return r.createElement(kt,{...d,"aria-current":C,className:x,ref:t,style:_,to:s,viewTransition:u},"function"===typeof c?c(N):c)});wt.displayName="NavLink";var xt=r.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:a,navigate:l,reloadDocument:i,replace:o,state:s,method:u=$e,action:c,onSubmit:d,relative:f,preventScrollReset:p,viewTransition:h,...m}=e,g=_t(),y=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=r.useContext(me),a=r.useContext(ye);j(a,"useFormAction must be used inside a RouteContext");let[l]=a.matches.slice(-1),i={...Ee(e||".",{relative:t})},o=ke();if(null==e){i.search=o.search;let e=new URLSearchParams(i.search),t=e.getAll("index"),n=t.some(e=>""===e);if(n){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();i.search=n?`?${n}`:""}}e&&"."!==e||!l.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:re([n,i.pathname]));return R(i)}(c,{relative:f}),v="get"===u.toLowerCase()?"get":"post",b="string"===typeof c&&bt.test(c);return r.createElement("form",{ref:t,method:v,action:y,onSubmit:i?d:e=>{if(d&&d(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=t?.getAttribute("formmethod")||u;g(t||e.currentTarget,{fetcherKey:a,method:n,navigate:l,replace:o,state:s,relative:f,preventScrollReset:p,viewTransition:h})},...m,"data-discover":b||"render"!==n?void 0:"true"})});function St(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Et(e){let t=r.useContext(ce);return j(t,St(e)),t}xt.displayName="Form";var Nt=0,Ct=()=>`__${String(++Nt)}__`;function _t(){let{router:e}=Et("useSubmit"),{basename:t}=r.useContext(me),n=Oe("useRouteId");return r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:l,method:i,encType:o,formData:s,body:u}=qe(r,t);if(!1===a.navigate){let t=a.fetcherKey||Ct();await e.fetch(t,n,a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||o,flushSync:a.flushSync})}else await e.navigate(a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||o,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}function jt(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var Tt=(()=>"function"===typeof Symbol&&Symbol.observable||"@@observable")(),Pt=()=>Math.random().toString(36).substring(7).split("").join("."),zt={INIT:`@@redux/INIT${Pt()}`,REPLACE:`@@redux/REPLACE${Pt()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${Pt()}`};function Rt(e){if("object"!==typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function Lt(e,t,n){if("function"!==typeof e)throw new Error(jt(2));if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(jt(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(jt(1));return n(Lt)(e,t)}let r=e,a=t,l=new Map,i=l,o=0,s=!1;function u(){i===l&&(i=new Map,l.forEach((e,t)=>{i.set(t,e)}))}function c(){if(s)throw new Error(jt(3));return a}function d(e){if("function"!==typeof e)throw new Error(jt(4));if(s)throw new Error(jt(5));let t=!0;u();const n=o++;return i.set(n,e),function(){if(t){if(s)throw new Error(jt(6));t=!1,u(),i.delete(n),l=null}}}function f(e){if(!Rt(e))throw new Error(jt(7));if("undefined"===typeof e.type)throw new Error(jt(8));if("string"!==typeof e.type)throw new Error(jt(17));if(s)throw new Error(jt(9));try{s=!0,a=r(a,e)}finally{s=!1}return(l=i).forEach(e=>{e()}),e}f({type:zt.INIT});return{dispatch:f,subscribe:d,getState:c,replaceReducer:function(e){if("function"!==typeof e)throw new Error(jt(10));r=e,f({type:zt.REPLACE})},[Tt]:function(){const e=d;return{subscribe(t){if("object"!==typeof t||null===t)throw new Error(jt(11));function n(){const e=t;e.next&&e.next(c())}n();return{unsubscribe:e(n)}},[Tt](){return this}}}}}function Ot(e){const t=Object.keys(e),n={};for(let l=0;l<t.length;l++){const r=t[l];0,"function"===typeof e[r]&&(n[r]=e[r])}const r=Object.keys(n);let a;try{!function(e){Object.keys(e).forEach(t=>{const n=e[t];if("undefined"===typeof n(void 0,{type:zt.INIT}))throw new Error(jt(12));if("undefined"===typeof n(void 0,{type:zt.PROBE_UNKNOWN_ACTION()}))throw new Error(jt(13))})}(n)}catch(gs){a=gs}return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(a)throw a;let l=!1;const i={};for(let a=0;a<r.length;a++){const o=r[a],s=n[o],u=e[o],c=s(u,t);if("undefined"===typeof c){t&&t.type;throw new Error(jt(14))}i[o]=c,l=l||c!==u}return l=l||r.length!==Object.keys(e).length,l?i:e}}function At(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?e=>e:1===t.length?t[0]:t.reduce((e,t)=>function(){return e(t(...arguments))})}function Mt(e){return t=>{let{dispatch:n,getState:r}=t;return t=>a=>"function"===typeof a?a(n,r,e):t(a)}}var Dt=Mt(),It=Mt,$t=Symbol.for("immer-nothing"),Ft=Symbol.for("immer-draftable"),Ut=Symbol.for("immer-state");function Ht(e){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var Bt=Object.getPrototypeOf;function Wt(e){return!!e&&!!e[Ut]}function qt(e){return!!e&&(Qt(e)||Array.isArray(e)||!!e[Ft]||!!e.constructor?.[Ft]||Zt(e)||Jt(e))}var Vt=Object.prototype.constructor.toString();function Qt(e){if(!e||"object"!==typeof e)return!1;const t=Bt(e);if(null===t)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Vt}function Gt(e,t){0===Kt(e)?Reflect.ownKeys(e).forEach(n=>{t(n,e[n],e)}):e.forEach((n,r)=>t(r,n,e))}function Kt(e){const t=e[Ut];return t?t.type_:Array.isArray(e)?1:Zt(e)?2:Jt(e)?3:0}function Yt(e,t){return 2===Kt(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Xt(e,t,n){const r=Kt(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function Zt(e){return e instanceof Map}function Jt(e){return e instanceof Set}function en(e){return e.copy_||e.base_}function tn(e,t){if(Zt(e))return new Map(e);if(Jt(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=Qt(e);if(!0===t||"class_only"===t&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[Ut];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const a=n[r],l=t[a];!1===l.writable&&(l.writable=!0,l.configurable=!0),(l.get||l.set)&&(t[a]={configurable:!0,writable:!0,enumerable:l.enumerable,value:e[a]})}return Object.create(Bt(e),t)}{const t=Bt(e);if(null!==t&&n)return{...e};const r=Object.create(t);return Object.assign(r,e)}}function nn(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return an(e)||Wt(e)||!qt(e)||(Kt(e)>1&&(e.set=e.add=e.clear=e.delete=rn),Object.freeze(e),t&&Object.entries(e).forEach(e=>{let[t,n]=e;return nn(n,!0)})),e}function rn(){Ht(2)}function an(e){return Object.isFrozen(e)}var ln,on={};function sn(e){const t=on[e];return t||Ht(0),t}function un(){return ln}function cn(e,t){t&&(sn("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function dn(e){fn(e),e.drafts_.forEach(hn),e.drafts_=null}function fn(e){e===ln&&(ln=e.parent_)}function pn(e){return ln={drafts_:[],parent_:ln,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function hn(e){const t=e[Ut];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function mn(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return void 0!==e&&e!==n?(n[Ut].modified_&&(dn(t),Ht(4)),qt(e)&&(e=gn(t,e),t.parent_||vn(t,e)),t.patches_&&sn("Patches").generateReplacementPatches_(n[Ut].base_,e,t.patches_,t.inversePatches_)):e=gn(t,n,[]),dn(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==$t?e:void 0}function gn(e,t,n){if(an(t))return t;const r=t[Ut];if(!r)return Gt(t,(a,l)=>yn(e,r,t,a,l,n)),t;if(r.scope_!==e)return t;if(!r.modified_)return vn(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let a=t,l=!1;3===r.type_&&(a=new Set(t),t.clear(),l=!0),Gt(a,(a,i)=>yn(e,r,t,a,i,n,l)),vn(e,t,!1),n&&e.patches_&&sn("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function yn(e,t,n,r,a,l,i){if(Wt(a)){const i=gn(e,a,l&&t&&3!==t.type_&&!Yt(t.assigned_,r)?l.concat(r):void 0);if(Xt(n,r,i),!Wt(i))return;e.canAutoFreeze_=!1}else i&&n.add(a);if(qt(a)&&!an(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;gn(e,a),t&&t.scope_.parent_||"symbol"===typeof r||!Object.prototype.propertyIsEnumerable.call(n,r)||vn(e,a)}}function vn(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&nn(t,n)}var bn={get(e,t){if(t===Ut)return e;const n=en(e);if(!Yt(n,t))return function(e,t,n){const r=xn(t,n);return r?"value"in r?r.value:r.get?.call(e.draft_):void 0}(e,n,t);const r=n[t];return e.finalized_||!qt(r)?r:r===wn(e.base_,t)?(En(e),e.copy_[t]=Nn(r,e)):r},has:(e,t)=>t in en(e),ownKeys:e=>Reflect.ownKeys(en(e)),set(e,t,n){const r=xn(en(e),t);if(r?.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const r=wn(en(e),t),i=r?.[Ut];if(i&&i.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(((a=n)===(l=r)?0!==a||1/a===1/l:a!==a&&l!==l)&&(void 0!==n||Yt(e.base_,t)))return!0;En(e),Sn(e)}var a,l;return e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==wn(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,En(e),Sn(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const n=en(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){Ht(11)},getPrototypeOf:e=>Bt(e.base_),setPrototypeOf(){Ht(12)}},kn={};function wn(e,t){const n=e[Ut];return(n?en(n):e)[t]}function xn(e,t){if(!(t in e))return;let n=Bt(e);for(;n;){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=Bt(n)}}function Sn(e){e.modified_||(e.modified_=!0,e.parent_&&Sn(e.parent_))}function En(e){e.copy_||(e.copy_=tn(e.base_,e.scope_.immer_.useStrictShallowCopy_))}Gt(bn,(e,t)=>{kn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),kn.deleteProperty=function(e,t){return kn.set.call(this,e,t,void 0)},kn.set=function(e,t,n){return bn.set.call(this,e[0],t,n,e[0])};function Nn(e,t){const n=Zt(e)?sn("MapSet").proxyMap_(e,t):Jt(e)?sn("MapSet").proxySet_(e,t):function(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:un(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let a=r,l=bn;n&&(a=[r],l=kn);const{revoke:i,proxy:o}=Proxy.revocable(a,l);return r.draft_=o,r.revoke_=i,o}(e,t);return(t?t.scope_:un()).drafts_.push(n),n}function Cn(e){if(!qt(e)||an(e))return e;const t=e[Ut];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=tn(e,t.scope_.immer_.useStrictShallowCopy_)}else n=tn(e,!0);return Gt(n,(e,t)=>{Xt(n,e,Cn(t))}),t&&(t.finalized_=!1),n}var _n=new class{constructor(e){var t=this;this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{if("function"===typeof e&&"function"!==typeof t){const n=t;t=e;const r=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n;for(var a=arguments.length,l=new Array(a>1?a-1:0),i=1;i<a;i++)l[i-1]=arguments[i];return r.produce(e,e=>t.call(this,e,...l))}}let r;if("function"!==typeof t&&Ht(6),void 0!==n&&"function"!==typeof n&&Ht(7),qt(e)){const a=pn(this),l=Nn(e,void 0);let i=!0;try{r=t(l),i=!1}finally{i?dn(a):fn(a)}return cn(a,n),mn(r,a)}if(!e||"object"!==typeof e){if(r=t(e),void 0===r&&(r=e),r===$t&&(r=void 0),this.autoFreeze_&&nn(r,!0),n){const t=[],a=[];sn("Patches").generateReplacementPatches_(e,r,t,a),n(t,a)}return r}Ht(1)},this.produceWithPatches=(e,n)=>{if("function"===typeof e)return function(n){for(var r=arguments.length,a=new Array(r>1?r-1:0),l=1;l<r;l++)a[l-1]=arguments[l];return t.produceWithPatches(n,t=>e(t,...a))};let r,a;return[this.produce(e,n,(e,t)=>{r=e,a=t}),r,a]},"boolean"===typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"===typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){qt(e)||Ht(8),Wt(e)&&(e=function(e){Wt(e)||Ht(10);return Cn(e)}(e));const t=pn(this),n=Nn(e,void 0);return n[Ut].isManual_=!0,fn(t),n}finishDraft(e,t){const n=e&&e[Ut];n&&n.isManual_||Ht(9);const{scope_:r}=n;return cn(r,t),mn(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));const r=sn("Patches").applyPatches_;return Wt(e)?r(e,t):this.produce(e,e=>r(e,t))}},jn=_n.produce;_n.produceWithPatches.bind(_n),_n.setAutoFreeze.bind(_n),_n.setUseStrictShallowCopy.bind(_n),_n.applyPatches.bind(_n),_n.createDraft.bind(_n),_n.finishDraft.bind(_n);var Tn="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?At:At.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Pn(e,t){function n(){if(t){let n=t(...arguments);if(!n)throw new Error(qn(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:arguments.length<=0?void 0:arguments[0]}}return n.toString=()=>`${e}`,n.type=e,n.match=t=>function(e){return Rt(e)&&"type"in e&&"string"===typeof e.type}(t)&&t.type===e,n}var zn=class e extends Array{constructor(){super(...arguments),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return super.concat.apply(this,t)}prepend(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return 1===n.length&&Array.isArray(n[0])?new e(...n[0].concat(this)):new e(...n.concat(this))}};function Rn(e){return qt(e)?jn(e,()=>{}):e}function Ln(e,t,n){return e.has(t)?e.get(t):e.set(t,n(t)).get(t)}var On="RTK_autoBatch",An=e=>t=>{setTimeout(t,e)},Mn=e=>function(t){const{autoBatch:n=!0}=t??{};let r=new zn(e);return n&&r.push(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"raf"};return t=>function(){const n=t(...arguments);let r=!0,a=!1,l=!1;const i=new Set,o="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:An(10):"callback"===e.type?e.queueNotification:An(e.timeout),s=()=>{l=!1,a&&(a=!1,i.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){const t=n.subscribe(()=>r&&e());return i.add(e),()=>{t(),i.delete(e)}},dispatch(e){try{return r=!e?.meta?.[On],a=!r,a&&(l||(l=!0,o(s))),n.dispatch(e)}finally{r=!0}}})}}("object"===typeof n?n:void 0)),r};function Dn(e){const t={},n=[];let r;const a={addCase(e,n){const r="string"===typeof e?e:e.type;if(!r)throw new Error(qn(28));if(r in t)throw new Error(qn(29));return t[r]=n,a},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),a),addDefaultCase:e=>(r=e,a)};return e(a),[t,n,r]}var In=Symbol.for("rtk-slice-createasyncthunk");function $n(e,t){return`${e}/${t}`}function Fn(){let{creators:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=e?.asyncThunk?.[In];return function(e){const{name:n,reducerPath:r=n}=e;if(!n)throw new Error(qn(11));const a=("function"===typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name](){return e(...arguments)}}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),i={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},o={addCase(e,t){const n="string"===typeof e?e:e.type;if(!n)throw new Error(qn(12));if(n in i.sliceCaseReducersByType)throw new Error(qn(13));return i.sliceCaseReducersByType[n]=t,o},addMatcher:(e,t)=>(i.sliceMatchers.push({matcher:e,reducer:t}),o),exposeAction:(e,t)=>(i.actionCreators[e]=t,o),exposeCaseReducer:(e,t)=>(i.sliceCaseReducersByName[e]=t,o)};function s(){const[t={},n=[],r]="function"===typeof e.extraReducers?Dn(e.extraReducers):[e.extraReducers],a={...t,...i.sliceCaseReducersByType};return function(e,t){let n,[r,a,l]=Dn(t);if("function"===typeof e)n=()=>Rn(e());else{const t=Rn(e);n=()=>t}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n(),t=arguments.length>1?arguments[1]:void 0,i=[r[t.type],...a.filter(e=>{let{matcher:n}=e;return n(t)}).map(e=>{let{reducer:t}=e;return t})];return 0===i.filter(e=>!!e).length&&(i=[l]),i.reduce((e,n)=>{if(n){if(Wt(e)){const r=n(e,t);return void 0===r?e:r}if(qt(e))return jn(e,e=>n(e,t));{const r=n(e,t);if(void 0===r){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}}return e},e)}return i.getInitialState=n,i}(e.initialState,e=>{for(let t in a)e.addCase(t,a[t]);for(let t of i.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of n)e.addMatcher(t.matcher,t.reducer);r&&e.addDefaultCase(r)})}l.forEach(r=>{const l=a[r],i={reducerName:r,type:$n(n,r),createNotation:"function"===typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(l)?function(e,t,n){let r,a,{type:l,reducerName:i,createNotation:o}=e;if("reducer"in t){if(o&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(t))throw new Error(qn(17));r=t.reducer,a=t.prepare}else r=t;n.addCase(l,r).exposeCaseReducer(i,r).exposeAction(i,a?Pn(l,a):Pn(l))}(i,l,o):function(e,t,n,r){let{type:a,reducerName:l}=e;if(!r)throw new Error(qn(18));const{payloadCreator:i,fulfilled:o,pending:s,rejected:u,settled:c,options:d}=t,f=r(a,i,d);n.exposeAction(l,f),o&&n.addCase(f.fulfilled,o);s&&n.addCase(f.pending,s);u&&n.addCase(f.rejected,u);c&&n.addMatcher(f.settled,c);n.exposeCaseReducer(l,{fulfilled:o||Bn,pending:s||Bn,rejected:u||Bn,settled:c||Bn})}(i,l,o,t)});const u=e=>e,c=new Map,d=new WeakMap;let f;function p(e,t){return f||(f=s()),f(e,t)}function h(){return f||(f=s()),f.getInitialState()}function m(t){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];function r(e){let a=e[t];return"undefined"===typeof a&&n&&(a=Ln(d,r,h)),a}function a(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:u;const r=Ln(c,n,()=>new WeakMap);return Ln(r,t,()=>{const r={};for(const[a,l]of Object.entries(e.selectors??{}))r[a]=Un(l,t,()=>Ln(d,t,h),n);return r})}return{reducerPath:t,getSelectors:a,get selectors(){return a(r)},selectSlice:r}}const g={name:n,reducer:p,actions:i.actionCreators,caseReducers:i.sliceCaseReducersByName,getInitialState:h,...m(r),injectInto(e){let{reducerPath:t,...n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a=t??r;return e.inject({reducerPath:a,reducer:p},n),{...g,...m(a,!0)}}};return g}}function Un(e,t,n,r){function a(a){let l=t(a);"undefined"===typeof l&&r&&(l=n());for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];return e(l,...o)}return a.unwrapped=e,a}var Hn=Fn();function Bn(){}var{assign:Wn}=Object;Symbol.for("rtk-state-proxy-original");function qn(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}const Vn=Hn({name:"chat",initialState:{messages:[],isStreaming:!1,currentSessionId:null,isLoading:!1,error:null},reducers:{addMessage:(e,t)=>{e.messages.push(t.payload)},updateMessage:(e,t)=>{const n=e.messages.find(e=>e.id===t.payload.id);n&&(n.content=t.payload.content)},setStreaming:(e,t)=>{e.isStreaming=t.payload},setSessionId:(e,t)=>{e.currentSessionId=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},clearMessages:e=>{e.messages=[]},addIntermediateStep:(e,t)=>{const n=e.messages.find(e=>e.id===t.payload.messageId);n&&(n.metadata||(n.metadata={}),n.metadata.intermediateSteps||(n.metadata.intermediateSteps=[]),n.metadata.intermediateSteps.push(t.payload.step))}}}),{addMessage:Qn,updateMessage:Gn,setStreaming:Kn,setSessionId:Yn,setLoading:Xn,setError:Zn,clearMessages:Jn,addIntermediateStep:er}=Vn.actions,tr=Vn.reducer,nr=Hn({name:"papers",initialState:{papers:[],currentPaper:null,isLoading:!1,error:null,searchQuery:"",filteredPapers:[]},reducers:{setPapers:(e,t)=>{e.papers=t.payload,e.filteredPapers=t.payload},addPaper:(e,t)=>{e.papers.push(t.payload),e.filteredPapers=e.papers.filter(t=>t.title.toLowerCase().includes(e.searchQuery.toLowerCase())||t.content.toLowerCase().includes(e.searchQuery.toLowerCase()))},updatePaper:(e,t)=>{const n=e.papers.findIndex(e=>e.paper_id===t.payload.paper_id);var r;-1!==n&&(e.papers[n]=t.payload,(null===(r=e.currentPaper)||void 0===r?void 0:r.paper_id)===t.payload.paper_id&&(e.currentPaper=t.payload));e.filteredPapers=e.papers.filter(t=>t.title.toLowerCase().includes(e.searchQuery.toLowerCase())||t.content.toLowerCase().includes(e.searchQuery.toLowerCase()))},deletePaper:(e,t)=>{var n;e.papers=e.papers.filter(e=>e.paper_id!==t.payload),(null===(n=e.currentPaper)||void 0===n?void 0:n.paper_id)===t.payload&&(e.currentPaper=null),e.filteredPapers=e.papers.filter(t=>t.title.toLowerCase().includes(e.searchQuery.toLowerCase())||t.content.toLowerCase().includes(e.searchQuery.toLowerCase()))},setCurrentPaper:(e,t)=>{e.currentPaper=t.payload},updateCurrentPaperContent:(e,t)=>{if(e.currentPaper){e.currentPaper.content=t.payload,e.currentPaper.updated_at=(new Date).toISOString();const n=e.papers.findIndex(t=>t.paper_id===e.currentPaper.paper_id);-1!==n&&(e.papers[n]={...e.currentPaper})}},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload,e.filteredPapers=e.papers.filter(e=>e.title.toLowerCase().includes(t.payload.toLowerCase())||e.content.toLowerCase().includes(t.payload.toLowerCase()))}}}),{setPapers:rr,addPaper:ar,updatePaper:lr,deletePaper:ir,setCurrentPaper:or,updateCurrentPaperContent:sr,setLoading:ur,setError:cr,setSearchQuery:dr}=nr.actions,fr=nr.reducer,pr=Hn({name:"session",initialState:{currentSession:null,isConnected:!1,connectionError:null},reducers:{setCurrentSession:(e,t)=>{e.currentSession=t.payload},setConnected:(e,t)=>{e.isConnected=t.payload,t.payload&&(e.connectionError=null)},setConnectionError:(e,t)=>{e.connectionError=t.payload,t.payload&&(e.isConnected=!1)},updateSessionActivity:e=>{e.currentSession&&(e.currentSession.last_activity=(new Date).toISOString())}}}),{setCurrentSession:hr,setConnected:mr,setConnectionError:gr,updateSessionActivity:yr}=pr.actions,vr=pr.reducer,br=Hn({name:"tools",initialState:{executions:[],charts:[],searchResults:[],isExecuting:!1},reducers:{startToolExecution:(e,t)=>{const n={...t.payload,id:Date.now().toString(),status:"running",startTime:(new Date).toISOString()};e.executions.push(n),e.isExecuting=!0},updateToolExecution:(e,t)=>{const n=e.executions.find(e=>e.id===t.payload.id);n&&(Object.assign(n,t.payload.updates),"completed"!==t.payload.updates.status&&"failed"!==t.payload.updates.status||(n.endTime=(new Date).toISOString())),e.isExecuting=e.executions.some(e=>"running"===e.status||"pending"===e.status)},completeToolExecution:(e,t)=>{const n=e.executions.find(e=>e.id===t.payload.id);n&&(n.status="completed",n.output=t.payload.output,n.endTime=(new Date).toISOString()),e.isExecuting=e.executions.some(e=>"running"===e.status||"pending"===e.status)},failToolExecution:(e,t)=>{const n=e.executions.find(e=>e.id===t.payload.id);n&&(n.status="failed",n.error=t.payload.error,n.endTime=(new Date).toISOString()),e.isExecuting=e.executions.some(e=>"running"===e.status||"pending"===e.status)},addChart:(e,t)=>{const n={...t.payload,id:Date.now().toString(),created_at:(new Date).toISOString()};e.charts.push(n)},removeChart:(e,t)=>{e.charts=e.charts.filter(e=>e.id!==t.payload)},addSearchResult:(e,t)=>{const n={...t.payload,id:Date.now().toString(),timestamp:(new Date).toISOString()};e.searchResults.push(n),e.searchResults.length>10&&(e.searchResults=e.searchResults.slice(-10))},clearExecutions:e=>{e.executions=[],e.isExecuting=!1}}}),{startToolExecution:kr,updateToolExecution:wr,completeToolExecution:xr,failToolExecution:Sr,addChart:Er,removeChart:Nr,addSearchResult:Cr,clearExecutions:_r}=br.actions,jr=br.reducer,Tr=Hn({name:"ui",initialState:{notifications:[],sidebarCollapsed:!1,rightPanelCollapsed:!1,currentView:"chat",theme:"light",isLoading:!1,loadingMessage:""},reducers:{addNotification:(e,t)=>{const n={...t.payload,id:Date.now().toString(),timestamp:(new Date).toISOString()};e.notifications.push(n)},removeNotification:(e,t)=>{e.notifications=e.notifications.filter(e=>e.id!==t.payload)},clearNotifications:e=>{e.notifications=[]},toggleSidebar:e=>{e.sidebarCollapsed=!e.sidebarCollapsed},toggleRightPanel:e=>{e.rightPanelCollapsed=!e.rightPanelCollapsed},setCurrentView:(e,t)=>{e.currentView=t.payload},setTheme:(e,t)=>{e.theme=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload.isLoading,e.loadingMessage=t.payload.message||""}}}),{addNotification:Pr,removeNotification:zr,clearNotifications:Rr,toggleSidebar:Lr,toggleRightPanel:Or,setCurrentView:Ar,setTheme:Mr,setLoading:Dr}=Tr.actions,Ir=function(e){const t=function(e){const{thunk:t=!0,immutableCheck:n=!0,serializableCheck:r=!0,actionCreatorCheck:a=!0}=e??{};let l=new zn;return t&&("boolean"===typeof t?l.push(Dt):l.push(It(t.extraArgument))),l},{reducer:n,middleware:r,devTools:a=!0,duplicateMiddlewareCheck:l=!0,preloadedState:i,enhancers:o}=e||{};let s,u;if("function"===typeof n)s=n;else{if(!Rt(n))throw new Error(qn(1));s=Ot(n)}u="function"===typeof r?r(t):t();let c=At;a&&(c=Tn({trace:!1,..."object"===typeof a&&a}));const d=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>(n,r)=>{const a=e(n,r);let l=()=>{throw new Error(jt(15))};const i={getState:a.getState,dispatch:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return l(e,...n)}},o=t.map(e=>e(i));return l=At(...o)(a.dispatch),{...a,dispatch:l}}}(...u),f=Mn(d);return Lt(s,i,c(..."function"===typeof o?o(f):f()))}({reducer:{chat:tr,papers:fr,session:vr,tools:jr,ui:Tr.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),$r=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},Fr=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},Ur=e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var Hr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Br=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:s,iconNode:u,...c}=e;return(0,r.createElement)("svg",{ref:t,...Hr,width:a,height:a,stroke:n,strokeWidth:i?24*Number(l)/Number(a):l,className:Fr("lucide",o),...!s&&!Ur(c)&&{"aria-hidden":"true"},...c},[...u.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),Wr=(e,t)=>{const n=(0,r.forwardRef)((n,a)=>{let{className:l,...i}=n;return(0,r.createElement)(Br,{ref:a,iconNode:t,className:Fr(`lucide-${o=$r(e),o.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${e}`,l),...i});var o});return n.displayName=$r(e),n},qr=Wr("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),Vr=Wr("panel-left-close",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]]),Qr=Wr("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),Gr=Wr("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),Kr=Wr("wifi-off",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),Yr=Wr("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),Xr=Wr("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),Zr=Wr("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),Jr=Wr("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ea=Wr("panel-right-close",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m8 9 3 3-3 3",key:"12hl5m"}]]);var ta=n(579);const na=()=>{const e=x(),{theme:t,sidebarCollapsed:n,rightPanelCollapsed:r}=N(e=>e.ui),{currentSession:a,isConnected:l}=N(e=>e.session),{isStreaming:i}=N(e=>e.chat);return(0,ta.jsxs)("header",{className:`header ${t}`,children:[(0,ta.jsxs)("div",{className:"header-left",children:[(0,ta.jsx)("button",{className:"header-button",onClick:()=>{e(Lr())},title:n?"Show chat panel":"Hide chat panel",children:n?(0,ta.jsx)(qr,{size:20}):(0,ta.jsx)(Vr,{size:20})}),(0,ta.jsxs)("div",{className:"header-logo",children:[(0,ta.jsx)(Qr,{size:24,className:"logo-icon"}),(0,ta.jsx)("h1",{className:"logo-text",children:"Paper Agent"})]})]}),(0,ta.jsx)("div",{className:"header-center",children:a&&(0,ta.jsxs)("div",{className:"session-info",children:[(0,ta.jsxs)("div",{className:"session-status",children:[l?(0,ta.jsx)(Gr,{size:16,className:"status-icon connected"}):(0,ta.jsx)(Kr,{size:16,className:"status-icon disconnected"}),(0,ta.jsxs)("span",{className:"session-id",children:["Session: ",a.session_id.slice(0,8),"..."]})]}),i&&(0,ta.jsxs)("div",{className:"streaming-indicator",children:[(0,ta.jsx)("div",{className:"streaming-dot"}),(0,ta.jsx)("span",{children:"AI is thinking..."})]})]})}),(0,ta.jsxs)("div",{className:"header-right",children:[(0,ta.jsx)("button",{className:"header-button",onClick:()=>{e(Mr("light"===t?"dark":"light"))},title:`Switch to ${"light"===t?"dark":"light"} theme`,children:"light"===t?(0,ta.jsx)(Yr,{size:20}):(0,ta.jsx)(Xr,{size:20})}),(0,ta.jsx)("button",{className:"header-button",onClick:()=>{console.log("Export functionality to be implemented")},title:"Export data",children:(0,ta.jsx)(Zr,{size:20})}),(0,ta.jsx)("button",{className:"header-button",title:"Settings",children:(0,ta.jsx)(Jr,{size:20})}),(0,ta.jsx)("button",{className:"header-button",onClick:()=>{e(Or())},title:r?"Show context panel":"Hide context panel",children:r?(0,ta.jsx)(qr,{size:20}):(0,ta.jsx)(ea,{size:20})})]})]})},ra=Wr("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),aa=Wr("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),la=Wr("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);function ia(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var oa={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function sa(e){oa=e}var ua={exec:()=>null};function ca(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="string"==typeof e?e:e.source,r={replace:(e,t)=>{let a="string"==typeof t?t:t.source;return a=a.replace(da.caret,"$1"),n=n.replace(e,a),r},getRegex:()=>new RegExp(n,t)};return r}var da={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},fa=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,pa=/(?:[*+-]|\d{1,9}[.)])/,ha=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,ma=ca(ha).replace(/bull/g,pa).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),ga=ca(ha).replace(/bull/g,pa).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),ya=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,va=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ba=ca(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",va).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ka=ca(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,pa).getRegex(),wa="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",xa=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Sa=ca("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",xa).replace("tag",wa).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ea=ca(ya).replace("hr",fa).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",wa).getRegex(),Na={blockquote:ca(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ea).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:ba,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:fa,html:Sa,lheading:ma,list:ka,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:Ea,table:ua,text:/^[^\n]+/},Ca=ca("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",fa).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",wa).getRegex(),_a={...Na,lheading:ga,table:Ca,paragraph:ca(ya).replace("hr",fa).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Ca).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",wa).getRegex()},ja={...Na,html:ca("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",xa).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ua,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ca(ya).replace("hr",fa).replace("heading"," *#{1,6} *[^\n]").replace("lheading",ma).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Ta=/^( {2,}|\\)\n(?!\s*$)/,Pa=/[\p{P}\p{S}]/u,za=/[\s\p{P}\p{S}]/u,Ra=/[^\s\p{P}\p{S}]/u,La=ca(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,za).getRegex(),Oa=/(?!~)[\p{P}\p{S}]/u,Aa=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,Ma=ca(Aa,"u").replace(/punct/g,Pa).getRegex(),Da=ca(Aa,"u").replace(/punct/g,Oa).getRegex(),Ia="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",$a=ca(Ia,"gu").replace(/notPunctSpace/g,Ra).replace(/punctSpace/g,za).replace(/punct/g,Pa).getRegex(),Fa=ca(Ia,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,Oa).getRegex(),Ua=ca("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Ra).replace(/punctSpace/g,za).replace(/punct/g,Pa).getRegex(),Ha=ca(/\\(punct)/,"gu").replace(/punct/g,Pa).getRegex(),Ba=ca(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Wa=ca(xa).replace("(?:--\x3e|$)","--\x3e").getRegex(),qa=ca("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Wa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Va=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Qa=ca(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Va).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ga=ca(/^!?\[(label)\]\[(ref)\]/).replace("label",Va).replace("ref",va).getRegex(),Ka=ca(/^!?\[(ref)\](?:\[\])?/).replace("ref",va).getRegex(),Ya={_backpedal:ua,anyPunctuation:Ha,autolink:Ba,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:Ta,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ua,emStrongLDelim:Ma,emStrongRDelimAst:$a,emStrongRDelimUnd:Ua,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:Qa,nolink:Ka,punctuation:La,reflink:Ga,reflinkSearch:ca("reflink|nolink(?!\\()","g").replace("reflink",Ga).replace("nolink",Ka).getRegex(),tag:qa,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ua},Xa={...Ya,link:ca(/^!?\[(label)\]\((.*?)\)/).replace("label",Va).getRegex(),reflink:ca(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Va).getRegex()},Za={...Ya,emStrongRDelimAst:Fa,emStrongLDelim:Da,url:ca(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ja={...Za,br:ca(Ta).replace("{2,}","*").getRegex(),text:ca(Za.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},el={normal:Na,gfm:_a,pedantic:ja},tl={normal:Ya,gfm:Za,breaks:Ja,pedantic:Xa},nl={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},rl=e=>nl[e];function al(e,t){if(t){if(da.escapeTest.test(e))return e.replace(da.escapeReplace,rl)}else if(da.escapeTestNoEncode.test(e))return e.replace(da.escapeReplaceNoEncode,rl);return e}function ll(e){try{e=encodeURI(e).replace(da.percentDecode,"%")}catch{return null}return e}function il(e,t){let n=e.replace(da.findPipe,(e,t,n)=>{let r=!1,a=t;for(;--a>=0&&"\\"===n[a];)r=!r;return r?"|":" |"}),r=n.split(da.splitPipe),a=0;if(r[0].trim()||r.shift(),r.length>0&&!r.at(-1)?.trim()&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;a<r.length;a++)r[a]=r[a].trim().replace(da.slashPipe,"|");return r}function ol(e,t,n){let r=e.length;if(0===r)return"";let a=0;for(;a<r;){let l=e.charAt(r-a-1);if(l!==t||n){if(l===t||!n)break;a++}else a++}return e.slice(0,r-a)}function sl(e,t,n,r,a){let l=t.href,i=t.title||null,o=e[1].replace(a.other.outputLinkReplace,"$1");r.state.inLink=!0;let s={type:"!"===e[0].charAt(0)?"image":"link",raw:n,href:l,title:i,text:o,tokens:r.inlineTokens(o)};return r.state.inLink=!1,s}var ul=class{options;rules;lexer;constructor(e){this.options=e||oa}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:ol(e,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let e=t[0],n=function(e,t,n){let r=e.match(n.other.indentCodeCompensation);if(null===r)return t;let a=r[1];return t.split("\n").map(e=>{let t=e.match(n.other.beginningSpace);if(null===t)return e;let[r]=t;return r.length>=a.length?e.slice(a.length):e}).join("\n")}(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){let t=ol(e,"#");(this.options.pedantic||!t||this.rules.other.endingSpaceChar.test(t))&&(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ol(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let e=ol(t[0],"\n").split("\n"),n="",r="",a=[];for(;e.length>0;){let t,l=!1,i=[];for(t=0;t<e.length;t++)if(this.rules.other.blockquoteStart.test(e[t]))i.push(e[t]),l=!0;else{if(l)break;i.push(e[t])}e=e.slice(t);let o=i.join("\n"),s=o.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}\n${o}`:o,r=r?`${r}\n${s}`:s;let u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(s,a,!0),this.lexer.state.top=u,0===e.length)break;let c=a.at(-1);if("code"===c?.type)break;if("blockquote"===c?.type){let t=c,l=t.raw+"\n"+e.join("\n"),i=this.blockquote(l);a[a.length-1]=i,n=n.substring(0,n.length-t.raw.length)+i.raw,r=r.substring(0,r.length-t.text.length)+i.text;break}if("list"===c?.type){let t=c,l=t.raw+"\n"+e.join("\n"),i=this.list(l);a[a.length-1]=i,n=n.substring(0,n.length-c.raw.length)+i.raw,r=r.substring(0,r.length-t.raw.length)+i.raw,e=l.substring(a.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:n,tokens:a,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,a={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let l=this.rules.other.listItemRegex(n),i=!1;for(;e;){let n=!1,r="",o="";if(!(t=l.exec(e))||this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let s=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,e=>" ".repeat(3*e.length)),u=e.split("\n",1)[0],c=!s.trim(),d=0;if(this.options.pedantic?(d=2,o=s.trimStart()):c?d=t[1].length+1:(d=t[2].search(this.rules.other.nonSpaceChar),d=d>4?1:d,o=s.slice(d),d+=t[1].length),c&&this.rules.other.blankLine.test(u)&&(r+=u+"\n",e=e.substring(u.length+1),n=!0),!n){let t=this.rules.other.nextBulletRegex(d),n=this.rules.other.hrRegex(d),a=this.rules.other.fencesBeginRegex(d),l=this.rules.other.headingBeginRegex(d),i=this.rules.other.htmlBeginRegex(d);for(;e;){let f,p=e.split("\n",1)[0];if(u=p,this.options.pedantic?(u=u.replace(this.rules.other.listReplaceNesting,"  "),f=u):f=u.replace(this.rules.other.tabCharGlobal,"    "),a.test(u)||l.test(u)||i.test(u)||t.test(u)||n.test(u))break;if(f.search(this.rules.other.nonSpaceChar)>=d||!u.trim())o+="\n"+f.slice(d);else{if(c||s.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||a.test(s)||l.test(s)||n.test(s))break;o+="\n"+u}!c&&!u.trim()&&(c=!0),r+=p+"\n",e=e.substring(p.length+1),s=f.slice(d)}}a.loose||(i?a.loose=!0:this.rules.other.doubleBlankLine.test(r)&&(i=!0));let f,p=null;this.options.gfm&&(p=this.rules.other.listIsTask.exec(o),p&&(f="[ ] "!==p[0],o=o.replace(this.rules.other.listReplaceTask,""))),a.items.push({type:"list_item",raw:r,task:!!p,checked:f,loose:!1,text:o,tokens:[]}),a.raw+=r}let o=a.items.at(-1);if(!o)return;o.raw=o.raw.trimEnd(),o.text=o.text.trimEnd(),a.raw=a.raw.trimEnd();for(let e=0;e<a.items.length;e++)if(this.lexer.state.top=!1,a.items[e].tokens=this.lexer.blockTokens(a.items[e].text,[]),!a.loose){let t=a.items[e].tokens.filter(e=>"space"===e.type),n=t.length>0&&t.some(e=>this.rules.other.anyLine.test(e.raw));a.loose=n}if(a.loose)for(let e=0;e<a.items.length;e++)a.items[e].loose=!0;return a}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=il(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),a=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],l={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let e of r)this.rules.other.tableAlignRight.test(e)?l.align.push("right"):this.rules.other.tableAlignCenter.test(e)?l.align.push("center"):this.rules.other.tableAlignLeft.test(e)?l.align.push("left"):l.align.push(null);for(let e=0;e<n.length;e++)l.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:l.align[e]});for(let e of a)l.rows.push(il(e,l.header.length).map((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:l.align[t]})));return l}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;let t=ol(e.slice(0,-1),"\\");if((e.length-t.length)%2===0)return}else{let e=function(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return n>0?-2:-1}(t[2],"()");if(-2===e)return;if(e>-1){let n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){let e=this.rules.other.pedanticHrefTitle.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(n=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?n.slice(1):n.slice(1,-1)),sl(t,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=t[(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!e){let e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return sl(n,e,n[0],this.lexer,this.rules)}}emStrong(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=this.rules.inline.emStrongLDelim.exec(e);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){let n,a,l=[...r[0]].length-1,i=l,o=0,s="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+l);null!=(r=s.exec(t));){if(n=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!n)continue;if(a=[...n].length,r[3]||r[4]){i+=a;continue}if((r[5]||r[6])&&l%3&&!((l+a)%3)){o+=a;continue}if(i-=a,i>0)continue;a=Math.min(a,a+i+o);let t=[...r[0]][0].length,s=e.slice(0,l+r.index+t+a);if(Math.min(l,a)%2){let e=s.slice(1,-1);return{type:"em",raw:s,text:e,tokens:this.lexer.inlineTokens(e)}}let u=s.slice(2,-2);return{type:"strong",raw:s,text:u,tokens:this.lexer.inlineTokens(u)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," "),n=this.rules.other.nonSpaceChar.test(e),r=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return n&&r&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=t[1],n="mailto:"+e):(e=t[1],n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=t[0],n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=t[0],n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}},cl=class e{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||oa,this.options.tokenizer=this.options.tokenizer||new ul,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:da,block:el.normal,inline:tl.normal};this.options.pedantic?(t.block=el.pedantic,t.inline=tl.pedantic):this.options.gfm&&(t.block=el.gfm,this.options.breaks?t.inline=tl.breaks:t.inline=tl.gfm),this.tokenizer.rules=t}static get rules(){return{block:el,inline:tl}}static lex(t,n){return new e(n).lex(t)}static lexInline(t,n){return new e(n).inlineTokens(t)}lex(e){e=e.replace(da.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){let e=this.inlineQueue[t];this.inlineTokens(e.src,e.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(this.options.pedantic&&(e=e.replace(da.tabCharGlobal,"    ").replace(da.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let n=t.at(-1);1===r.raw.length&&void 0!==n?n.raw+="\n":t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(r=this.tokenizer.fences(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.heading(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.hr(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.blockquote(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.list(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.html(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let n=t.at(-1);"paragraph"===n?.type||"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.raw,this.inlineQueue.at(-1).src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.lheading(e)){e=e.substring(r.raw.length),t.push(r);continue}let a=e;if(this.options.extensions?.startBlock){let t,n=1/0,r=e.slice(1);this.options.extensions.startBlock.forEach(e=>{t=e.call({lexer:this},r),"number"==typeof t&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(a=e.substring(0,n+1))}if(this.state.top&&(r=this.tokenizer.paragraph(a))){let l=t.at(-1);n&&"paragraph"===l?.type?(l.raw+="\n"+r.raw,l.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):t.push(r),n=a.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===n?.type?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=e,r=null;if(this.tokens.links){let e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(n));)e.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.anyPunctuation.exec(n));)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(n));)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let a=!1,l="";for(;e;){let r;if(a||(l=""),a=!1,this.options.extensions?.inline?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.escape(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.tag(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.link(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===r.type&&"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(r=this.tokenizer.emStrong(e,n,l)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.codespan(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.br(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.del(e)){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.autolink(e)){e=e.substring(r.raw.length),t.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startInline){let t,n=1/0,r=e.slice(1);this.options.extensions.startInline.forEach(e=>{t=e.call({lexer:this},r),"number"==typeof t&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(i=e.substring(0,n+1))}if(r=this.tokenizer.inlineText(i)){e=e.substring(r.raw.length),"_"!==r.raw.slice(-1)&&(l=r.raw.slice(-1)),a=!0;let n=t.at(-1);"text"===n?.type?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return t}},dl=class{options;parser;constructor(e){this.options=e||oa}space(e){return""}code(e){let{text:t,lang:n,escaped:r}=e,a=(n||"").match(da.notSpaceStart)?.[0],l=t.replace(da.endingNewline,"")+"\n";return a?'<pre><code class="language-'+al(a)+'">'+(r?l:al(l,!0))+"</code></pre>\n":"<pre><code>"+(r?l:al(l,!0))+"</code></pre>\n"}blockquote(e){let{tokens:t}=e;return`<blockquote>\n${this.parser.parse(t)}</blockquote>\n`}html(e){let{text:t}=e;return t}heading(e){let{tokens:t,depth:n}=e;return`<h${n}>${this.parser.parseInline(t)}</h${n}>\n`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,n=e.start,r="";for(let l=0;l<e.items.length;l++){let t=e.items[l];r+=this.listitem(t)}let a=t?"ol":"ul";return"<"+a+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+a+">\n"}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?"paragraph"===e.tokens[0]?.type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+al(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox(e){let{checked:t}=e;return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){let{tokens:t}=e;return`<p>${this.parser.parseInline(t)}</p>\n`}table(e){let t="",n="";for(let a=0;a<e.header.length;a++)n+=this.tablecell(e.header[a]);t+=this.tablerow({text:n});let r="";for(let a=0;a<e.rows.length;a++){let t=e.rows[a];n="";for(let e=0;e<t.length;e++)n+=this.tablecell(t[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow(e){let{text:t}=e;return`<tr>\n${t}</tr>\n`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong(e){let{tokens:t}=e;return`<strong>${this.parser.parseInline(t)}</strong>`}em(e){let{tokens:t}=e;return`<em>${this.parser.parseInline(t)}</em>`}codespan(e){let{text:t}=e;return`<code>${al(t,!0)}</code>`}br(e){return"<br>"}del(e){let{tokens:t}=e;return`<del>${this.parser.parseInline(t)}</del>`}link(e){let{href:t,title:n,tokens:r}=e,a=this.parser.parseInline(r),l=ll(t);if(null===l)return a;t=l;let i='<a href="'+t+'"';return n&&(i+=' title="'+al(n)+'"'),i+=">"+a+"</a>",i}image(e){let{href:t,title:n,text:r,tokens:a}=e;a&&(r=this.parser.parseInline(a,this.parser.textRenderer));let l=ll(t);if(null===l)return al(r);t=l;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${al(n)}"`),i+=">",i}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:al(e.text)}},fl=class{strong(e){let{text:t}=e;return t}em(e){let{text:t}=e;return t}codespan(e){let{text:t}=e;return t}del(e){let{text:t}=e;return t}html(e){let{text:t}=e;return t}text(e){let{text:t}=e;return t}link(e){let{text:t}=e;return""+t}image(e){let{text:t}=e;return""+t}br(){return""}},pl=class e{options;renderer;textRenderer;constructor(e){this.options=e||oa,this.options.renderer=this.options.renderer||new dl,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new fl}static parse(t,n){return new e(n).parse(t)}static parseInline(t,n){return new e(n).parseInline(t)}parse(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n="";for(let r=0;r<e.length;r++){let a=e[r];if(this.options.extensions?.renderers?.[a.type]){let e=a,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}let l=a;switch(l.type){case"space":n+=this.renderer.space(l);continue;case"hr":n+=this.renderer.hr(l);continue;case"heading":n+=this.renderer.heading(l);continue;case"code":n+=this.renderer.code(l);continue;case"table":n+=this.renderer.table(l);continue;case"blockquote":n+=this.renderer.blockquote(l);continue;case"list":n+=this.renderer.list(l);continue;case"html":n+=this.renderer.html(l);continue;case"paragraph":n+=this.renderer.paragraph(l);continue;case"text":{let a=l,i=this.renderer.text(a);for(;r+1<e.length&&"text"===e[r+1].type;)a=e[++r],i+="\n"+this.renderer.text(a);n+=t?this.renderer.paragraph({type:"paragraph",raw:i,text:i,tokens:[{type:"text",raw:i,text:i,escaped:!0}]}):i;continue}default:{let e='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.renderer,n="";for(let r=0;r<e.length;r++){let a=e[r];if(this.options.extensions?.renderers?.[a.type]){let e=this.options.extensions.renderers[a.type].call({parser:this},a);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){n+=e||"";continue}}let l=a;switch(l.type){case"escape":case"text":n+=t.text(l);break;case"html":n+=t.html(l);break;case"link":n+=t.link(l);break;case"image":n+=t.image(l);break;case"strong":n+=t.strong(l);break;case"em":n+=t.em(l);break;case"codespan":n+=t.codespan(l);break;case"br":n+=t.br(l);break;case"del":n+=t.del(l);break;default:{let e='Token with "'+l.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}},hl=class{options;block;constructor(e){this.options=e||oa}static passThroughHooks=(()=>new Set(["preprocess","postprocess","processAllTokens"]))();preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?cl.lex:cl.lexInline}provideParser(){return this.block?pl.parse:pl.parseInline}},ml=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=(()=>this.parseMarkdown(!0))();parseInline=(()=>this.parseMarkdown(!1))();Parser=(()=>pl)();Renderer=(()=>dl)();TextRenderer=(()=>fl)();Lexer=(()=>cl)();Tokenizer=(()=>ul)();Hooks=(()=>hl)();constructor(){this.use(...arguments)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{let e=r;for(let r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(let r of e.rows)for(let e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{let e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{let e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach(r=>{let a=e[r].flat(1/0);n=n.concat(this.walkTokens(a,t))}):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(){let e=this.defaults.extensions||{renderers:{},childTokens:{}};for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return n.forEach(t=>{let n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(t=>{if(!t.name)throw new Error("extension name required");if("renderer"in t){let n=e.renderers[t.name];e.renderers[t.name]=n?function(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];let l=t.renderer.apply(this,r);return!1===l&&(l=n.apply(this,r)),l}:t.renderer}if("tokenizer"in t){if(!t.level||"block"!==t.level&&"inline"!==t.level)throw new Error("extension level must be 'block' or 'inline'");let n=e[t.level];n?n.unshift(t.tokenizer):e[t.level]=[t.tokenizer],t.start&&("block"===t.level?e.startBlock?e.startBlock.push(t.start):e.startBlock=[t.start]:"inline"===t.level&&(e.startInline?e.startInline.push(t.start):e.startInline=[t.start]))}"childTokens"in t&&t.childTokens&&(e.childTokens[t.name]=t.childTokens)}),n.extensions=e),t.renderer){let e=this.defaults.renderer||new dl(this.defaults);for(let n in t.renderer){if(!(n in e))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;let r=n,a=t.renderer[r],l=e[r];e[r]=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=a.apply(e,n);return!1===i&&(i=l.apply(e,n)),i||""}}n.renderer=e}if(t.tokenizer){let e=this.defaults.tokenizer||new ul(this.defaults);for(let n in t.tokenizer){if(!(n in e))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;let r=n,a=t.tokenizer[r],l=e[r];e[r]=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=a.apply(e,n);return!1===i&&(i=l.apply(e,n)),i}}n.tokenizer=e}if(t.hooks){let e=this.defaults.hooks||new hl;for(let n in t.hooks){if(!(n in e))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;let r=n,a=t.hooks[r],l=e[r];hl.passThroughHooks.has(n)?e[r]=t=>{if(this.defaults.async)return Promise.resolve(a.call(e,t)).then(t=>l.call(e,t));let n=a.call(e,t);return l.call(e,n)}:e[r]=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=a.apply(e,n);return!1===i&&(i=l.apply(e,n)),i}}n.hooks=e}if(t.walkTokens){let e=this.defaults.walkTokens,r=t.walkTokens;n.walkTokens=function(t){let n=[];return n.push(r.call(this,t)),e&&(n=n.concat(e.call(this,t))),n}}this.defaults={...this.defaults,...n}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return cl.lex(e,t??this.defaults)}parser(e,t){return pl.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{let r={...n},a={...this.defaults,...r},l=this.onError(!!a.silent,!!a.async);if(!0===this.defaults.async&&!1===r.async)return l(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||null===t)return l(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return l(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=e);let i=a.hooks?a.hooks.provideLexer():e?cl.lex:cl.lexInline,o=a.hooks?a.hooks.provideParser():e?pl.parse:pl.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then(e=>i(e,a)).then(e=>a.hooks?a.hooks.processAllTokens(e):e).then(e=>a.walkTokens?Promise.all(this.walkTokens(e,a.walkTokens)).then(()=>e):e).then(e=>o(e,a)).then(e=>a.hooks?a.hooks.postprocess(e):e).catch(l);try{a.hooks&&(t=a.hooks.preprocess(t));let e=i(t,a);a.hooks&&(e=a.hooks.processAllTokens(e)),a.walkTokens&&this.walkTokens(e,a.walkTokens);let n=o(e,a);return a.hooks&&(n=a.hooks.postprocess(n)),n}catch(s){return l(s)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let e="<p>An error occurred:</p><pre>"+al(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function gl(e,t){return ml.parse(e,t)}gl.options=gl.setOptions=function(e){return ml.setOptions(e),gl.defaults=ml.defaults,sa(gl.defaults),gl},gl.getDefaults=ia,gl.defaults=oa,gl.use=function(){return ml.use(...arguments),gl.defaults=ml.defaults,sa(gl.defaults),gl},gl.walkTokens=function(e,t){return ml.walkTokens(e,t)},gl.parseInline=ml.parseInline,gl.Parser=pl,gl.parser=pl.parse,gl.Renderer=dl,gl.TextRenderer=fl,gl.Lexer=cl,gl.lexer=cl.lex,gl.Tokenizer=ul,gl.Hooks=hl,gl.parse=gl;gl.options,gl.setOptions,gl.use,gl.walkTokens,gl.parseInline,pl.parse,cl.lex;const{entries:yl,setPrototypeOf:vl,isFrozen:bl,getPrototypeOf:kl,getOwnPropertyDescriptor:wl}=Object;let{freeze:xl,seal:Sl,create:El}=Object,{apply:Nl,construct:Cl}="undefined"!==typeof Reflect&&Reflect;xl||(xl=function(e){return e}),Sl||(Sl=function(e){return e}),Nl||(Nl=function(e,t,n){return e.apply(t,n)}),Cl||(Cl=function(e,t){return new e(...t)});const _l=Hl(Array.prototype.forEach),jl=Hl(Array.prototype.lastIndexOf),Tl=Hl(Array.prototype.pop),Pl=Hl(Array.prototype.push),zl=Hl(Array.prototype.splice),Rl=Hl(String.prototype.toLowerCase),Ll=Hl(String.prototype.toString),Ol=Hl(String.prototype.match),Al=Hl(String.prototype.replace),Ml=Hl(String.prototype.indexOf),Dl=Hl(String.prototype.trim),Il=Hl(Object.prototype.hasOwnProperty),$l=Hl(RegExp.prototype.test),Fl=(Ul=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Cl(Ul,t)});var Ul;function Hl(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return Nl(e,t,r)}}function Bl(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Rl;vl&&vl(e,null);let r=t.length;for(;r--;){let a=t[r];if("string"===typeof a){const e=n(a);e!==a&&(bl(t)||(t[r]=e),a=e)}e[a]=!0}return e}function Wl(e){for(let t=0;t<e.length;t++){Il(e,t)||(e[t]=null)}return e}function ql(e){const t=El(null);for(const[n,r]of yl(e)){Il(e,n)&&(Array.isArray(r)?t[n]=Wl(r):r&&"object"===typeof r&&r.constructor===Object?t[n]=ql(r):t[n]=r)}return t}function Vl(e,t){for(;null!==e;){const n=wl(e,t);if(n){if(n.get)return Hl(n.get);if("function"===typeof n.value)return Hl(n.value)}e=kl(e)}return function(){return null}}const Ql=xl(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Gl=xl(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Kl=xl(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Yl=xl(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Xl=xl(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Zl=xl(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Jl=xl(["#text"]),ei=xl(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),ti=xl(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),ni=xl(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ri=xl(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ai=Sl(/\{\{[\w\W]*|[\w\W]*\}\}/gm),li=Sl(/<%[\w\W]*|[\w\W]*%>/gm),ii=Sl(/\$\{[\w\W]*/gm),oi=Sl(/^data-[\-\w.\u00B7-\uFFFF]+$/),si=Sl(/^aria-[\-\w]+$/),ui=Sl(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ci=Sl(/^(?:\w+script|data):/i),di=Sl(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),fi=Sl(/^html$/i),pi=Sl(/^[a-z][.\w]*(-[.\w]+)+$/i);var hi=Object.freeze({__proto__:null,ARIA_ATTR:si,ATTR_WHITESPACE:di,CUSTOM_ELEMENT:pi,DATA_ATTR:oi,DOCTYPE_NAME:fi,ERB_EXPR:li,IS_ALLOWED_URI:ui,IS_SCRIPT_OR_DATA:ci,MUSTACHE_EXPR:ai,TMPLIT_EXPR:ii});const mi=1,gi=3,yi=7,vi=8,bi=9,ki=function(){return"undefined"===typeof window?null:window};var wi=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ki();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||t.document.nodeType!==bi||!t.Element)return n.isSupported=!1,n;let{document:r}=t;const a=r,l=a.currentScript,{DocumentFragment:i,HTMLTemplateElement:o,Node:s,Element:u,NodeFilter:c,NamedNodeMap:d=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:f,DOMParser:p,trustedTypes:h}=t,m=u.prototype,g=Vl(m,"cloneNode"),y=Vl(m,"remove"),v=Vl(m,"nextSibling"),b=Vl(m,"childNodes"),k=Vl(m,"parentNode");if("function"===typeof o){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let w,x="";const{implementation:S,createNodeIterator:E,createDocumentFragment:N,getElementsByTagName:C}=r,{importNode:_}=a;let j={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"===typeof yl&&"function"===typeof k&&S&&void 0!==S.createHTMLDocument;const{MUSTACHE_EXPR:T,ERB_EXPR:P,TMPLIT_EXPR:z,DATA_ATTR:R,ARIA_ATTR:L,IS_SCRIPT_OR_DATA:O,ATTR_WHITESPACE:A,CUSTOM_ELEMENT:M}=hi;let{IS_ALLOWED_URI:D}=hi,I=null;const $=Bl({},[...Ql,...Gl,...Kl,...Xl,...Jl]);let F=null;const U=Bl({},[...ei,...ti,...ni,...ri]);let H=Object.seal(El(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),B=null,W=null,q=!0,V=!0,Q=!1,G=!0,K=!1,Y=!0,X=!1,Z=!1,J=!1,ee=!1,te=!1,ne=!1,re=!0,ae=!1,le=!0,ie=!1,oe={},se=null;const ue=Bl({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ce=null;const de=Bl({},["audio","video","img","source","image","track"]);let fe=null;const pe=Bl({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),he="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",ge="http://www.w3.org/1999/xhtml";let ye=ge,ve=!1,be=null;const ke=Bl({},[he,me,ge],Ll);let we=Bl({},["mi","mo","mn","ms","mtext"]),xe=Bl({},["annotation-xml"]);const Se=Bl({},["title","style","font","a","script"]);let Ee=null;const Ne=["application/xhtml+xml","text/html"];let Ce=null,_e=null;const je=r.createElement("form"),Te=function(e){return e instanceof RegExp||e instanceof Function},Pe=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!_e||_e!==e){if(e&&"object"===typeof e||(e={}),e=ql(e),Ee=-1===Ne.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,Ce="application/xhtml+xml"===Ee?Ll:Rl,I=Il(e,"ALLOWED_TAGS")?Bl({},e.ALLOWED_TAGS,Ce):$,F=Il(e,"ALLOWED_ATTR")?Bl({},e.ALLOWED_ATTR,Ce):U,be=Il(e,"ALLOWED_NAMESPACES")?Bl({},e.ALLOWED_NAMESPACES,Ll):ke,fe=Il(e,"ADD_URI_SAFE_ATTR")?Bl(ql(pe),e.ADD_URI_SAFE_ATTR,Ce):pe,ce=Il(e,"ADD_DATA_URI_TAGS")?Bl(ql(de),e.ADD_DATA_URI_TAGS,Ce):de,se=Il(e,"FORBID_CONTENTS")?Bl({},e.FORBID_CONTENTS,Ce):ue,B=Il(e,"FORBID_TAGS")?Bl({},e.FORBID_TAGS,Ce):ql({}),W=Il(e,"FORBID_ATTR")?Bl({},e.FORBID_ATTR,Ce):ql({}),oe=!!Il(e,"USE_PROFILES")&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,V=!1!==e.ALLOW_DATA_ATTR,Q=e.ALLOW_UNKNOWN_PROTOCOLS||!1,G=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,K=e.SAFE_FOR_TEMPLATES||!1,Y=!1!==e.SAFE_FOR_XML,X=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=e.RETURN_TRUSTED_TYPE||!1,J=e.FORCE_BODY||!1,re=!1!==e.SANITIZE_DOM,ae=e.SANITIZE_NAMED_PROPS||!1,le=!1!==e.KEEP_CONTENT,ie=e.IN_PLACE||!1,D=e.ALLOWED_URI_REGEXP||ui,ye=e.NAMESPACE||ge,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,xe=e.HTML_INTEGRATION_POINTS||xe,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Te(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Te(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"===typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),K&&(V=!1),te&&(ee=!0),oe&&(I=Bl({},Jl),F=[],!0===oe.html&&(Bl(I,Ql),Bl(F,ei)),!0===oe.svg&&(Bl(I,Gl),Bl(F,ti),Bl(F,ri)),!0===oe.svgFilters&&(Bl(I,Kl),Bl(F,ti),Bl(F,ri)),!0===oe.mathMl&&(Bl(I,Xl),Bl(F,ni),Bl(F,ri))),e.ADD_TAGS&&(I===$&&(I=ql(I)),Bl(I,e.ADD_TAGS,Ce)),e.ADD_ATTR&&(F===U&&(F=ql(F)),Bl(F,e.ADD_ATTR,Ce)),e.ADD_URI_SAFE_ATTR&&Bl(fe,e.ADD_URI_SAFE_ATTR,Ce),e.FORBID_CONTENTS&&(se===ue&&(se=ql(se)),Bl(se,e.FORBID_CONTENTS,Ce)),le&&(I["#text"]=!0),X&&Bl(I,["html","head","body"]),I.table&&(Bl(I,["tbody"]),delete B.tbody),e.TRUSTED_TYPES_POLICY){if("function"!==typeof e.TRUSTED_TYPES_POLICY.createHTML)throw Fl('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!==typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw Fl('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');w=e.TRUSTED_TYPES_POLICY,x=w.createHTML("")}else void 0===w&&(w=function(e,t){if("object"!==typeof e||"function"!==typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(fl){return console.warn("TrustedTypes policy "+a+" could not be created."),null}}(h,l)),null!==w&&"string"===typeof x&&(x=w.createHTML(""));xl&&xl(e),_e=e}},ze=Bl({},[...Gl,...Kl,...Yl]),Re=Bl({},[...Xl,...Zl]),Le=function(e){Pl(n.removed,{element:e});try{k(e).removeChild(e)}catch(fl){y(e)}},Oe=function(e,t){try{Pl(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(fl){Pl(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ee||te)try{Le(t)}catch(fl){}else try{t.setAttribute(e,"")}catch(fl){}},Ae=function(e){let t=null,n=null;if(J)e="<remove></remove>"+e;else{const t=Ol(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Ee&&ye===ge&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=w?w.createHTML(e):e;if(ye===ge)try{t=(new p).parseFromString(a,Ee)}catch(fl){}if(!t||!t.documentElement){t=S.createDocument(ye,"template",null);try{t.documentElement.innerHTML=ve?x:a}catch(fl){}}const l=t.body||t.documentElement;return e&&n&&l.insertBefore(r.createTextNode(n),l.childNodes[0]||null),ye===ge?C.call(t,X?"html":"body")[0]:X?t.documentElement:l},Me=function(e){return E.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},De=function(e){return e instanceof f&&("string"!==typeof e.nodeName||"string"!==typeof e.textContent||"function"!==typeof e.removeChild||!(e.attributes instanceof d)||"function"!==typeof e.removeAttribute||"function"!==typeof e.setAttribute||"string"!==typeof e.namespaceURI||"function"!==typeof e.insertBefore||"function"!==typeof e.hasChildNodes)},Ie=function(e){return"function"===typeof s&&e instanceof s};function $e(e,t,r){_l(e,e=>{e.call(n,t,r,_e)})}const Fe=function(e){let t=null;if($e(j.beforeSanitizeElements,e,null),De(e))return Le(e),!0;const r=Ce(e.nodeName);if($e(j.uponSanitizeElement,e,{tagName:r,allowedTags:I}),Y&&e.hasChildNodes()&&!Ie(e.firstElementChild)&&$l(/<[/\w!]/g,e.innerHTML)&&$l(/<[/\w!]/g,e.textContent))return Le(e),!0;if(e.nodeType===yi)return Le(e),!0;if(Y&&e.nodeType===vi&&$l(/<[/\w]/g,e.data))return Le(e),!0;if(!I[r]||B[r]){if(!B[r]&&He(r)){if(H.tagNameCheck instanceof RegExp&&$l(H.tagNameCheck,r))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(r))return!1}if(le&&!se[r]){const t=k(e)||e.parentNode,n=b(e)||e.childNodes;if(n&&t){for(let r=n.length-1;r>=0;--r){const a=g(n[r],!0);a.__removalCount=(e.__removalCount||0)+1,t.insertBefore(a,v(e))}}}return Le(e),!0}return e instanceof u&&!function(e){let t=k(e);t&&t.tagName||(t={namespaceURI:ye,tagName:"template"});const n=Rl(e.tagName),r=Rl(t.tagName);return!!be[e.namespaceURI]&&(e.namespaceURI===me?t.namespaceURI===ge?"svg"===n:t.namespaceURI===he?"svg"===n&&("annotation-xml"===r||we[r]):Boolean(ze[n]):e.namespaceURI===he?t.namespaceURI===ge?"math"===n:t.namespaceURI===me?"math"===n&&xe[r]:Boolean(Re[n]):e.namespaceURI===ge?!(t.namespaceURI===me&&!xe[r])&&!(t.namespaceURI===he&&!we[r])&&!Re[n]&&(Se[n]||!ze[n]):!("application/xhtml+xml"!==Ee||!be[e.namespaceURI]))}(e)?(Le(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!$l(/<\/no(script|embed|frames)/i,e.innerHTML)?(K&&e.nodeType===gi&&(t=e.textContent,_l([T,P,z],e=>{t=Al(t,e," ")}),e.textContent!==t&&(Pl(n.removed,{element:e.cloneNode()}),e.textContent=t)),$e(j.afterSanitizeElements,e,null),!1):(Le(e),!0)},Ue=function(e,t,n){if(re&&("id"===t||"name"===t)&&(n in r||n in je))return!1;if(V&&!W[t]&&$l(R,t));else if(q&&$l(L,t));else if(!F[t]||W[t]){if(!(He(e)&&(H.tagNameCheck instanceof RegExp&&$l(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&$l(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&$l(H.tagNameCheck,n)||H.tagNameCheck instanceof Function&&H.tagNameCheck(n))))return!1}else if(fe[t]);else if($l(D,Al(n,A,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Ml(n,"data:")||!ce[e]){if(Q&&!$l(O,Al(n,A,"")));else if(n)return!1}else;return!0},He=function(e){return"annotation-xml"!==e&&Ol(e,M)},Be=function(e){$e(j.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||De(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:F,forceKeepAttr:void 0};let a=t.length;for(;a--;){const l=t[a],{name:i,namespaceURI:o,value:s}=l,u=Ce(i),c=s;let d="value"===i?c:Dl(c);if(r.attrName=u,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,$e(j.uponSanitizeAttribute,e,r),d=r.attrValue,!ae||"id"!==u&&"name"!==u||(Oe(i,e),d="user-content-"+d),Y&&$l(/((--!?|])>)|<\/(style|title)/i,d)){Oe(i,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){Oe(i,e);continue}if(!G&&$l(/\/>/i,d)){Oe(i,e);continue}K&&_l([T,P,z],e=>{d=Al(d,e," ")});const f=Ce(e.nodeName);if(Ue(f,u,d)){if(w&&"object"===typeof h&&"function"===typeof h.getAttributeType)if(o);else switch(h.getAttributeType(f,u)){case"TrustedHTML":d=w.createHTML(d);break;case"TrustedScriptURL":d=w.createScriptURL(d)}if(d!==c)try{o?e.setAttributeNS(o,i,d):e.setAttribute(i,d),De(e)?Le(e):Tl(n.removed)}catch(fl){Oe(i,e)}}else Oe(i,e)}$e(j.afterSanitizeAttributes,e,null)},We=function e(t){let n=null;const r=Me(t);for($e(j.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)$e(j.uponSanitizeShadowNode,n,null),Fe(n),Be(n),n.content instanceof i&&e(n.content);$e(j.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,l=null,o=null,u=null;if(ve=!e,ve&&(e="\x3c!--\x3e"),"string"!==typeof e&&!Ie(e)){if("function"!==typeof e.toString)throw Fl("toString is not a function");if("string"!==typeof(e=e.toString()))throw Fl("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Z||Pe(t),n.removed=[],"string"===typeof e&&(ie=!1),ie){if(e.nodeName){const t=Ce(e.nodeName);if(!I[t]||B[t])throw Fl("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)r=Ae("\x3c!----\x3e"),l=r.ownerDocument.importNode(e,!0),l.nodeType===mi&&"BODY"===l.nodeName||"HTML"===l.nodeName?r=l:r.appendChild(l);else{if(!ee&&!K&&!X&&-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;if(r=Ae(e),!r)return ee?null:ne?x:""}r&&J&&Le(r.firstChild);const c=Me(ie?e:r);for(;o=c.nextNode();)Fe(o),Be(o),o.content instanceof i&&We(o.content);if(ie)return e;if(ee){if(te)for(u=N.call(r.ownerDocument);r.firstChild;)u.appendChild(r.firstChild);else u=r;return(F.shadowroot||F.shadowrootmode)&&(u=_.call(a,u,!0)),u}let d=X?r.outerHTML:r.innerHTML;return X&&I["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&$l(fi,r.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+d),K&&_l([T,P,z],e=>{d=Al(d,e," ")}),w&&ne?w.createHTML(d):d},n.setConfig=function(){Pe(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Z=!0},n.clearConfig=function(){_e=null,Z=!1},n.isValidAttribute=function(e,t,n){_e||Pe({});const r=Ce(e),a=Ce(t);return Ue(r,a,n)},n.addHook=function(e,t){"function"===typeof t&&Pl(j[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=jl(j[e],t);return-1===n?void 0:zl(j[e],n,1)[0]}return Tl(j[e])},n.removeHooks=function(e){j[e]=[]},n.removeAllHooks=function(){j={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const xi=e=>{var t;let{message:n}=e;return(0,ta.jsxs)("div",{className:`message-bubble ${n.role}`,children:[(0,ta.jsxs)("div",{className:"message-header",children:[(0,ta.jsx)("div",{className:"message-icon",children:(()=>{switch(n.role){case"user":return(0,ta.jsx)(ra,{size:16});case"assistant":return(0,ta.jsx)(aa,{size:16});case"system":return(0,ta.jsx)(la,{size:16});default:return null}})()}),(0,ta.jsx)("div",{className:"message-role",children:"user"===n.role?"You":"assistant"===n.role?"AI Assistant":"System"})]}),(0,ta.jsx)("div",{className:"message-content",children:(()=>{if("assistant"===n.role){const e=gl.parse(n.content,{async:!1}),t=wi.sanitize(e);return(0,ta.jsx)("div",{dangerouslySetInnerHTML:{__html:t}})}return(0,ta.jsx)("div",{children:n.content})})()}),(null===(t=n.metadata)||void 0===t?void 0:t.intermediateSteps)&&n.metadata.intermediateSteps.length>0&&(0,ta.jsx)("div",{className:"message-steps",children:(0,ta.jsxs)("details",{children:[(0,ta.jsxs)("summary",{children:["Tool Executions (",n.metadata.intermediateSteps.length,")"]}),(0,ta.jsx)("div",{className:"steps-list",children:n.metadata.intermediateSteps.map((e,t)=>(0,ta.jsxs)("div",{className:"step-item",children:[(0,ta.jsxs)("div",{className:"step-header",children:[(0,ta.jsx)("span",{className:"step-type",children:e.type}),e.tool_name&&(0,ta.jsx)("span",{className:"step-tool",children:e.tool_name})]}),e.result&&(0,ta.jsx)("div",{className:"step-result",children:(0,ta.jsx)("pre",{children:JSON.stringify(e.result,null,2)})})]},t))})]})}),(0,ta.jsx)("div",{className:"message-metadata",children:(r=n.timestamp,new Date(r).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))})]});var r},Si=e=>{let{messages:t}=e;const{isStreaming:n}=N(e=>e.chat),a=(0,r.useRef)(null);return(0,r.useEffect)(()=>{(()=>{var e;null===(e=a.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})})()},[t]),(0,ta.jsxs)("div",{className:"message-list",children:[0===t.length?(0,ta.jsx)("div",{className:"empty-state",children:(0,ta.jsxs)("div",{className:"empty-state-content",children:[(0,ta.jsx)("h3",{children:"Welcome to Paper Agent!"}),(0,ta.jsx)("p",{children:"I can help you with:"}),(0,ta.jsxs)("ul",{children:[(0,ta.jsx)("li",{children:"\ud83d\udd0d Searching for research papers"}),(0,ta.jsx)("li",{children:"\ud83d\udcca Creating visualizations"}),(0,ta.jsx)("li",{children:"\ud83d\udcdd Writing and editing papers"}),(0,ta.jsx)("li",{children:"\ud83d\udcbe Managing your research data"})]}),(0,ta.jsx)("p",{children:"Start by asking me a question or describing what you'd like to research!"})]})}):(0,ta.jsxs)(ta.Fragment,{children:[t.map(e=>(0,ta.jsx)(xi,{message:e},e.id)),n&&(0,ta.jsxs)("div",{className:"typing-indicator",children:[(0,ta.jsxs)("div",{className:"typing-dots",children:[(0,ta.jsx)("div",{className:"typing-dot"}),(0,ta.jsx)("div",{className:"typing-dot"}),(0,ta.jsx)("div",{className:"typing-dot"})]}),(0,ta.jsx)("span",{children:"AI is thinking..."})]})]}),(0,ta.jsx)("div",{ref:a})]})},Ei=Wr("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Ni=Wr("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),Ci=e=>{let{value:t,onChange:n,onSend:a,onKeyPress:l,disabled:i,placeholder:o="Type your message..."}=e;const s=(0,r.useRef)(null);(0,r.useEffect)(()=>{s.current&&(s.current.style.height="auto",s.current.style.height=`${s.current.scrollHeight}px`)},[t]);return(0,ta.jsxs)("div",{className:"message-input-container",children:[(0,ta.jsxs)("div",{className:"message-input-wrapper",children:[(0,ta.jsx)("textarea",{ref:s,value:t,onChange:e=>{n(e.target.value)},onKeyPress:l,placeholder:o,disabled:i,className:"message-input",rows:1,maxLength:2e3}),(0,ta.jsx)("button",{onClick:a,disabled:i||!t.trim(),className:"send-button",title:"Send message (Enter)",children:i?(0,ta.jsx)(Ei,{size:20,className:"spinning"}):(0,ta.jsx)(Ni,{size:20})})]}),(0,ta.jsxs)("div",{className:"input-footer",children:[(0,ta.jsxs)("div",{className:"character-count",children:[t.length,"/2000"]}),(0,ta.jsx)("div",{className:"input-hint",children:"Press Enter to send, Shift+Enter for new line"})]})]})},_i=Wr("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),ji=Wr("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),Ti=Wr("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Pi=Wr("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),zi=Wr("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),Ri=Wr("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),Li=()=>{const{executions:e,isExecuting:t}=N(e=>e.tools),n=e.slice(-5);if(!t&&0===n.length)return null;const r=e=>{switch(e){case"firecrawl_search":case"deep_research":case"deep_research_status":return(0,ta.jsx)(_i,{size:14});case"airtable_overview":case"airtable_create_update":case"airtable_delete":return(0,ta.jsx)(ji,{size:14});case"create_line_chart":case"create_bar_chart":case"create_pie_chart":case"create_polar_chart":case"create_doughnut_chart":return(0,ta.jsx)(Ti,{size:14});default:return(0,ta.jsx)(Pi,{size:14})}},a=e=>{switch(e){case"running":case"pending":return(0,ta.jsx)(Ei,{size:14,className:"spinning"});case"completed":return(0,ta.jsx)(zi,{size:14});case"failed":return(0,ta.jsx)(Ri,{size:14});default:return(0,ta.jsx)(Pi,{size:14})}},l=e=>{switch(e){case"running":case"pending":return"text-blue-500";case"completed":return"text-green-500";case"failed":return"text-red-500";default:return"text-gray-500"}},i=(e,t)=>{const n=new Date(e),r=t?new Date(t):new Date,a=Math.round((r.getTime()-n.getTime())/1e3);if(a<60)return`${a}s`;return`${Math.floor(a/60)}m ${a%60}s`};return(0,ta.jsxs)("div",{className:"tool-status-list",children:[(0,ta.jsxs)("div",{className:"tool-status-header",children:[(0,ta.jsx)("h4",{children:"Tool Activity"}),t&&(0,ta.jsxs)("div",{className:"executing-indicator",children:[(0,ta.jsx)(Ei,{size:12,className:"spinning"}),(0,ta.jsx)("span",{children:"Executing..."})]})]}),(0,ta.jsx)("div",{className:"tool-status-items",children:n.map(e=>{return(0,ta.jsxs)("div",{className:"tool-status-item",children:[(0,ta.jsxs)("div",{className:"tool-info",children:[(0,ta.jsx)("div",{className:"tool-icon",children:r(e.toolName)}),(0,ta.jsxs)("div",{className:"tool-details",children:[(0,ta.jsx)("div",{className:"tool-name",children:(t=e.toolName,t.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()))}),(0,ta.jsx)("div",{className:"tool-duration",children:i(e.startTime,e.endTime)})]})]}),(0,ta.jsx)("div",{className:`tool-status ${l(e.status)}`,children:a(e.status)}),void 0!==e.progress&&"running"===e.status&&(0,ta.jsx)("div",{className:"tool-progress",children:(0,ta.jsx)("div",{className:"tool-progress-bar",style:{width:`${e.progress}%`}})})]},e.id);var t})})]})},Oi="http://localhost:8000/api/v1",Ai={session:{async create(){try{const e=await fetch(`${Oi}/sessions`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error("Failed to create session");return{data:await e.json()}}catch(e){return{error:e instanceof Error?e.message:"Unknown error"}}}},papers:{async list(){try{const e=await fetch(`${Oi}/papers`);if(!e.ok)throw new Error("Failed to fetch papers");return{data:await e.json()}}catch(e){return{error:e instanceof Error?e.message:"Unknown error"}}},async create(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{const n=await fetch(`${Oi}/papers?title=${encodeURIComponent(e)}&content=${encodeURIComponent(t)}`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!n.ok)throw new Error("Failed to create paper");return{data:await n.json()}}catch(n){return{error:n instanceof Error?n.message:"Unknown error"}}},async update(e,t,n){try{const r=new URLSearchParams;t&&r.append("title",t),n&&r.append("content",n);const a=await fetch(`${Oi}/papers/${e}?${r.toString()}`,{method:"PUT",headers:{"Content-Type":"application/json"}});if(!a.ok)throw new Error("Failed to update paper");return{data:await a.json()}}catch(r){return{error:r instanceof Error?r.message:"Unknown error"}}},async get(e){try{const t=await fetch(`${Oi}/papers/${e}`);if(!t.ok)throw new Error("Failed to fetch paper");return{data:await t.json()}}catch(t){return{error:t instanceof Error?t.message:"Unknown error"}}}},charts:{async list(){try{const e=await fetch(`${Oi}/charts`);if(!e.ok)throw new Error("Failed to fetch charts");return{data:await e.json()}}catch(e){return{error:e instanceof Error?e.message:"Unknown error"}}}},chat:{async sendMessage(e,t,n){const r=await fetch(`${Oi}/chat/stream`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,session_id:t,paper_id:n})});if(!r.ok)throw new Error("Failed to send message");return r}}},Mi=()=>{const e=x(),{messages:t,isStreaming:n,isLoading:a,currentSessionId:l}=N(e=>e.chat),{sidebarCollapsed:i}=N(e=>e.ui),{currentPaper:o}=N(e=>e.papers),[s,u]=(0,r.useState)(""),c=async()=>{if(!s.trim()||n||a)return;const t={id:Date.now().toString(),role:"user",content:s.trim(),timestamp:(new Date).toISOString()};e(Qn(t)),u(""),e(Xn(!0));try{var r;const t=Date.now().toString();e(Qn({id:t,role:"assistant",content:"",timestamp:(new Date).toISOString()})),e(Xn(!1)),e(Kn(!0));const n=await Ai.chat.sendMessage(s.trim(),l||"",null===o||void 0===o?void 0:o.paper_id);if(!n.ok)throw new Error("Failed to send message");const a=null===(r=n.body)||void 0===r?void 0:r.getReader(),i=new TextDecoder;let u="";if(a)for(;;){const{done:n,value:r}=await a.read();if(n)break;const l=i.decode(r).split("\n");for(const a of l)if(a.startsWith("data: "))try{const n=JSON.parse(a.slice(6));switch(n.event){case"start":break;case"step":"tool_call"===n.data.type&&e(Pr({type:"info",title:"Tool Execution",message:`Executing ${n.data.tool_name}...`,duration:3e3}));break;case"response":n.data.message&&(u=n.data.message,e(Gn({id:t,content:u})));break;case"complete":return void e(Kn(!1));case"error":return e(Kn(!1)),void e(Pr({type:"error",title:"Chat Error",message:n.data.error||"An error occurred",duration:5e3}))}}catch(gs){}}e(Kn(!1))}catch(i){e(Xn(!1)),e(Pr({type:"error",title:"Error",message:"Failed to send message",duration:5e3}))}};return i?(0,ta.jsx)("div",{className:"chat-panel-collapsed",children:(0,ta.jsx)("div",{className:"collapsed-icon",children:(0,ta.jsx)(Ni,{size:20})})}):(0,ta.jsxs)("div",{className:"chat-panel-content",children:[(0,ta.jsxs)("div",{className:"chat-header",children:[(0,ta.jsx)("h2",{children:"Chat"}),o&&(0,ta.jsx)("div",{className:"current-paper-indicator",children:(0,ta.jsxs)("span",{children:["\ud83d\udcc4 ",o.title]})})]}),(0,ta.jsxs)("div",{className:"chat-body",children:[(0,ta.jsx)(Si,{messages:t}),(0,ta.jsx)(Li,{})]}),(0,ta.jsx)("div",{className:"chat-footer",children:(0,ta.jsx)(Ci,{value:s,onChange:u,onSend:c,onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),c())},disabled:n||a,placeholder:"Ask me anything about research papers..."})})]})},Di=Wr("split",[["path",{d:"M16 3h5v5",key:"1806ms"}],["path",{d:"M8 3H3v5",key:"15dfkv"}],["path",{d:"M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3",key:"1qrqzj"}],["path",{d:"m15 9 6-6",key:"ko1vev"}]]),Ii=Wr("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),$i=Wr("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Fi=Wr("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]]),Ui=Wr("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function Hi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Bi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Wi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bi(Object(n),!0).forEach(function(t){Hi(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bi(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function qi(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Vi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Qi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Gi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Ki(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gi(Object(n),!0).forEach(function(t){Qi(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gi(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Yi(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];return t.apply(n,[].concat(a,r))}}}function Xi(e){return{}.toString.call(e).includes("Object")}function Zi(e){return"function"===typeof e}var Ji=Yi(function(e,t){throw new Error(e[t]||e.default)})({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),eo={changes:function(e,t){return Xi(t)||Ji("changeType"),Object.keys(t).some(function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r})&&Ji("changeField"),t},selector:function(e){Zi(e)||Ji("selectorType")},handler:function(e){Zi(e)||Xi(e)||Ji("handlerType"),Xi(e)&&Object.values(e).some(function(e){return!Zi(e)})&&Ji("handlersType")},initial:function(e){var t;e||Ji("initialIsRequired"),Xi(e)||Ji("initialType"),t=e,Object.keys(t).length||Ji("initialContent")}};function to(e,t){return Zi(t)?t(e.current):t}function no(e,t){return e.current=Ki(Ki({},e.current),t),t}function ro(e,t,n){return Zi(t)?t(e.current):Object.keys(n).forEach(function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])}),n}const ao={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};eo.initial(e),eo.handler(t);var n={current:e},r=Yi(ro)(n,t),a=Yi(no)(n),l=Yi(eo.changes)(e),i=Yi(to)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return eo.selector(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}}(r,a,l,i)(e)}]}};const lo={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};const io=function(e){return{}.toString.call(e).includes("Object")};var oo={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},so=function(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];return t.apply(n,[].concat(a,r))}}}(function(e,t){throw new Error(e[t]||e.default)})(oo),uo={config:function(e){return e||so("configIsRequired"),io(e)||so("configType"),e.urls?(console.warn(oo.deprecation),{paths:{vs:e.urls.monacoBase}}):e}};const co=uo;const fo=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight(function(e,t){return t(e)},e)}};const po=function e(t,n){return Object.keys(n).forEach(function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))}),Wi(Wi({},t),n)};var ho={type:"cancelation",msg:"operation is manually canceled"};const mo=function(e){var t=!1,n=new Promise(function(n,r){e.then(function(e){return t?r(ho):n(e)}),e.catch(r)});return n.cancel=function(){return t=!0},n};var go,yo,vo=ao.create({config:lo,isInitialized:!1,resolve:null,reject:null,monaco:null}),bo=(yo=2,function(e){if(Array.isArray(e))return e}(go=vo)||function(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,a=!1,l=void 0;try{for(var i,o=e[Symbol.iterator]();!(r=(i=o.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(s){a=!0,l=s}finally{try{r||null==o.return||o.return()}finally{if(a)throw l}}return n}}(go,yo)||function(e,t){if(e){if("string"===typeof e)return Vi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Vi(e,t):void 0}}(go,yo)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),ko=bo[0],wo=bo[1];function xo(e){return document.body.appendChild(e)}function So(e){var t=ko(function(e){return{config:e.config,reject:e.reject}}),n=function(e){var t=document.createElement("script");return e&&(t.src=e),t}("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function Eo(){var e=ko(function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(t){No(t),e.resolve(t)},function(t){e.reject(t)})}function No(e){ko().monaco||wo({monaco:e})}var Co=new Promise(function(e,t){return wo({resolve:e,reject:t})}),_o={config:function(e){var t=co.config(e),n=t.monaco,r=qi(t,["monaco"]);wo(function(e){return{config:po(e.config,r),monaco:n}})},init:function(){var e=ko(function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}});if(!e.isInitialized){if(wo({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),mo(Co);if(window.monaco&&window.monaco.editor)return No(window.monaco),e.resolve(window.monaco),mo(Co);fo(xo,So)(Eo)}return mo(Co)},__getMonacoInstance:function(){return ko(function(e){return e.monaco})}};const jo=_o;var To={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},Po={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var zo=function(e){let{children:t}=e;return r.createElement("div",{style:Po.container},t)};var Ro=function(e){let{width:t,height:n,isEditorReady:a,loading:l,_ref:i,className:o,wrapperProps:s}=e;return r.createElement("section",{style:{...To.wrapper,width:t,height:n},...s},!a&&r.createElement(zo,null,l),r.createElement("div",{ref:i,style:{...To.fullWidth,...!a&&To.hide},className:o}))},Lo=(0,r.memo)(Ro);var Oo=function(e){(0,r.useEffect)(e,[])};var Ao=function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=(0,r.useRef)(!0);(0,r.useEffect)(a.current||!n?()=>{a.current=!1}:e,t)};function Mo(){}function Do(e,t,n,r){return function(e,t){return e.editor.getModel(Io(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?Io(e,r):void 0)}(e,t,n,r)}function Io(e,t){return e.Uri.parse(t)}var $o=function(e){let{original:t,modified:n,language:a,originalLanguage:l,modifiedLanguage:i,originalModelPath:o,modifiedModelPath:s,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:c=!1,theme:d="light",loading:f="Loading...",options:p={},height:h="100%",width:m="100%",className:g,wrapperProps:y={},beforeMount:v=Mo,onMount:b=Mo}=e,[k,w]=(0,r.useState)(!1),[x,S]=(0,r.useState)(!0),E=(0,r.useRef)(null),N=(0,r.useRef)(null),C=(0,r.useRef)(null),_=(0,r.useRef)(b),j=(0,r.useRef)(v),T=(0,r.useRef)(!1);Oo(()=>{let e=jo.init();return e.then(e=>(N.current=e)&&S(!1)).catch(e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e)),()=>E.current?function(){let e=E.current?.getModel();u||e?.original?.dispose(),c||e?.modified?.dispose(),E.current?.dispose()}():e.cancel()}),Ao(()=>{if(E.current&&N.current){let e=E.current.getOriginalEditor(),n=Do(N.current,t||"",l||a||"text",o||"");n!==e.getModel()&&e.setModel(n)}},[o],k),Ao(()=>{if(E.current&&N.current){let e=E.current.getModifiedEditor(),t=Do(N.current,n||"",i||a||"text",s||"");t!==e.getModel()&&e.setModel(t)}},[s],k),Ao(()=>{let e=E.current.getModifiedEditor();e.getOption(N.current.editor.EditorOption.readOnly)?e.setValue(n||""):n!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:n||"",forceMoveMarkers:!0}]),e.pushUndoStop())},[n],k),Ao(()=>{E.current?.getModel()?.original.setValue(t||"")},[t],k),Ao(()=>{let{original:e,modified:t}=E.current.getModel();N.current.editor.setModelLanguage(e,l||a||"text"),N.current.editor.setModelLanguage(t,i||a||"text")},[a,l,i],k),Ao(()=>{N.current?.editor.setTheme(d)},[d],k),Ao(()=>{E.current?.updateOptions(p)},[p],k);let P=(0,r.useCallback)(()=>{if(!N.current)return;j.current(N.current);let e=Do(N.current,t||"",l||a||"text",o||""),r=Do(N.current,n||"",i||a||"text",s||"");E.current?.setModel({original:e,modified:r})},[a,n,i,t,l,o,s]),z=(0,r.useCallback)(()=>{!T.current&&C.current&&(E.current=N.current.editor.createDiffEditor(C.current,{automaticLayout:!0,...p}),P(),N.current?.editor.setTheme(d),w(!0),T.current=!0)},[p,d,P]);return(0,r.useEffect)(()=>{k&&_.current(E.current,N.current)},[k]),(0,r.useEffect)(()=>{!x&&!k&&z()},[x,k,z]),r.createElement(Lo,{width:m,height:h,isEditorReady:k,loading:f,_ref:C,className:g,wrapperProps:y})};(0,r.memo)($o);var Fo=function(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current},Uo=new Map;var Ho=function(e){let{defaultValue:t,defaultLanguage:n,defaultPath:a,value:l,language:i,path:o,theme:s="light",line:u,loading:c="Loading...",options:d={},overrideServices:f={},saveViewState:p=!0,keepCurrentModel:h=!1,width:m="100%",height:g="100%",className:y,wrapperProps:v={},beforeMount:b=Mo,onMount:k=Mo,onChange:w,onValidate:x=Mo}=e,[S,E]=(0,r.useState)(!1),[N,C]=(0,r.useState)(!0),_=(0,r.useRef)(null),j=(0,r.useRef)(null),T=(0,r.useRef)(null),P=(0,r.useRef)(k),z=(0,r.useRef)(b),R=(0,r.useRef)(),L=(0,r.useRef)(l),O=Fo(o),A=(0,r.useRef)(!1),M=(0,r.useRef)(!1);Oo(()=>{let e=jo.init();return e.then(e=>(_.current=e)&&C(!1)).catch(e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e)),()=>j.current?(R.current?.dispose(),h?p&&Uo.set(o,j.current.saveViewState()):j.current.getModel()?.dispose(),void j.current.dispose()):e.cancel()}),Ao(()=>{let e=Do(_.current,t||l||"",n||i||"",o||a||"");e!==j.current?.getModel()&&(p&&Uo.set(O,j.current?.saveViewState()),j.current?.setModel(e),p&&j.current?.restoreViewState(Uo.get(o)))},[o],S),Ao(()=>{j.current?.updateOptions(d)},[d],S),Ao(()=>{!j.current||void 0===l||(j.current.getOption(_.current.editor.EditorOption.readOnly)?j.current.setValue(l):l!==j.current.getValue()&&(M.current=!0,j.current.executeEdits("",[{range:j.current.getModel().getFullModelRange(),text:l,forceMoveMarkers:!0}]),j.current.pushUndoStop(),M.current=!1))},[l],S),Ao(()=>{let e=j.current?.getModel();e&&i&&_.current?.editor.setModelLanguage(e,i)},[i],S),Ao(()=>{void 0!==u&&j.current?.revealLine(u)},[u],S),Ao(()=>{_.current?.editor.setTheme(s)},[s],S);let D=(0,r.useCallback)(()=>{if(T.current&&_.current&&!A.current){z.current(_.current);let e=o||a,r=Do(_.current,l||t||"",n||i||"",e||"");j.current=_.current?.editor.create(T.current,{model:r,automaticLayout:!0,...d},f),p&&j.current.restoreViewState(Uo.get(e)),_.current.editor.setTheme(s),void 0!==u&&j.current.revealLine(u),E(!0),A.current=!0}},[t,n,a,l,i,o,d,f,p,s,u]);return(0,r.useEffect)(()=>{S&&P.current(j.current,_.current)},[S]),(0,r.useEffect)(()=>{!N&&!S&&D()},[N,S,D]),L.current=l,(0,r.useEffect)(()=>{S&&w&&(R.current?.dispose(),R.current=j.current?.onDidChangeModelContent(e=>{M.current||w(j.current.getValue(),e)}))},[S,w]),(0,r.useEffect)(()=>{if(S){let e=_.current.editor.onDidChangeMarkers(e=>{let t=j.current.getModel()?.uri;if(t&&e.find(e=>e.path===t.path)){let e=_.current.editor.getModelMarkers({resource:t});x?.(e)}});return()=>{e?.dispose()}}return()=>{}},[S,x]),r.createElement(Lo,{width:m,height:g,isEditorReady:S,loading:c,_ref:T,className:y,wrapperProps:v})},Bo=(0,r.memo)(Ho);const Wo=e=>{let{paper:t}=e;const n=x(),[a,l]=(0,r.useState)(t.content),[i,o]=(0,r.useState)(!1),[s,u]=(0,r.useState)(!0),[c,d]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!1),[h,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{l(t.content),p(!1)},[t.paper_id,t.content]),(0,r.useEffect)(()=>{p(a!==t.content)},[a,t.content]);const g=(0,r.useCallback)(e=>{const t=e||"";l(t),n(sr(t))},[n]),y=async()=>{if(f){d(!0);try{const e=await Ai.papers.update(t.paper_id,t.title,a);if(!e.data)throw new Error(e.error||"Failed to save");p(!1),n(Pr({type:"success",title:"Paper Saved",message:"Your changes have been saved successfully.",duration:3e3}))}catch(e){n(Pr({type:"error",title:"Save Failed",message:"Failed to save your changes. Please try again.",duration:5e3}))}finally{d(!1)}}},v=()=>{const e=gl.parse(a,{async:!1}),t=wi.sanitize(e);return(0,ta.jsx)("div",{dangerouslySetInnerHTML:{__html:t}})},b=()=>a.trim().split(/\s+/).filter(e=>e.length>0).length,k=()=>{s?(o(!i),u(!1)):(u(!0),o(!1))};return(0,r.useEffect)(()=>{if(f){const e=setTimeout(()=>{y()},2e3);return()=>clearTimeout(e)}},[a,f]),(0,r.useEffect)(()=>{const e=e=>{(e.ctrlKey||e.metaKey)&&"s"===e.key&&(e.preventDefault(),y()),(e.ctrlKey||e.metaKey)&&"p"===e.key&&(e.preventDefault(),k())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),(0,ta.jsxs)("div",{className:"paper-editor "+(h?"fullscreen":""),children:[(0,ta.jsxs)("div",{className:"editor-header",children:[(0,ta.jsxs)("div",{className:"editor-title",children:[(0,ta.jsx)("h2",{children:t.title}),(0,ta.jsxs)("div",{className:"editor-meta",children:[(0,ta.jsxs)("span",{className:"word-count",children:[b()," words"]}),(0,ta.jsx)("span",{className:"status-indicator",children:f?(0,ta.jsx)("span",{className:"unsaved",children:"\u25cf Unsaved changes"}):(0,ta.jsx)("span",{className:"saved",children:"\u2713 Saved"})})]})]}),(0,ta.jsxs)("div",{className:"editor-actions",children:[(0,ta.jsxs)("button",{onClick:k,className:"editor-button",title:s?"Toggle single view":i?"Edit mode":"Preview mode",children:[s?(0,ta.jsx)(Di,{size:16}):i?(0,ta.jsx)(Ii,{size:16}):(0,ta.jsx)($i,{size:16}),s?"Split":i?"Edit":"Preview"]}),(0,ta.jsxs)("button",{onClick:()=>{m(!h)},className:"editor-button",title:h?"Exit fullscreen":"Enter fullscreen",children:[(0,ta.jsx)(Fi,{size:16}),h?"Exit":"Fullscreen"]}),(0,ta.jsxs)("button",{onClick:y,disabled:!f||c,className:"editor-button save-button",title:"Save changes (Ctrl+S)",children:[(0,ta.jsx)(Ui,{size:16}),c?"Saving...":"Save"]}),(0,ta.jsxs)("button",{onClick:()=>{const e=new Blob([a],{type:"text/markdown"}),n=URL.createObjectURL(e),r=document.createElement("a");r.href=n,r.download=`${t.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.md`,r.click(),URL.revokeObjectURL(n)},className:"editor-button",title:"Export as Markdown",children:[(0,ta.jsx)(Zr,{size:16}),"Export"]})]})]}),(0,ta.jsx)("div",{className:"editor-content",children:s?(0,ta.jsxs)("div",{className:"split-view",children:[(0,ta.jsx)("div",{className:"edit-pane",children:(0,ta.jsx)(Bo,{height:"100%",defaultLanguage:"markdown",value:a,onChange:g,theme:"vs-light",options:{minimap:{enabled:!1},wordWrap:"on",lineNumbers:"on",scrollBeyondLastLine:!1,automaticLayout:!0,fontSize:14,fontFamily:'Monaco, Menlo, "Ubuntu Mono", monospace',padding:{top:16,bottom:16},suggest:{showKeywords:!1,showSnippets:!1}}})}),(0,ta.jsx)("div",{className:"preview-pane",children:(0,ta.jsx)("div",{className:"markdown-content",children:v()})})]}):i?(0,ta.jsx)("div",{className:"preview-pane full",children:(0,ta.jsx)("div",{className:"markdown-content",children:v()})}):(0,ta.jsx)("div",{className:"edit-pane full",children:(0,ta.jsx)(Bo,{height:"100%",defaultLanguage:"markdown",value:a,onChange:g,theme:"vs-light",options:{minimap:{enabled:!0},wordWrap:"on",lineNumbers:"on",scrollBeyondLastLine:!1,automaticLayout:!0,fontSize:16,fontFamily:'Monaco, Menlo, "Ubuntu Mono", monospace',padding:{top:20,bottom:20}}})})}),(0,ta.jsxs)("div",{className:"editor-footer",children:[(0,ta.jsxs)("div",{className:"editor-stats",children:[(0,ta.jsxs)("span",{children:["Characters: ",a.length]}),(0,ta.jsxs)("span",{children:["Words: ",b()]}),(0,ta.jsxs)("span",{children:["Lines: ",a.split("\n").length]})]}),(0,ta.jsx)("div",{className:"editor-help",children:(0,ta.jsx)("span",{children:"Markdown supported \u2022 Ctrl+S to save \u2022 Ctrl+P to toggle view"})})]})]})},qo=Wr("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),Vo=Wr("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),Qo=Wr("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),Go=Wr("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),Ko=e=>{let{charts:t}=e;const[n,a]=(0,r.useState)(t[0]||null),[l,i]=(0,r.useState)(!1),o=e=>{switch(e){case"line":return(0,ta.jsx)(qo,{size:20});case"bar":default:return(0,ta.jsx)(Ti,{size:20});case"pie":return(0,ta.jsx)(Vo,{size:20});case"doughnut":return(0,ta.jsx)(Qo,{size:20})}},s=e=>{window.open(e.url,"_blank")};return 0===t.length?(0,ta.jsxs)("div",{className:"chart-viewer-empty",children:[(0,ta.jsx)(Ti,{size:64,className:"empty-icon"}),(0,ta.jsx)("h3",{children:"No Charts Available"}),(0,ta.jsx)("p",{children:"Ask the AI to create visualizations from your research data."})]}):(0,ta.jsxs)("div",{className:"chart-viewer",children:[(0,ta.jsxs)("div",{className:"chart-sidebar",children:[(0,ta.jsxs)("h3",{className:"sidebar-title",children:["Charts (",t.length,")"]}),(0,ta.jsx)("div",{className:"chart-list",children:t.map(e=>(0,ta.jsxs)("div",{className:"chart-list-item "+((null===n||void 0===n?void 0:n.id)===e.id?"active":""),onClick:()=>(e=>{a(e),i(!1)})(e),children:[(0,ta.jsx)("div",{className:"chart-list-icon",children:o(e.type)}),(0,ta.jsxs)("div",{className:"chart-list-content",children:[(0,ta.jsx)("div",{className:"chart-list-title",children:e.title}),(0,ta.jsxs)("div",{className:"chart-list-type",children:[e.type," chart"]})]})]},e.id))})]}),(0,ta.jsx)("div",{className:"chart-main",children:n?(0,ta.jsxs)(ta.Fragment,{children:[(0,ta.jsxs)("div",{className:"chart-header",children:[(0,ta.jsxs)("div",{className:"chart-info",children:[(0,ta.jsx)("h2",{className:"chart-title",children:n.title}),(0,ta.jsxs)("div",{className:"chart-meta",children:[(0,ta.jsxs)("span",{className:"chart-type",children:[o(n.type),n.type," chart"]}),(0,ta.jsxs)("span",{className:"chart-date",children:["Created ",new Date(n.created_at).toLocaleDateString()]})]})]}),(0,ta.jsxs)("div",{className:"chart-actions",children:[(0,ta.jsxs)("button",{onClick:()=>s(n),className:"chart-action-button",title:"Open in new tab",children:[(0,ta.jsx)(Go,{size:16}),"Open"]}),(0,ta.jsxs)("button",{onClick:()=>(e=>{const t=document.createElement("a");t.href=e.url,t.download=`${e.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.png`,t.click()})(n),className:"chart-action-button",title:"Download chart",children:[(0,ta.jsx)(Zr,{size:16}),"Download"]})]})]}),(0,ta.jsxs)("div",{className:"chart-display",children:[(0,ta.jsx)("img",{src:n.url,alt:n.title,className:"chart-image "+(l?"zoomed":""),onClick:()=>{i(!l)},onError:e=>{var t;const n=e.target;n.style.display="none",null===(t=n.nextElementSibling)||void 0===t||t.classList.remove("hidden")}}),(0,ta.jsxs)("div",{className:"chart-error hidden",children:[(0,ta.jsx)(Ti,{size:48}),(0,ta.jsx)("h4",{children:"Failed to Load Chart"}),(0,ta.jsx)("p",{children:"The chart image could not be loaded."}),(0,ta.jsx)("button",{onClick:()=>s(n),className:"retry-button",children:"Try Opening in New Tab"})]})]}),n.data&&(0,ta.jsx)("div",{className:"chart-data",children:(0,ta.jsxs)("details",{children:[(0,ta.jsx)("summary",{children:"Chart Data"}),(0,ta.jsx)("pre",{className:"data-preview",children:JSON.stringify(n.data,null,2)})]})})]}):(0,ta.jsxs)("div",{className:"no-chart-selected",children:[(0,ta.jsx)(Ti,{size:64,className:"empty-icon"}),(0,ta.jsx)("h3",{children:"Select a Chart"}),(0,ta.jsx)("p",{children:"Choose a chart from the sidebar to view it here."})]})})]})},Yo=Wr("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),Xo=Wr("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),Zo=e=>{let{results:t}=e;const[n,a]=(0,r.useState)(t[0]||null),l=e=>new Date(e).toLocaleDateString();return 0===t.length?(0,ta.jsxs)("div",{className:"search-results-empty",children:[(0,ta.jsx)(_i,{size:64,className:"empty-icon"}),(0,ta.jsx)("h3",{children:"No Search Results"}),(0,ta.jsx)("p",{children:"Use the chat to search for research papers and view results here."})]}):(0,ta.jsxs)("div",{className:"search-results",children:[(0,ta.jsxs)("div",{className:"results-sidebar",children:[(0,ta.jsx)("h3",{className:"sidebar-title",children:"Search Results"}),(0,ta.jsx)("div",{className:"results-list",children:t.map(e=>(0,ta.jsxs)("div",{className:"result-list-item "+((null===n||void 0===n?void 0:n.id)===e.id?"active":""),onClick:()=>a(e),children:[(0,ta.jsxs)("div",{className:"result-list-header",children:[(0,ta.jsx)("div",{className:"result-query",children:e.query}),(0,ta.jsxs)("div",{className:"result-count",children:[e.count," results"]})]}),(0,ta.jsxs)("div",{className:"result-meta",children:[(0,ta.jsx)("span",{className:"result-source",children:e.source}),(0,ta.jsx)("span",{className:"result-date",children:l(e.timestamp)})]})]},e.id))})]}),(0,ta.jsx)("div",{className:"results-main",children:n?(0,ta.jsxs)(ta.Fragment,{children:[(0,ta.jsx)("div",{className:"results-header",children:(0,ta.jsxs)("div",{className:"results-info",children:[(0,ta.jsxs)("h2",{className:"results-title",children:['"',n.query,'"']}),(0,ta.jsxs)("div",{className:"results-meta",children:[(0,ta.jsxs)("span",{className:"results-count",children:[n.count," results found"]}),(0,ta.jsxs)("span",{className:"results-source",children:[(0,ta.jsx)(Yo,{size:16}),n.source]}),(0,ta.jsxs)("span",{className:"results-date",children:[(0,ta.jsx)(Xo,{size:16}),l(n.timestamp)]})]})]})}),(0,ta.jsx)("div",{className:"results-content",children:n.results.map((e,t)=>(0,ta.jsxs)("div",{className:"result-item",children:[(0,ta.jsxs)("div",{className:"result-item-header",children:[(0,ta.jsx)("h4",{className:"result-title",children:e.title||`Result ${t+1}`}),e.url&&(0,ta.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"result-link",title:"Open in new tab",children:(0,ta.jsx)(Go,{size:16})})]}),e.description&&(0,ta.jsx)("p",{className:"result-description",children:e.description}),e.snippet&&(0,ta.jsx)("div",{className:"result-snippet",children:(0,ta.jsx)("p",{children:e.snippet})}),e.authors&&(0,ta.jsxs)("div",{className:"result-authors",children:[(0,ta.jsx)("strong",{children:"Authors: <AUTHORS>
//# sourceMappingURL=main.0810a1c7.js.map