{"version": 3, "file": "static/css/main.4f461ab4.css", "mappings": "AASA,KACE,uEAEF,CCZA,QAKE,kBAAmB,CAHnB,qBAAuB,CACvB,+BAAgC,CAKhC,8BAAwC,CAJxC,YAAa,CAHb,WAAY,CAKZ,6BAA8B,CAC9B,cAAe,CAEf,UACF,CAEA,aACE,wBAAyB,CACzB,2BAA4B,CAC5B,UACF,CAEA,2BAGE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,eACE,QAAO,CAEP,sBAEF,CAGA,4BAJE,kBAAmB,CAFnB,YAWF,CALA,aAGE,OAAQ,CACR,gBACF,CAEA,WACE,aACF,CAEA,WAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,wBACE,UACF,CAGA,eAEE,kBAAmB,CAKnB,wBAA6B,CAD7B,WAAY,CAEZ,iBAAkB,CAGlB,aAAc,CAFd,cAAe,CARf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAOvB,uBAAyB,CANzB,UAQF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,4BACE,aACF,CAEA,kCACE,wBAAyB,CACzB,aACF,CAGA,cAGE,kBAAmB,CACnB,OACF,CAEA,gBAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,cAAe,CADf,OAGF,CAEA,6BACE,aACF,CAEA,uBACE,aACF,CAEA,0BACE,aACF,CAEA,YACE,8CAAwD,CACxD,cACF,CAGA,qBAEE,kBAAmB,CAInB,2BAA4B,CAD5B,aAAc,CAJd,YAAa,CAGb,cAAe,CADf,OAIF,CAEA,eAKE,6BAA8B,CAF9B,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAEA,iBACE,OACE,SACF,CACA,OACE,UACF,CACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,yBACE,QACE,cACF,CAEA,2BAEE,OACF,CAMA,yBACE,YACF,CAEA,eAEE,WAAY,CADZ,UAEF,CACF,CAEA,yBACE,QACE,aACF,CAEA,2BAEE,OACF,CAEA,eAEE,WAAY,CADZ,UAEF,CAEA,mBAEE,WAAY,CADZ,UAEF,CACF,CAGA,qBACE,yBAA0B,CAC1B,kBACF,CAGA,sBACE,oBACF,CAGA,sBACE,iBACF,CAEA,kCAME,wBAAyB,CAGzB,iBAAkB,CANlB,YAAa,CAIb,UAAY,CANZ,mBAAoB,CASpB,cAAe,CANf,QAAS,CAIT,eAAgB,CAKhB,mBAAoB,CAXpB,iBAAkB,CAGlB,0BAA2B,CAM3B,kBAAmB,CACnB,WAEF,CAEA,+CACE,wBACF,CCzOA,oBAIE,qBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAFtB,WAIF,CAEA,sBAGE,kBAAmB,CAEnB,wBAAyB,CACzB,8BAA+B,CAJ/B,YAAa,CADb,WAAY,CAGZ,sBAGF,CAEA,gBAIE,qBAAuB,CADvB,iBAAkB,CAElB,8BAAwC,CAJxC,aAAc,CACd,YAIF,CAGA,aAGE,wBAAyB,CADzB,+BAAgC,CADhC,iBAGF,CAEA,gBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,yBAGE,qBAAuB,CAGvB,wBAAyB,CADzB,iBAAkB,CAHlB,aAAc,CADd,cAAe,CAGf,eAGF,CAEA,8BACE,aAAc,CAId,eAAgB,CAFhB,eAAgB,CAChB,sBAAuB,CAFvB,kBAIF,CAGA,WAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAGA,aAGE,qBAAuB,CADvB,4BAA6B,CAD7B,iBAGF,CAGA,cAIE,YAAa,CAHb,QAAO,CAIP,qBAAsB,CACtB,QAAS,CAJT,eAAgB,CAChB,iBAIF,CAEA,iCACE,SACF,CAEA,uCACE,kBACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,6CACE,kBACF,CAGA,gBAME,oBAAqB,CAHrB,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAJhB,aAAc,CACd,iBAKF,CAEA,qBACE,mBAAoB,CACpB,wBAAyB,CAEzB,8BAA+B,CAD/B,UAEF,CAEA,0BACE,qBAAsB,CACtB,wBAAyB,CAGzB,wBAAyB,CADzB,6BAA8B,CAD9B,aAGF,CAEA,uBACE,iBAAkB,CAClB,wBAAyB,CAEzB,wBAAyB,CADzB,aAAc,CAEd,cAAe,CACf,aAAc,CACd,iBACF,CAGA,kBAEE,aAAc,CADd,cAAe,CAEf,cAAe,CACf,gBACF,CAEA,4CACE,eACF,CAGA,kBAEE,kBAAmB,CAQnB,qBAAsB,CALtB,wBAAyB,CAGzB,wBAAyB,CAFzB,kBAAmB,CACnB,6BAA8B,CAN9B,YAAa,CAEb,OAAQ,CAMR,aAAc,CALd,iBAOF,CAEA,aACE,YAAa,CACb,OACF,CAEA,YAKE,0CAA2C,CAF3C,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAEA,wBACE,qBACF,CAEA,yBACE,qBACF,CAEA,kBAEE,UAIE,UAAY,CADZ,mBAEF,CAEA,IAEE,SAAU,CADV,kBAEF,CACF,CAGA,kBAGE,wBAAyB,CADzB,4BAA6B,CAE7B,gBAAiB,CACjB,eAAgB,CAJhB,gBAKF,CAEA,kBAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAIb,cAAe,CAFf,OAAQ,CACR,aAGF,CAEA,kBAIE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAJvB,UAKF,CAEA,0BAEE,iCAAkC,CADlC,aAEF,CAEA,4BACE,aACF,CAEA,yBACE,aACF,CAaA,eAGE,wBAAyB,CACzB,iBAAkB,CAHlB,QAAO,CACP,UAAW,CAIX,YAAa,CADb,eAEF,CAEA,mBAEE,wBAAyB,CAEzB,iBAAkB,CAHlB,WAAY,CAEZ,yBAEF,CAGA,yBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,uBAEE,oBAAqB,CAGrB,wBAAyB,CAEzB,wBAAyB,CADzB,kBAAmB,CALnB,YAAa,CAEb,OAAQ,CACR,YAIF,CAEA,eAME,qBAAuB,CADvB,WAAY,CAEZ,iBAAkB,CANlB,QAAO,CAOP,cAAe,CACf,eAAgB,CANhB,gBAAiB,CADjB,eAAgB,CAShB,YAAa,CAPb,gBAAiB,CAMjB,WAEF,CAEA,qBACE,4BACF,CAEA,aAOE,kBAAmB,CAHnB,wBAAyB,CADzB,iBAAkB,CAElB,UAAY,CACZ,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CACvB,uBAAyB,CARzB,UASF,CAEA,kCACE,wBACF,CAEA,sBACE,wBAAyB,CACzB,kBACF,CAEA,cAGE,kBAAmB,CAEnB,aAAc,CAJd,YAAa,CAGb,cAAe,CAFf,6BAA8B,CAI9B,aACF,CAEA,iBACE,8CACF,CAEA,YACE,iBACF,CAGA,aACE,iBAGF,CAEA,wBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,uBAEE,cAAe,CADf,eAEF,CAEA,wBAGE,kBAAwB,CADxB,eAAgB,CAEhB,iBAAkB,CAHlB,eAIF,CAEA,wBAEE,cAAe,CADf,iBAEF,CAGA,gBAOE,aAAc,CAFd,cAAe,CACf,eAAgB,CAHhB,OAAQ,CACR,iBAIF,CAEA,8BARE,kBAAmB,CADnB,YAeF,CANA,cAEE,WAAY,CAGZ,sBAAuB,CAJvB,UAKF,CAEA,cAEE,mBAAqB,CADrB,wBAEF,CAEA,iBACE,eACF,CAEA,wHAOE,aAAc,CADd,iBAEF,CAEA,mBACE,YACF,CAEA,wCAEE,YAAa,CACb,iBACF,CAEA,sBACE,0BAAoC,CAEpC,iBAAkB,CAClB,cAAgB,CAFhB,eAGF,CAEA,qBACE,0BAAqC,CAErC,iBAAkB,CAElB,YAAa,CADb,eAAgB,CAFhB,YAIF,CAEA,eAEE,4BAA6B,CAD7B,eAAgB,CAEhB,eACF,CAEA,uBAIE,aAAc,CAHd,cAAe,CACf,cAAe,CACf,eAAgB,CAEhB,aACF,CAEA,YACE,cAAe,CACf,iBACF,CAEA,WAEE,cAAe,CADf,iBAEF,CAEA,aACE,YAAa,CACb,OAAQ,CACR,iBACF,CAEA,WACE,wBAAyB,CACzB,aAAc,CAGd,eACF,CAEA,sBAJE,iBAAkB,CADlB,eAUF,CALA,WACE,wBAAyB,CACzB,aAGF,CAEA,aACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,cAAe,CADf,WAEF,CAEA,iBAEE,cAAe,CADf,QAAS,CAET,oBAAqB,CACrB,oBACF,CAGA,yBASE,wCACE,iBACF,CAEA,gBAGE,cAAe,CAFf,aAAc,CACd,iBAEF,CAEA,8BACE,eACF,CAEA,uBACE,WACF,CAEA,aAEE,WAAY,CADZ,UAEF,CAEA,aACE,iBACF,CACF,CAGA,0BACE,wBAAyB,CACzB,UACF,CAEA,mBACE,wBAAyB,CACzB,2BACF,CAEA,sBACE,UACF,CAQA,+DALE,wBAAyB,CACzB,oBAAqB,CACrB,aAOF,CAEA,mBACE,wBAAyB,CACzB,wBACF,CC3iBA,cAIE,qBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,iBACF,CAEA,yBAOE,qBAAuB,CAFvB,QAAS,CAFT,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAIN,YAEF,CAEA,eAGE,sBAAuB,CAGvB,wBAAyB,CADzB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,iBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,aAIE,aAAc,CAHd,YAAa,CAEb,cAAe,CADf,QAGF,CAEA,YACE,eACF,CAEA,2BACE,aAAc,CACd,eACF,CAEA,yBACE,aAAc,CACd,eACF,CAEA,gBACE,YAAa,CACb,OACF,CAEA,eAEE,kBAAmB,CAInB,qBAAuB,CADvB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAId,cAAe,CAVf,YAAa,CAQb,cAAe,CACf,eAAgB,CAPhB,OAAQ,CACR,gBAAiB,CAQjB,uBACF,CAEA,oCAGE,wBAAyB,CAFzB,oBAAqB,CACrB,aAEF,CAEA,wBAEE,kBAAmB,CADnB,UAEF,CAEA,4BACE,wBAAyB,CAEzB,oBAAqB,CADrB,UAEF,CAEA,kCACE,wBAAyB,CACzB,oBACF,CAEA,gBACE,QAAO,CAGP,qBACF,CAEA,4BAJE,YAAa,CADb,eASF,CAJA,YACE,QAGF,CAEA,yBAIE,YAAa,CAFb,QAAO,CAGP,qBAAsB,CAFtB,eAGF,CAEA,mCAEE,QACF,CAEA,iDAGE,8BAA+B,CAD/B,QAEF,CAEA,0BAEE,6BAA8B,CAD9B,iBAEF,CAEA,iBAUE,qBAAuB,CANvB,WAAY,CAKZ,aAAc,CARd,QAAO,CAKP,mEAAmF,CACnF,cAAe,CACf,eAAgB,CAHhB,YAAa,CAFb,YAAa,CAQb,WAAY,CATZ,UAUF,CAEA,8BACE,aACF,CAEA,kBAIE,qBAAuB,CAHvB,QAAO,CACP,eAAgB,CAChB,YAEF,CAEA,8HAOE,aAAc,CACd,eAAgB,CAFhB,kBAGF,CAEA,qBAEE,+BAAgC,CADhC,cAAe,CAEf,kBACF,CAEA,qBAEE,+BAAgC,CADhC,cAAe,CAEf,kBACF,CAEA,qBACE,cACF,CAEA,qBACE,cACF,CAEA,oBAGE,aAAc,CADd,eAAgB,CADhB,aAGF,CAEA,0CAEE,aAAc,CACd,iBACF,CAEA,qBAEE,eAAgB,CADhB,YAEF,CAEA,6BAIE,wBAAyB,CADzB,6BAA8B,CAE9B,aAAc,CACd,iBAAkB,CALlB,aAAc,CACd,iBAKF,CAEA,uBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAId,8CAAwD,CADxD,cAAgB,CAFhB,eAIF,CAEA,sBACE,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAId,aAAc,CADd,eAAgB,CAFhB,YAIF,CAEA,2BACE,wBAA6B,CAG7B,eAAgB,CAFhB,aAAc,CACd,SAEF,CAEA,wBAEE,wBAAyB,CACzB,aAAc,CAFd,UAGF,CAEA,0CAEE,wBAAyB,CACzB,gBAAiB,CACjB,eACF,CAEA,qBACE,wBAAyB,CACzB,eACF,CAEA,eAGE,kBAAmB,CAGnB,wBAAyB,CADzB,4BAA6B,CAG7B,aAAc,CAPd,YAAa,CAMb,cAAe,CALf,6BAA8B,CAE9B,iBAKF,CAEA,cACE,YAAa,CACb,QACF,CAEA,aACE,iBACF,CAGA,yBACE,eAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,gBACE,wBACF,CAEA,eAEE,cAAe,CADf,gBAEF,CAEA,iBAEE,cAAe,CADf,YAEF,CAEA,kBACE,YACF,CAEA,eAGE,sBAAuB,CAFvB,qBAAsB,CACtB,OAEF,CAEA,cACE,QACF,CACF,CAGA,oBACE,wBACF,CAEA,qBACE,wBAAyB,CACzB,2BACF,CAEA,uBACE,aACF,CAEA,mBACE,aACF,CAEA,qBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,0CAGE,0BAAyC,CAFzC,oBAAqB,CACrB,aAEF,CAEA,uBAEE,aACF,CAEA,+CAJE,wBAMF,CAEA,kKAME,aACF,CAMA,sDACE,2BACF,CAEA,0BACE,aACF,CAEA,mCACE,0BAAyC,CACzC,yBAA0B,CAC1B,aACF,CAEA,6BACE,wBAAyB,CACzB,aACF,CAEA,2BACE,wBACF,CAEA,sDAEE,oBACF,CAEA,qBACE,wBAAyB,CACzB,wBAAyB,CACzB,aACF,CChZA,cAGE,qBAAuB,CADvB,YAAa,CADb,WAGF,CAEA,uCAKE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,sBAAuB,CAGvB,YAAa,CAFb,iBAGF,CAEA,+DAEE,aAAc,CACd,kBACF,CAEA,6CAKE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,2CAGE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,eAEF,CAEA,eAIE,wBAAyB,CADzB,8BAA+B,CAE/B,YAAa,CACb,qBAAsB,CAJtB,eAAgB,CADhB,WAMF,CAEA,eAME,+BAAgC,CADhC,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAAS,CADT,YAMF,CAEA,YACE,QAAO,CACP,eAAgB,CAChB,YACF,CAEA,iBAEE,kBAAmB,CAOnB,qBAAuB,CACvB,wBAAyB,CALzB,iBAAkB,CAClB,cAAe,CALf,YAAa,CAEb,QAAS,CAKT,iBAAkB,CAJlB,YAAa,CAGb,uBAIF,CAEA,uBACE,oBAAqB,CACrB,8BACF,CAEA,wBAEE,wBAAyB,CADzB,oBAEF,CAEA,iBACE,aAAc,CACd,aACF,CAEA,oBACE,QAAO,CACP,WACF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cAAiB,CAEjB,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAEA,iBAEE,aAAc,CADd,cAAe,CAEf,yBACF,CAEA,YAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAEA,cAGE,sBAAuB,CAGvB,wBAAyB,CADzB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,YACE,QACF,CAEA,aAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,YAIE,aAAc,CAHd,YAAa,CAEb,cAAe,CADf,QAGF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAGb,eAAgB,CADhB,OAEF,CAEA,eACE,YAAa,CACb,OACF,CAEA,qBAEE,kBAAmB,CAInB,qBAAuB,CADvB,wBAAyB,CAGzB,iBAAkB,CADlB,aAAc,CAId,cAAe,CAVf,YAAa,CAQb,cAAe,CACf,eAAgB,CAPhB,OAAQ,CACR,gBAAiB,CASjB,oBAAqB,CADrB,uBAEF,CAEA,2BAGE,wBAAyB,CAFzB,oBAAqB,CACrB,aAEF,CAEA,eAIE,kBAAmB,CAGnB,qBAAuB,CAJvB,YAAa,CAFb,QAAO,CAIP,sBAAuB,CAHvB,eAAgB,CAIhB,YAEF,CAEA,aAIE,iBAAkB,CAClB,+BAAyC,CAEzC,cAAe,CALf,eAAgB,CADhB,cAAe,CAEf,kBAAmB,CAGnB,6BAEF,CAEA,mBACE,qBACF,CAEA,oBACE,eAAgB,CAChB,oBACF,CAEA,aAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAKtB,QAAS,CAHT,sBAAuB,CACvB,iBAGF,CAEA,oBACE,YACF,CAEA,gBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,eAEE,cAAe,CADf,QAEF,CAEA,cAEE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,gBAAiB,CAQjB,oCACF,CAEA,oBACE,wBACF,CAEA,YAEE,wBAAyB,CADzB,4BAEF,CAEA,oBACE,iBACF,CAEA,oBAGE,aAAc,CACd,cAAe,CAHf,cAAe,CACf,eAAgB,CAGhB,aACF,CAEA,cAGE,wBAAyB,CAEzB,iBAAkB,CADlB,aAAc,CAEd,cAAe,CALf,eAAkB,CAOlB,gBAAiB,CADjB,eAAgB,CAEhB,eAAgB,CAPhB,YAQF,CAGA,yBACE,cACE,qBACF,CAEA,eAIE,+BAAgC,CADhC,iBAAkB,CAElB,gBAAiB,CAHjB,cAAe,CADf,UAKF,CAEA,YACE,YAAa,CACb,OAAQ,CACR,eAAgB,CAChB,YACF,CAEA,iBAEE,eAAgB,CADhB,eAEF,CAEA,cAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,eACE,wBACF,CAEA,eACE,YACF,CACF,CAGA,oBACE,wBACF,CAEA,qBACE,wBAAyB,CACzB,0BACF,CAEA,qBAEE,2BAA4B,CAD5B,aAEF,CAEA,uBACE,wBAAyB,CACzB,oBACF,CAEA,6BACE,oBACF,CAEA,8BACE,0BAAyC,CACzC,oBACF,CAEA,wBACE,aACF,CAEA,oBACE,wBAAyB,CACzB,2BACF,CAEA,mBACE,aACF,CAEA,2BACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,iCAGE,0BAAyC,CAFzC,oBAAqB,CACrB,aAEF,CAEA,qBACE,wBACF,CAEA,sBACE,aACF,CAEA,kBACE,wBAAyB,CACzB,wBACF,CAEA,0BACE,aACF,CCvYA,gBACE,WAIF,CAEA,kBAEE,wBAAyB,CADzB,+BAAgC,CAEhC,cACF,CAEA,WACE,YAAa,CACb,KACF,CAEA,KAGE,wBAA6B,CAK7B,WAAoC,CAApC,6BAAoC,CAJpC,aAAc,CAGd,cAAe,CAFf,cAAe,CACf,eAAgB,CALhB,iBAAkB,CASlB,iBAAkB,CADlB,uBAEF,CAEA,WAEE,0BAA0C,CAD1C,aAEF,CAEA,YAGE,qBAAuB,CADvB,2BAA4B,CAD5B,aAGF,CAEA,mBAOE,qBAAuB,CANvB,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,mBACE,QAAO,CACP,eAAgB,CAChB,iBACF,CAGA,iDAME,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,sBAAuB,CAGvB,YAAa,CAFb,iBAGF,CAEA,0DAME,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,uDAIE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,eAEF,CAGA,gBAIE,kBAAmB,CAInB,kDAA6D,CAN7D,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,sBAAuB,CAEvB,YAAa,CADb,iBAGF,CAEA,iBACE,eACF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,kBAEE,aAAc,CADd,cAAe,CAGf,eAAgB,CADhB,eAEF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,aACF,CAEA,cACE,qBAAuB,CAIvB,wBAAyB,CAFzB,kBAAmB,CACnB,8BAAwC,CAFxC,YAAa,CAIb,uBACF,CAEA,oBAEE,+BAA0C,CAD1C,0BAEF,CAEA,cAOE,kBAAmB,CAJnB,wBAAyB,CAEzB,kBAAmB,CADnB,UAAY,CAEZ,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CACvB,kBAAwB,CARxB,UASF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cACF,CAEA,qBAEE,aAAc,CADd,cAAe,CAGf,eAAgB,CADhB,QAEF,CAEA,aACE,eACF,CAEA,UAEE,aAAc,CADd,cAAe,CAEf,eACF,CAEA,iBACE,YAAa,CACb,cAAe,CACf,OAAQ,CACR,sBACF,CAEA,eACE,qBAAuB,CACvB,wBAAyB,CACzB,kBAAmB,CAGnB,aAAc,CACd,cAAe,CAFf,cAAe,CADf,gBAAiB,CAIjB,uBACF,CAEA,qBACE,wBAAyB,CACzB,UAAY,CACZ,0BACF,CAGA,yBACE,WAGE,uBAAwB,CAFxB,eAAgB,CAChB,oBAEF,CAEA,8BACE,YACF,CAEA,KAEE,cAAe,CADf,iBAAkB,CAElB,kBACF,CAEA,kBACE,cACF,CAEA,gBACE,YACF,CAEA,eACE,cACF,CAEA,kBACE,cACF,CAEA,kBAEE,QAAS,CADT,yBAEF,CAEA,cACE,YACF,CAEA,iBAEE,kBAAmB,CADnB,qBAEF,CAEA,eAEE,eAAgB,CAChB,iBAAkB,CAFlB,UAGF,CACF,CAGA,sBACE,wBACF,CAEA,wBACE,wBAAyB,CACzB,2BACF,CAEA,WACE,aACF,CAEA,iBAEE,0BAAyC,CADzC,aAEF,CAEA,kBAEE,wBAAyB,CADzB,aAEF,CAEA,4EAGE,aACF,CAEA,sBACE,kDACF,CAEA,qBACE,aACF,CAEA,oBACE,wBAAyB,CACzB,oBACF,CAEA,qBACE,aACF,CAEA,qBACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CAEA,2BACE,wBAAyB,CACzB,UACF,CCxTA,YAIE,qBACF,CAEA,mBAGE,wBACF,CAEA,kBAEE,cAAe,CADf,eAIF,CAEA,wBACE,kBACF,CAEA,sBAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,aAGE,aAAc,CADd,SAAU,CADV,iBAAkB,CAGlB,SACF,CAEA,cAME,qBAAuB,CAFvB,iBAAkB,CAFlB,2BAA4B,CAK5B,uBACF,CAEA,oBAGE,8BACF,CAEA,qBAEE,kBAAmB,CAInB,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAXf,YAAa,CASb,cAAe,CACf,eAAgB,CARhB,OAAQ,CAER,iBAAkB,CAQlB,uBAAyB,CATzB,UAUF,CAEA,2BACE,wBAAyB,CACzB,0BACF,CAEA,oBAGE,iBACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CAMb,iBACF,CAEA,kBAGE,0BACF,CAEA,qCAJE,+BAQF,CAEA,mBAGE,sBAAuB,CAFvB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,kBAME,QAAO,CALP,cAAe,CAIf,eAAgB,CAEhB,iBACF,CAEA,mBAEE,iBAAkB,CAKlB,aAAc,CAJd,cAAe,CACf,eAAgB,CAEhB,mBAAqB,CALrB,eAOF,CAiBA,oBACE,kBACF,CAEA,oBAME,oBAAqB,CACrB,2BAA4B,CAL5B,aAAc,CAGd,mBAAoB,CAJpB,cAAe,CAEf,eAAgB,CAChB,QAAS,CAIT,eACF,CAEA,iBAKE,aACF,CAEA,iBACE,eACF,CAEA,kBACE,wBAAyB,CAEzB,iBAAkB,CAClB,eAAgB,CAFhB,eAGF,CAGA,aAGE,kBAAmB,CAInB,aAAc,CANd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAEvB,iBAAkB,CADlB,iBAGF,CAEA,YACE,aAAc,CACd,kBACF,CAEA,gBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,eAEE,cAAe,CAEf,eAAgB,CAHhB,eAAkB,CAElB,eAEF,CAEA,oBAEE,kBAAmB,CAGnB,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAVf,YAAa,CAQb,cAAe,CACf,eAAgB,CAPhB,OAAQ,CACR,iBAAkB,CAQlB,uBACF,CAEA,0BACE,wBAAyB,CACzB,0BACF,CAGA,eAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAEF,CAEA,iBAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAQA,yBAKE,uCACE,iBACF,CAEA,YACE,YACF,CAEA,kBACE,cACF,CAEA,oBAEE,oBAAqB,CADrB,cAEF,CAEA,aACE,iBACF,CACF,CAGA,kBACE,wBACF,CAEA,yBACE,wBAAyB,CACzB,2BACF,CAEA,wBACE,aACF,CAQA,0BACE,oBAAqB,CACrB,8BACF,CAOA,wBAEE,+BACF,CAWA,0BACE,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,aACF,CCtVA,uBAIE,qBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAFtB,WAIF,CAEA,yBAIE,kBAAmB,CACnB,wBAAyB,CACzB,6BAA8B,CAJ9B,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAMZ,cACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,eAIE,kBAAmB,CAGnB,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAHxC,aAAc,CAId,cAAe,CAPf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAMvB,uBAAyB,CAVzB,UAWF,CAEA,qBACE,aAAc,CACd,0BACF,CAGA,sBAEE,wBAAyB,CADzB,+BAEF,CAEA,cAIE,uBAAwB,CAHxB,YAAa,CACb,eAAgB,CAChB,oBAEF,CAEA,iCACE,YACF,CAEA,aAEE,kBAAmB,CAInB,wBAA6B,CAK7B,WAAoC,CAApC,6BAAoC,CAJpC,aAAc,CAGd,cAAe,CATf,YAAa,CAOb,cAAe,CACf,eAAgB,CANhB,OAAQ,CAWR,qBAAsB,CAVtB,iBAAkB,CAQlB,uBAAyB,CACzB,kBAEF,CAEA,mBAEE,0BAA0C,CAD1C,aAEF,CAEA,oBAGE,qBAAuB,CADvB,2BAA4B,CAD5B,aAGF,CAEA,kBACE,cACF,CAGA,oBAGE,YAAa,CAFb,QAAO,CAGP,qBAAsB,CAFtB,eAGF,CAGA,YAGE,aAAc,CACd,cAAe,CAHf,YAAa,CACb,iBAGF,CAGA,YAEE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAEA,mBAGE,qBAAuB,CADvB,+BAAgC,CADhC,iBAGF,CAEA,kBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,cAME,wBAAyB,CAHzB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,gBAAiB,CADjB,UAMF,CAEA,oBAGE,qBAAuB,CADvB,oBAAqB,CADrB,YAGF,CAEA,oBACE,QAAO,CACP,eAAgB,CAChB,iBACF,CAEA,YAOE,qBAAuB,CALvB,wBAAyB,CACzB,iBAAkB,CAElB,cAAe,CADf,iBAAkB,CAHlB,YAAa,CAKb,uBAEF,CAEA,kBACE,oBAAqB,CACrB,8BACF,CAEA,mBAEE,wBAAyB,CADzB,oBAEF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,eAAgB,CADhB,cAEF,CAEA,iBAKE,kBAAmB,CAHnB,aAAc,CACd,YAAa,CAFb,cAAe,CAGf,6BAEF,CAEA,mBAEE,iBAAkB,CAClB,cAAe,CACf,eAAgB,CAHhB,eAAgB,CAIhB,wBACF,CAEA,yBACE,wBAAyB,CACzB,aACF,CAEA,+BACE,wBAAyB,CACzB,aACF,CAEA,6BACE,wBAAyB,CACzB,aACF,CAGA,eAEE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAEA,sBAGE,qBAAuB,CADvB,+BAAgC,CADhC,iBAGF,CAEA,qBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,uBACE,QAAO,CACP,eAAgB,CAChB,iBACF,CAEA,YAKE,qBAAuB,CAHvB,wBAAyB,CACzB,iBAAkB,CAFlB,kBAAmB,CAGnB,eAEF,CAEA,mBAEE,wBAAyB,CACzB,+BAAgC,CAFhC,YAGF,CAEA,kBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cACF,CAEA,iBAEE,aAAc,CADd,cAAe,CAEf,yBACF,CAEA,kBAGE,aAAc,CADd,WAAY,CADZ,UAGF,CAGA,cAEE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAEA,qBAGE,qBAAuB,CADvB,+BAAgC,CADhC,iBAGF,CAEA,oBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,sBACE,QAAO,CACP,eAAgB,CAChB,iBACF,CAEA,gBACE,kBACF,CAEA,sBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,iBAAkB,CADlB,wBAEF,CAEA,sBAEE,aAAc,CACd,8CAAwD,CAFxD,cAGF,CAGA,yBACE,aAEE,cAAe,CADf,iBAEF,CAEA,kBACE,YACF,CAEA,8DAGE,iBACF,CAEA,iEAGE,gBACF,CACF,CAGA,6BACE,wBACF,CAEA,4BACE,wBAAyB,CACzB,2BACF,CAEA,mBACE,aACF,CAEA,yBAEE,0BAAyC,CADzC,aAEF,CAEA,0BAEE,wBAAyB,CADzB,aAEF,CAEA,6EAGE,aACF,CAEA,oBAGE,aACF,CAEA,sCALE,wBAAyB,CACzB,oBAOF,CAEA,wBACE,oBACF,CAEA,yBACE,0BAAyC,CACzC,oBACF,CAEA,wBACE,aACF,CAEA,kBACE,wBAAyB,CACzB,oBACF,CAEA,yBACE,wBAAyB,CACzB,2BACF,CAEA,wBACE,aACF,CChZA,wBAKE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,eAAgB,CAPhB,cAAe,CAEf,UAAW,CADX,QAAS,CAOT,UAAW,CALX,YAMF,CAEA,cAEE,sBAAuB,CAOvB,8BAAgC,CAJhC,qBAAuB,CAGvB,qBAAsB,CAFtB,iBAAkB,CAClB,+BAA0C,CAN1C,YAAa,CAEb,QAAS,CACT,YAAa,CAMb,iBACF,CAEA,sBACE,yBACF,CAEA,oBACE,yBACF,CAEA,sBACE,yBACF,CAEA,mBACE,yBACF,CAEA,mBACE,aAAc,CACd,cACF,CAEA,yCACE,aACF,CAEA,uCACE,aACF,CAEA,yCACE,aACF,CAEA,sCACE,aACF,CAEA,sBACE,QAAO,CACP,WACF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,eAAgB,CADhB,iBAEF,CAEA,sBAIE,oBAAqB,CAFrB,aAAc,CADd,cAAe,CAEf,eAEF,CAEA,oBAUE,kBAAmB,CALnB,wBAA6B,CAD7B,WAAY,CAIZ,iBAAkB,CAFlB,aAAc,CACd,cAAe,CAEf,YAAa,CARb,aAAc,CAEd,WAAY,CAQZ,sBAAuB,CAEvB,eAAgB,CADhB,uBAAyB,CAVzB,UAYF,CAEA,0BACE,wBAAyB,CACzB,aACF,CAGA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,oBACE,GAEE,SAAU,CADV,uBAEF,CACA,GAEE,SAAU,CADV,0BAEF,CACF,CAEA,uBACE,uCACF,CAGA,oBAQE,yBAA0B,CAF1B,6BAA8B,CAH9B,QAAS,CAFT,UAAW,CAIX,UAAW,CADX,MAAO,CAGP,UAAY,CALZ,iBAOF,CAEA,4BACE,wBAEF,CAEA,sDAHE,qBAAuC,CAAvC,qCAMF,CAHA,0BACE,wBAEF,CAEA,4BAEE,qBAAuC,CAAvC,qCAAuC,CADvC,wBAEF,CAEA,yBAEE,qBAAuC,CAAvC,qCAAuC,CADvC,wBAEF,CAEA,oBACE,GACE,UACF,CACA,GACE,OACF,CACF,CAGA,yBACE,wBAGE,SAAU,CACV,cAAe,CAFf,UAAW,CADX,QAIF,CAEA,cAEE,QAAS,CADT,YAEF,CAEA,oBACE,cACF,CAEA,sBACE,cACF,CACF,CAEA,yBACE,wBAGE,QAAS,CADT,SAAU,CADV,QAGF,CAEA,cAEE,OAAQ,CADR,YAEF,CACF,CAGA,oBACE,wBAAyB,CACzB,aACF,CAEA,0BACE,aACF,CAEA,4BACE,aACF,CAEA,0BACE,aACF,CAEA,gCACE,wBAAyB,CACzB,aACF,CAGA,oBAEE,2BAAyC,CADzC,0BAEF,CAGA,0BACE,yBAA0B,CAC1B,kBACF,CAGA,+BACE,cACE,gBACF,CAEA,sBACE,oBACF,CAEA,oBACE,oBACF,CAEA,sBACE,oBACF,CAEA,mBACE,oBACF,CACF,CAGA,uCAKE,kCACE,cACF,CAEA,oBACE,cACF,CACF,CCtRA,QAIE,wBAAyB,CAFzB,YAAa,CACb,qBAAsB,CAFtB,YAIF,CAEA,aAEE,YAAa,CADb,QAAO,CAEP,eACF,CAGA,YAIE,qBAAuB,CACvB,8BAA+B,CAE/B,YAAa,CACb,qBAAsB,CALtB,eAAgB,CADhB,eAAgB,CAIhB,uBAAyB,CALzB,WAQF,CAEA,sBAEE,cAAe,CADf,UAEF,CAGA,gBACE,QAAO,CAIP,eACF,CAGA,+BAPE,qBAAuB,CACvB,YAAa,CACb,qBAcF,CATA,eAKE,6BAA8B,CAF9B,eAAgB,CADhB,eAAgB,CAIhB,uBAAyB,CALzB,WAQF,CAEA,yBAEE,cAAe,CADf,UAEF,CAGA,0BACE,eACE,WACF,CACF,CAEA,yBACE,aACE,iBACF,CAEA,YAME,+BAAyC,CAFzC,WAAY,CAFZ,MAAO,CADP,iBAAkB,CAElB,KAAM,CAIN,2BAA4B,CAF5B,WAGF,CAEA,4BACE,uBACF,CAEA,eAME,gCAA0C,CAF1C,WAAY,CAHZ,iBAAkB,CAClB,OAAQ,CACR,KAAM,CAIN,0BAA2B,CAF3B,WAGF,CAEA,+BACE,uBACF,CACF,CAEA,yBACE,2BAGE,cAAe,CADf,UAEF,CACF,CAGA,eAEE,wBAA6B,CAC7B,iBAAkB,CAClB,iBAAkB,CAHlB,SAIF,CAEA,qBACE,wBACF,CAEA,qBAME,QAAS,CALT,UAAW,CAGX,SAAU,CAFV,iBAAkB,CAGlB,UAAW,CAFX,KAIF,CAGA,2CAGE,0CACF,CAGA,qDAEE,gCACF,CAGA,gBACE,mBACF,CAEA,sBAOE,sBAA0C,CAD1C,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAKN,YACF,CCxJA,EACE,qBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,aAAc,CANd,mIAEY,CAHZ,QAQF,CAEA,KACE,mEACF,CAEA,KACE,YAAa,CACb,eACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,qDAIE,yBAA0B,CAC1B,kBACF,CAGA,OAEE,eAAgB,CADhB,WAAY,CAEZ,cAAe,CACf,mBACF,CAGA,eAEE,mBAAoB,CACpB,iBACF,CAGA,UACE,iCACF,CAEA,gBACE,GACE,sBACF,CAEA,GACE,uBACF,CACF,CAEA,SACE,4BACF,CAEA,kBACE,GACE,SACF,CAEA,GACE,SACF,CACF,CAEA,UACE,8BACF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CAEA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,MACE,wBAAyB,CACzB,aACF,CAEA,gCACE,kBACF,CAEA,gCACE,kBACF,CAEA,sCACE,kBACF", "sources": ["index.css", "components/Layout/Header.css", "components/Chat/ChatPanel.css", "components/Editor/PaperEditor.css", "components/Charts/ChartViewer.css", "components/Layout/MainWorkArea.css", "components/Papers/PaperList.css", "components/Layout/ContextPanel.css", "components/UI/NotificationContainer.css", "components/Layout/Layout.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".header {\n  height: 60px;\n  background-color: white;\n  border-bottom: 1px solid #e2e8f0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  z-index: 50;\n}\n\n.header.dark {\n  background-color: #1e293b;\n  border-bottom-color: #334155;\n  color: white;\n}\n\n.header-left,\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.header-center {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n/* Logo */\n.header-logo {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-left: 12px;\n}\n\n.logo-icon {\n  color: #3b82f6;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.header.dark .logo-text {\n  color: white;\n}\n\n/* Header Buttons */\n.header-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background-color: transparent;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  color: #64748b;\n}\n\n.header-button:hover {\n  background-color: #f1f5f9;\n  color: #3b82f6;\n}\n\n.header.dark .header-button {\n  color: #94a3b8;\n}\n\n.header.dark .header-button:hover {\n  background-color: #334155;\n  color: #60a5fa;\n}\n\n/* Session Info */\n.session-info {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 4px;\n}\n\n.session-status {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 14px;\n  color: #64748b;\n}\n\n.header.dark .session-status {\n  color: #94a3b8;\n}\n\n.status-icon.connected {\n  color: #10b981;\n}\n\n.status-icon.disconnected {\n  color: #ef4444;\n}\n\n.session-id {\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 12px;\n}\n\n/* Streaming Indicator */\n.streaming-indicator {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  color: #3b82f6;\n  animation: pulse 2s infinite;\n}\n\n.streaming-dot {\n  width: 8px;\n  height: 8px;\n  background-color: #3b82f6;\n  border-radius: 50%;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% {\n    opacity: 1;\n  }\n  51%, 100% {\n    opacity: 0.3;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .header {\n    padding: 0 12px;\n  }\n  \n  .header-left,\n  .header-right {\n    gap: 8px;\n  }\n  \n  .logo-text {\n    display: none;\n  }\n  \n  .session-info {\n    display: none;\n  }\n  \n  .header-button {\n    width: 36px;\n    height: 36px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header {\n    padding: 0 8px;\n  }\n  \n  .header-left,\n  .header-right {\n    gap: 4px;\n  }\n  \n  .header-button {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .header-button svg {\n    width: 18px;\n    height: 18px;\n  }\n}\n\n/* Focus states for accessibility */\n.header-button:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* Active states */\n.header-button:active {\n  transform: scale(0.95);\n}\n\n/* Tooltip positioning */\n.header-button[title] {\n  position: relative;\n}\n\n.header-button[title]:hover::after {\n  content: attr(title);\n  position: absolute;\n  bottom: -35px;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #1e293b;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  z-index: 100;\n  pointer-events: none;\n}\n\n.header.dark .header-button[title]:hover::after {\n  background-color: #475569;\n}\n", ".chat-panel-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: white;\n}\n\n.chat-panel-collapsed {\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #f8fafc;\n  border-right: 1px solid #e2e8f0;\n}\n\n.collapsed-icon {\n  color: #64748b;\n  padding: 12px;\n  border-radius: 8px;\n  background-color: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n/* Chat Header */\n.chat-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.chat-header h2 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.current-paper-indicator {\n  font-size: 12px;\n  color: #64748b;\n  background-color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  border: 1px solid #e2e8f0;\n}\n\n.current-paper-indicator span {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 250px;\n}\n\n/* Chat Body */\n.chat-body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* Chat Footer */\n.chat-footer {\n  padding: 16px 20px;\n  border-top: 1px solid #e2e8f0;\n  background-color: white;\n}\n\n/* Message List Styles */\n.message-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.message-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.message-list::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n.message-list::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.message-list::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Message Bubble */\n.message-bubble {\n  max-width: 85%;\n  padding: 12px 16px;\n  border-radius: 12px;\n  font-size: 14px;\n  line-height: 1.5;\n  word-wrap: break-word;\n}\n\n.message-bubble.user {\n  align-self: flex-end;\n  background-color: #3b82f6;\n  color: white;\n  border-bottom-right-radius: 4px;\n}\n\n.message-bubble.assistant {\n  align-self: flex-start;\n  background-color: #f1f5f9;\n  color: #1e293b;\n  border-bottom-left-radius: 4px;\n  border: 1px solid #e2e8f0;\n}\n\n.message-bubble.system {\n  align-self: center;\n  background-color: #fef3c7;\n  color: #92400e;\n  border: 1px solid #fbbf24;\n  font-size: 12px;\n  max-width: 70%;\n  text-align: center;\n}\n\n/* Message Metadata */\n.message-metadata {\n  font-size: 11px;\n  color: #64748b;\n  margin-top: 4px;\n  text-align: right;\n}\n\n.message-bubble.assistant .message-metadata {\n  text-align: left;\n}\n\n/* Typing Indicator */\n.typing-indicator {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background-color: #f1f5f9;\n  border-radius: 12px;\n  border-bottom-left-radius: 4px;\n  border: 1px solid #e2e8f0;\n  max-width: 85%;\n  align-self: flex-start;\n}\n\n.typing-dots {\n  display: flex;\n  gap: 4px;\n}\n\n.typing-dot {\n  width: 6px;\n  height: 6px;\n  background-color: #64748b;\n  border-radius: 50%;\n  animation: typing 1.4s infinite ease-in-out;\n}\n\n.typing-dot:nth-child(1) {\n  animation-delay: -0.32s;\n}\n\n.typing-dot:nth-child(2) {\n  animation-delay: -0.16s;\n}\n\n@keyframes typing {\n\n  0%,\n  80%,\n  100% {\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\n\n  40% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Tool Status */\n.tool-status-list {\n  padding: 8px 20px;\n  border-top: 1px solid #f1f5f9;\n  background-color: #fafbfc;\n  max-height: 120px;\n  overflow-y: auto;\n}\n\n.tool-status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px 0;\n  font-size: 12px;\n  color: #64748b;\n}\n\n.tool-status-icon {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tool-status-icon.running {\n  color: #3b82f6;\n  animation: spin 1s linear infinite;\n}\n\n.tool-status-icon.completed {\n  color: #10b981;\n}\n\n.tool-status-icon.failed {\n  color: #ef4444;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Progress Bar */\n.tool-progress {\n  flex: 1;\n  height: 4px;\n  background-color: #e2e8f0;\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 8px;\n}\n\n.tool-progress-bar {\n  height: 100%;\n  background-color: #3b82f6;\n  transition: width 0.3s ease;\n  border-radius: 2px;\n}\n\n/* Message Input Styles */\n.message-input-container {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.message-input-wrapper {\n  display: flex;\n  align-items: flex-end;\n  gap: 8px;\n  padding: 12px;\n  background-color: #f8fafc;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n\n.message-input {\n  flex: 1;\n  min-height: 20px;\n  max-height: 120px;\n  padding: 8px 12px;\n  border: none;\n  background-color: white;\n  border-radius: 8px;\n  font-size: 14px;\n  line-height: 1.4;\n  resize: none;\n  outline: none;\n}\n\n.message-input:focus {\n  box-shadow: 0 0 0 2px #3b82f6;\n}\n\n.send-button {\n  width: 36px;\n  height: 36px;\n  border-radius: 8px;\n  background-color: #3b82f6;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.send-button:hover:not(:disabled) {\n  background-color: #2563eb;\n}\n\n.send-button:disabled {\n  background-color: #94a3b8;\n  cursor: not-allowed;\n}\n\n.input-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #64748b;\n  padding: 0 4px;\n}\n\n.character-count {\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n.input-hint {\n  font-style: italic;\n}\n\n/* Empty State Styles */\n.empty-state {\n  padding: 40px 20px;\n  text-align: center;\n  color: #64748b;\n}\n\n.empty-state-content h3 {\n  margin: 0 0 16px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.empty-state-content p {\n  margin: 0 0 16px 0;\n  font-size: 14px;\n}\n\n.empty-state-content ul {\n  text-align: left;\n  max-width: 300px;\n  margin: 0 auto 16px auto;\n  padding-left: 20px;\n}\n\n.empty-state-content li {\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n/* Message Bubble Enhancements */\n.message-header {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 8px;\n  font-size: 12px;\n  font-weight: 600;\n  color: #64748b;\n}\n\n.message-icon {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.message-role {\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.message-content {\n  line-height: 1.5;\n}\n\n.message-content h1,\n.message-content h2,\n.message-content h3,\n.message-content h4,\n.message-content h5,\n.message-content h6 {\n  margin: 16px 0 8px 0;\n  color: inherit;\n}\n\n.message-content p {\n  margin: 8px 0;\n}\n\n.message-content ul,\n.message-content ol {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n\n.message-content code {\n  background-color: rgba(0, 0, 0, 0.1);\n  padding: 2px 4px;\n  border-radius: 4px;\n  font-size: 0.9em;\n}\n\n.message-content pre {\n  background-color: rgba(0, 0, 0, 0.05);\n  padding: 12px;\n  border-radius: 8px;\n  overflow-x: auto;\n  margin: 8px 0;\n}\n\n.message-steps {\n  margin-top: 12px;\n  border-top: 1px solid #e2e8f0;\n  padding-top: 8px;\n}\n\n.message-steps summary {\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 600;\n  color: #64748b;\n  padding: 4px 0;\n}\n\n.steps-list {\n  margin-top: 8px;\n  padding-left: 16px;\n}\n\n.step-item {\n  margin-bottom: 8px;\n  font-size: 12px;\n}\n\n.step-header {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 4px;\n}\n\n.step-type {\n  background-color: #dbeafe;\n  color: #1d4ed8;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-weight: 600;\n}\n\n.step-tool {\n  background-color: #f3f4f6;\n  color: #374151;\n  padding: 2px 6px;\n  border-radius: 4px;\n}\n\n.step-result {\n  background-color: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 4px;\n  padding: 8px;\n  margin-top: 4px;\n}\n\n.step-result pre {\n  margin: 0;\n  font-size: 11px;\n  white-space: pre-wrap;\n  word-break: break-all;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .chat-header {\n    padding: 12px 16px;\n  }\n\n  .chat-footer {\n    padding: 12px 16px;\n  }\n\n  .message-list {\n    padding: 12px 16px;\n  }\n\n  .message-bubble {\n    max-width: 90%;\n    padding: 10px 12px;\n    font-size: 13px;\n  }\n\n  .current-paper-indicator span {\n    max-width: 200px;\n  }\n\n  .message-input-wrapper {\n    padding: 8px;\n  }\n\n  .send-button {\n    width: 32px;\n    height: 32px;\n  }\n\n  .empty-state {\n    padding: 20px 16px;\n  }\n}\n\n/* Dark theme support */\n.dark .chat-panel-content {\n  background-color: #1e293b;\n  color: white;\n}\n\n.dark .chat-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .chat-header h2 {\n  color: white;\n}\n\n.dark .current-paper-indicator {\n  background-color: #334155;\n  border-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .message-bubble.assistant {\n  background-color: #334155;\n  color: #e2e8f0;\n  border-color: #475569;\n}\n\n.dark .chat-footer {\n  background-color: #1e293b;\n  border-top-color: #334155;\n}", ".paper-editor {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: white;\n  position: relative;\n}\n\n.paper-editor.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  background-color: white;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.editor-title h2 {\n  margin: 0 0 8px 0;\n  font-size: 24px;\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.editor-meta {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: #64748b;\n}\n\n.word-count {\n  font-weight: 500;\n}\n\n.status-indicator .unsaved {\n  color: #f59e0b;\n  font-weight: 500;\n}\n\n.status-indicator .saved {\n  color: #10b981;\n  font-weight: 500;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.editor-button {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 16px;\n  border: 1px solid #e2e8f0;\n  background-color: white;\n  color: #374151;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.editor-button:hover:not(:disabled) {\n  border-color: #3b82f6;\n  color: #3b82f6;\n  background-color: #f0f9ff;\n}\n\n.editor-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.save-button:not(:disabled) {\n  background-color: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n\n.save-button:hover:not(:disabled) {\n  background-color: #2563eb;\n  border-color: #2563eb;\n}\n\n.editor-content {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.split-view {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.edit-pane,\n.preview-pane {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.edit-pane.full,\n.preview-pane.full {\n  flex: 1;\n}\n\n.split-view .edit-pane,\n.split-view .preview-pane {\n  flex: 1;\n  border-right: 1px solid #e2e8f0;\n}\n\n.split-view .preview-pane {\n  border-right: none;\n  border-left: 1px solid #e2e8f0;\n}\n\n.editor-textarea {\n  flex: 1;\n  width: 100%;\n  padding: 20px;\n  border: none;\n  outline: none;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #1e293b;\n  background-color: white;\n  resize: none;\n}\n\n.editor-textarea::placeholder {\n  color: #94a3b8;\n}\n\n.markdown-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px;\n  background-color: white;\n}\n\n.markdown-content h1,\n.markdown-content h2,\n.markdown-content h3,\n.markdown-content h4,\n.markdown-content h5,\n.markdown-content h6 {\n  margin: 24px 0 12px 0;\n  color: #1e293b;\n  font-weight: 600;\n}\n\n.markdown-content h1 {\n  font-size: 32px;\n  border-bottom: 2px solid #e2e8f0;\n  padding-bottom: 8px;\n}\n\n.markdown-content h2 {\n  font-size: 24px;\n  border-bottom: 1px solid #e2e8f0;\n  padding-bottom: 6px;\n}\n\n.markdown-content h3 {\n  font-size: 20px;\n}\n\n.markdown-content h4 {\n  font-size: 18px;\n}\n\n.markdown-content p {\n  margin: 12px 0;\n  line-height: 1.6;\n  color: #374151;\n}\n\n.markdown-content ul,\n.markdown-content ol {\n  margin: 12px 0;\n  padding-left: 24px;\n}\n\n.markdown-content li {\n  margin: 6px 0;\n  line-height: 1.5;\n}\n\n.markdown-content blockquote {\n  margin: 16px 0;\n  padding: 12px 16px;\n  border-left: 4px solid #3b82f6;\n  background-color: #f0f9ff;\n  color: #1e40af;\n  font-style: italic;\n}\n\n.markdown-content code {\n  background-color: #f1f5f9;\n  color: #e11d48;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 0.9em;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n.markdown-content pre {\n  background-color: #1e293b;\n  color: #e2e8f0;\n  padding: 16px;\n  border-radius: 8px;\n  overflow-x: auto;\n  margin: 16px 0;\n}\n\n.markdown-content pre code {\n  background-color: transparent;\n  color: inherit;\n  padding: 0;\n  border-radius: 0;\n}\n\n.markdown-content table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 16px 0;\n}\n\n.markdown-content th,\n.markdown-content td {\n  border: 1px solid #e2e8f0;\n  padding: 8px 12px;\n  text-align: left;\n}\n\n.markdown-content th {\n  background-color: #f8fafc;\n  font-weight: 600;\n}\n\n.editor-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 20px;\n  border-top: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n  font-size: 12px;\n  color: #64748b;\n}\n\n.editor-stats {\n  display: flex;\n  gap: 16px;\n}\n\n.editor-help {\n  font-style: italic;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .editor-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n\n  .editor-actions {\n    justify-content: flex-end;\n  }\n\n  .editor-button {\n    padding: 6px 12px;\n    font-size: 13px;\n  }\n\n  .editor-textarea {\n    padding: 16px;\n    font-size: 13px;\n  }\n\n  .markdown-content {\n    padding: 16px;\n  }\n\n  .editor-footer {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n\n  .editor-stats {\n    gap: 12px;\n  }\n}\n\n/* Dark theme support */\n.dark .paper-editor {\n  background-color: #1e293b;\n}\n\n.dark .editor-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .editor-title h2 {\n  color: #e2e8f0;\n}\n\n.dark .editor-meta {\n  color: #94a3b8;\n}\n\n.dark .editor-button {\n  background-color: #334155;\n  border-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .editor-button:hover:not(:disabled) {\n  border-color: #60a5fa;\n  color: #60a5fa;\n  background-color: rgba(96, 165, 250, 0.1);\n}\n\n.dark .editor-textarea {\n  background-color: #1e293b;\n  color: #e2e8f0;\n}\n\n.dark .markdown-content {\n  background-color: #1e293b;\n}\n\n.dark .markdown-content h1,\n.dark .markdown-content h2,\n.dark .markdown-content h3,\n.dark .markdown-content h4,\n.dark .markdown-content h5,\n.dark .markdown-content h6 {\n  color: #e2e8f0;\n}\n\n.dark .markdown-content h1 {\n  border-bottom-color: #475569;\n}\n\n.dark .markdown-content h2 {\n  border-bottom-color: #475569;\n}\n\n.dark .markdown-content p {\n  color: #d1d5db;\n}\n\n.dark .markdown-content blockquote {\n  background-color: rgba(96, 165, 250, 0.1);\n  border-left-color: #60a5fa;\n  color: #93c5fd;\n}\n\n.dark .markdown-content code {\n  background-color: #374151;\n  color: #fbbf24;\n}\n\n.dark .markdown-content th {\n  background-color: #374151;\n}\n\n.dark .markdown-content th,\n.dark .markdown-content td {\n  border-color: #475569;\n}\n\n.dark .editor-footer {\n  background-color: #0f172a;\n  border-top-color: #334155;\n  color: #94a3b8;\n}", ".chart-viewer {\n  height: 100%;\n  display: flex;\n  background-color: white;\n}\n\n.chart-viewer-empty,\n.no-chart-selected {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  color: #64748b;\n  padding: 40px;\n}\n\n.chart-viewer-empty .empty-icon,\n.no-chart-selected .empty-icon {\n  color: #cbd5e1;\n  margin-bottom: 16px;\n}\n\n.chart-viewer-empty h3,\n.no-chart-selected h3 {\n  margin: 0 0 12px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.chart-viewer-empty p,\n.no-chart-selected p {\n  margin: 0;\n  font-size: 16px;\n  max-width: 400px;\n  line-height: 1.5;\n}\n\n.chart-sidebar {\n  width: 300px;\n  min-width: 280px;\n  border-right: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n  display: flex;\n  flex-direction: column;\n}\n\n.sidebar-title {\n  padding: 20px;\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.chart-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 12px;\n}\n\n.chart-list-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 8px;\n  background-color: white;\n  border: 1px solid #e2e8f0;\n}\n\n.chart-list-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);\n}\n\n.chart-list-item.active {\n  border-color: #3b82f6;\n  background-color: #f0f9ff;\n}\n\n.chart-list-icon {\n  color: #3b82f6;\n  flex-shrink: 0;\n}\n\n.chart-list-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.chart-list-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.chart-list-type {\n  font-size: 12px;\n  color: #64748b;\n  text-transform: capitalize;\n}\n\n.chart-main {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding: 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.chart-info {\n  flex: 1;\n}\n\n.chart-title {\n  margin: 0 0 8px 0;\n  font-size: 24px;\n  font-weight: 700;\n  color: #1e293b;\n}\n\n.chart-meta {\n  display: flex;\n  gap: 16px;\n  font-size: 14px;\n  color: #64748b;\n}\n\n.chart-type {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 500;\n}\n\n.chart-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.chart-action-button {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 16px;\n  border: 1px solid #e2e8f0;\n  background-color: white;\n  color: #374151;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n}\n\n.chart-action-button:hover {\n  border-color: #3b82f6;\n  color: #3b82f6;\n  background-color: #f0f9ff;\n}\n\n.chart-display {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  background-color: white;\n}\n\n.chart-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s ease;\n  cursor: zoom-in;\n}\n\n.chart-image:hover {\n  transform: scale(1.02);\n}\n\n.chart-image.zoomed {\n  cursor: zoom-out;\n  transform: scale(1.5);\n}\n\n.chart-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  color: #64748b;\n  gap: 16px;\n}\n\n.chart-error.hidden {\n  display: none;\n}\n\n.chart-error h4 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.chart-error p {\n  margin: 0;\n  font-size: 14px;\n}\n\n.retry-button {\n  padding: 8px 16px;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.retry-button:hover {\n  background-color: #2563eb;\n}\n\n.chart-data {\n  border-top: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.chart-data details {\n  padding: 16px 20px;\n}\n\n.chart-data summary {\n  font-size: 14px;\n  font-weight: 600;\n  color: #374151;\n  cursor: pointer;\n  padding: 4px 0;\n}\n\n.data-preview {\n  margin: 12px 0 0 0;\n  padding: 12px;\n  background-color: #1e293b;\n  color: #e2e8f0;\n  border-radius: 6px;\n  font-size: 12px;\n  overflow-x: auto;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .chart-viewer {\n    flex-direction: column;\n  }\n\n  .chart-sidebar {\n    width: 100%;\n    min-width: auto;\n    border-right: none;\n    border-bottom: 1px solid #e2e8f0;\n    max-height: 200px;\n  }\n\n  .chart-list {\n    display: flex;\n    gap: 8px;\n    overflow-x: auto;\n    padding: 12px;\n  }\n\n  .chart-list-item {\n    min-width: 200px;\n    margin-bottom: 0;\n  }\n\n  .chart-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n  }\n\n  .chart-actions {\n    justify-content: flex-end;\n  }\n\n  .chart-display {\n    padding: 16px;\n  }\n}\n\n/* Dark theme support */\n.dark .chart-viewer {\n  background-color: #1e293b;\n}\n\n.dark .chart-sidebar {\n  background-color: #0f172a;\n  border-right-color: #334155;\n}\n\n.dark .sidebar-title {\n  color: #e2e8f0;\n  border-bottom-color: #334155;\n}\n\n.dark .chart-list-item {\n  background-color: #334155;\n  border-color: #475569;\n}\n\n.dark .chart-list-item:hover {\n  border-color: #60a5fa;\n}\n\n.dark .chart-list-item.active {\n  background-color: rgba(96, 165, 250, 0.1);\n  border-color: #60a5fa;\n}\n\n.dark .chart-list-title {\n  color: #e2e8f0;\n}\n\n.dark .chart-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .chart-title {\n  color: #e2e8f0;\n}\n\n.dark .chart-action-button {\n  background-color: #334155;\n  border-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .chart-action-button:hover {\n  border-color: #60a5fa;\n  color: #60a5fa;\n  background-color: rgba(96, 165, 250, 0.1);\n}\n\n.dark .chart-display {\n  background-color: #1e293b;\n}\n\n.dark .chart-error h4 {\n  color: #e2e8f0;\n}\n\n.dark .chart-data {\n  background-color: #0f172a;\n  border-top-color: #334155;\n}\n\n.dark .chart-data summary {\n  color: #e2e8f0;\n}", ".main-work-area {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: white;\n}\n\n.work-area-header {\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n  padding: 0 20px;\n}\n\n.view-tabs {\n  display: flex;\n  gap: 0;\n}\n\n.tab {\n  padding: 12px 20px;\n  border: none;\n  background-color: transparent;\n  color: #64748b;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s ease;\n  position: relative;\n}\n\n.tab:hover {\n  color: #3b82f6;\n  background-color: rgba(59, 130, 246, 0.05);\n}\n\n.tab.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n  background-color: white;\n}\n\n.tab.active::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background-color: white;\n}\n\n.work-area-content {\n  flex: 1;\n  overflow: hidden;\n  position: relative;\n}\n\n/* Empty States */\n.no-paper-selected,\n.no-charts,\n.no-search-results {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  color: #64748b;\n  padding: 40px;\n}\n\n.no-paper-selected h3,\n.no-charts h3,\n.no-search-results h3 {\n  margin: 0 0 12px 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.no-paper-selected p,\n.no-charts p,\n.no-search-results p {\n  margin: 0;\n  font-size: 16px;\n  max-width: 400px;\n  line-height: 1.5;\n}\n\n/* Welcome Screen Styles */\n.welcome-screen {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 40px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n.welcome-content {\n  max-width: 600px;\n}\n\n.welcome-title {\n  font-size: 32px;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 16px 0;\n}\n\n.welcome-subtitle {\n  font-size: 18px;\n  color: #64748b;\n  margin: 0 0 32px 0;\n  line-height: 1.6;\n}\n\n.welcome-features {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 24px;\n  margin: 32px 0;\n}\n\n.feature-card {\n  background-color: white;\n  padding: 24px;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n  transition: all 0.2s ease;\n}\n\n.feature-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.feature-icon {\n  width: 48px;\n  height: 48px;\n  background-color: #3b82f6;\n  color: white;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto 16px auto;\n}\n\n.feature-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 8px 0;\n}\n\n.feature-description {\n  font-size: 14px;\n  color: #64748b;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.welcome-cta {\n  margin-top: 32px;\n}\n\n.cta-text {\n  font-size: 16px;\n  color: #64748b;\n  margin: 0 0 16px 0;\n}\n\n.example-queries {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  justify-content: center;\n}\n\n.example-query {\n  background-color: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 20px;\n  padding: 8px 16px;\n  font-size: 14px;\n  color: #3b82f6;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.example-query:hover {\n  background-color: #3b82f6;\n  color: white;\n  transform: translateY(-1px);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .view-tabs {\n    overflow-x: auto;\n    scrollbar-width: none;\n    -ms-overflow-style: none;\n  }\n\n  .view-tabs::-webkit-scrollbar {\n    display: none;\n  }\n\n  .tab {\n    padding: 10px 16px;\n    font-size: 13px;\n    white-space: nowrap;\n  }\n\n  .work-area-header {\n    padding: 0 12px;\n  }\n\n  .welcome-screen {\n    padding: 20px;\n  }\n\n  .welcome-title {\n    font-size: 24px;\n  }\n\n  .welcome-subtitle {\n    font-size: 16px;\n  }\n\n  .welcome-features {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .feature-card {\n    padding: 20px;\n  }\n\n  .example-queries {\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .example-query {\n    width: 100%;\n    max-width: 300px;\n    text-align: center;\n  }\n}\n\n/* Dark theme support */\n.dark .main-work-area {\n  background-color: #1e293b;\n}\n\n.dark .work-area-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .tab {\n  color: #94a3b8;\n}\n\n.dark .tab:hover {\n  color: #60a5fa;\n  background-color: rgba(96, 165, 250, 0.1);\n}\n\n.dark .tab.active {\n  color: #60a5fa;\n  background-color: #1e293b;\n}\n\n.dark .no-paper-selected h3,\n.dark .no-charts h3,\n.dark .no-search-results h3 {\n  color: #e2e8f0;\n}\n\n.dark .welcome-screen {\n  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);\n}\n\n.dark .welcome-title {\n  color: #e2e8f0;\n}\n\n.dark .feature-card {\n  background-color: #334155;\n  border-color: #475569;\n}\n\n.dark .feature-title {\n  color: #e2e8f0;\n}\n\n.dark .example-query {\n  background-color: #334155;\n  border-color: #475569;\n  color: #60a5fa;\n}\n\n.dark .example-query:hover {\n  background-color: #60a5fa;\n  color: white;\n}\n", ".paper-list {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: white;\n}\n\n.paper-list-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.paper-list-title {\n  margin: 0 0 16px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.paper-search-container {\n  margin-bottom: 16px;\n}\n\n.search-input-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.search-icon {\n  position: absolute;\n  left: 12px;\n  color: #64748b;\n  z-index: 1;\n}\n\n.paper-search {\n  width: 100%;\n  padding: 10px 12px 10px 40px;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  background-color: white;\n  transition: all 0.2s ease;\n}\n\n.paper-search:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.create-paper-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  width: 100%;\n  padding: 12px 16px;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.create-paper-button:hover {\n  background-color: #2563eb;\n  transform: translateY(-1px);\n}\n\n.paper-list-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px 20px;\n}\n\n.papers-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.paper-item {\n  padding: 16px;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background-color: white;\n  position: relative;\n}\n\n.paper-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n  transform: translateY(-2px);\n}\n\n.paper-item.active {\n  border-color: #3b82f6;\n  background-color: #f0f9ff;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.paper-item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.paper-item-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n  line-height: 1.4;\n  flex: 1;\n  margin-right: 12px;\n}\n\n.paper-item-status {\n  padding: 4px 8px;\n  border-radius: 6px;\n  font-size: 11px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  flex-shrink: 0;\n}\n\n.paper-item-status.draft {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.paper-item-status.in_progress {\n  background-color: #dbeafe;\n  color: #1d4ed8;\n}\n\n.paper-item-status.completed {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.paper-item-content {\n  margin-bottom: 12px;\n}\n\n.paper-item-preview {\n  font-size: 14px;\n  color: #64748b;\n  line-height: 1.5;\n  margin: 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.paper-item-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n  color: #94a3b8;\n}\n\n.paper-item-date {\n  font-weight: 500;\n}\n\n.paper-item-words {\n  background-color: #f1f5f9;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n/* Empty States */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 60px 20px;\n  color: #64748b;\n}\n\n.empty-icon {\n  color: #cbd5e1;\n  margin-bottom: 16px;\n}\n\n.empty-state h4 {\n  margin: 0 0 8px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.empty-state p {\n  margin: 0 0 24px 0;\n  font-size: 14px;\n  max-width: 300px;\n  line-height: 1.5;\n}\n\n.create-first-paper {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 24px;\n  background-color: #3b82f6;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.create-first-paper:hover {\n  background-color: #2563eb;\n  transform: translateY(-1px);\n}\n\n/* Loading State */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  color: #64748b;\n}\n\n.loading-spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #e2e8f0;\n  border-top: 3px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .paper-list-header {\n    padding: 12px 16px;\n  }\n  \n  .paper-list-content {\n    padding: 12px 16px;\n  }\n  \n  .paper-item {\n    padding: 12px;\n  }\n  \n  .paper-item-title {\n    font-size: 15px;\n  }\n  \n  .paper-item-preview {\n    font-size: 13px;\n    -webkit-line-clamp: 3;\n  }\n  \n  .empty-state {\n    padding: 40px 16px;\n  }\n}\n\n/* Dark theme support */\n.dark .paper-list {\n  background-color: #1e293b;\n}\n\n.dark .paper-list-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .paper-list-title {\n  color: #e2e8f0;\n}\n\n.dark .paper-search {\n  background-color: #334155;\n  border-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .paper-search:focus {\n  border-color: #60a5fa;\n  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);\n}\n\n.dark .paper-item {\n  background-color: #334155;\n  border-color: #475569;\n}\n\n.dark .paper-item:hover {\n  border-color: #60a5fa;\n  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);\n}\n\n.dark .paper-item.active {\n  background-color: rgba(96, 165, 250, 0.1);\n  border-color: #60a5fa;\n}\n\n.dark .paper-item-title {\n  color: #e2e8f0;\n}\n\n.dark .paper-item-preview {\n  color: #94a3b8;\n}\n\n.dark .paper-item-words {\n  background-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .empty-state h4 {\n  color: #e2e8f0;\n}\n", ".context-panel-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background-color: white;\n}\n\n.context-panel-collapsed {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: #f8fafc;\n  border-left: 1px solid #e2e8f0;\n  padding: 12px 0;\n}\n\n.collapsed-tabs {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.collapsed-tab {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #64748b;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.collapsed-tab:hover {\n  color: #3b82f6;\n  transform: translateX(-2px);\n}\n\n/* Context Panel Header */\n.context-panel-header {\n  border-bottom: 1px solid #e2e8f0;\n  background-color: #f8fafc;\n}\n\n.context-tabs {\n  display: flex;\n  overflow-x: auto;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.context-tabs::-webkit-scrollbar {\n  display: none;\n}\n\n.context-tab {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 12px 16px;\n  border: none;\n  background-color: transparent;\n  color: #64748b;\n  font-size: 13px;\n  font-weight: 500;\n  cursor: pointer;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s ease;\n  white-space: nowrap;\n  min-width: fit-content;\n}\n\n.context-tab:hover {\n  color: #3b82f6;\n  background-color: rgba(59, 130, 246, 0.05);\n}\n\n.context-tab.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n  background-color: white;\n}\n\n.context-tab span {\n  font-size: 12px;\n}\n\n/* Context Panel Body */\n.context-panel-body {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Tab Content Styles */\n.search-tab {\n  padding: 20px;\n  text-align: center;\n  color: #64748b;\n  font-size: 14px;\n}\n\n/* Paper List Styles */\n.paper-list {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.paper-list-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: white;\n}\n\n.paper-list-title {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.paper-search {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 14px;\n  background-color: #f8fafc;\n}\n\n.paper-search:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background-color: white;\n}\n\n.paper-list-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 12px 20px;\n}\n\n.paper-item {\n  padding: 12px;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  margin-bottom: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  background-color: white;\n}\n\n.paper-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);\n}\n\n.paper-item.active {\n  border-color: #3b82f6;\n  background-color: #f0f9ff;\n}\n\n.paper-item-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n  line-height: 1.3;\n}\n\n.paper-item-meta {\n  font-size: 12px;\n  color: #64748b;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.paper-item-status {\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 10px;\n  font-weight: 500;\n  text-transform: uppercase;\n}\n\n.paper-item-status.draft {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.paper-item-status.in_progress {\n  background-color: #dbeafe;\n  color: #1d4ed8;\n}\n\n.paper-item-status.completed {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n/* Chart Gallery Styles */\n.chart-gallery {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-gallery-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: white;\n}\n\n.chart-gallery-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.chart-gallery-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 12px 20px;\n}\n\n.chart-item {\n  margin-bottom: 16px;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  overflow: hidden;\n  background-color: white;\n}\n\n.chart-item-header {\n  padding: 12px;\n  background-color: #f8fafc;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.chart-item-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n}\n\n.chart-item-type {\n  font-size: 12px;\n  color: #64748b;\n  text-transform: capitalize;\n}\n\n.chart-item-image {\n  width: 100%;\n  height: auto;\n  display: block;\n}\n\n/* Session Info Styles */\n.session-info {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.session-info-header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background-color: white;\n}\n\n.session-info-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #1e293b;\n}\n\n.session-info-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px 20px;\n}\n\n.session-detail {\n  margin-bottom: 16px;\n}\n\n.session-detail-label {\n  font-size: 12px;\n  font-weight: 600;\n  color: #64748b;\n  text-transform: uppercase;\n  margin-bottom: 4px;\n}\n\n.session-detail-value {\n  font-size: 14px;\n  color: #1e293b;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .context-tab {\n    padding: 10px 12px;\n    font-size: 12px;\n  }\n  \n  .context-tab span {\n    display: none;\n  }\n  \n  .paper-list-header,\n  .chart-gallery-header,\n  .session-info-header {\n    padding: 12px 16px;\n  }\n  \n  .paper-list-content,\n  .chart-gallery-content,\n  .session-info-content {\n    padding: 8px 16px;\n  }\n}\n\n/* Dark theme support */\n.dark .context-panel-content {\n  background-color: #1e293b;\n}\n\n.dark .context-panel-header {\n  background-color: #0f172a;\n  border-bottom-color: #334155;\n}\n\n.dark .context-tab {\n  color: #94a3b8;\n}\n\n.dark .context-tab:hover {\n  color: #60a5fa;\n  background-color: rgba(96, 165, 250, 0.1);\n}\n\n.dark .context-tab.active {\n  color: #60a5fa;\n  background-color: #1e293b;\n}\n\n.dark .paper-list-title,\n.dark .chart-gallery-title,\n.dark .session-info-title {\n  color: #e2e8f0;\n}\n\n.dark .paper-search {\n  background-color: #334155;\n  border-color: #475569;\n  color: #e2e8f0;\n}\n\n.dark .paper-item {\n  background-color: #334155;\n  border-color: #475569;\n}\n\n.dark .paper-item:hover {\n  border-color: #60a5fa;\n}\n\n.dark .paper-item.active {\n  background-color: rgba(96, 165, 250, 0.1);\n  border-color: #60a5fa;\n}\n\n.dark .paper-item-title {\n  color: #e2e8f0;\n}\n\n.dark .chart-item {\n  background-color: #334155;\n  border-color: #475569;\n}\n\n.dark .chart-item-header {\n  background-color: #475569;\n  border-bottom-color: #64748b;\n}\n\n.dark .chart-item-title {\n  color: #e2e8f0;\n}\n", ".notification-container {\n  position: fixed;\n  top: 80px;\n  right: 20px;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  max-width: 400px;\n  width: 100%;\n}\n\n.notification {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 16px;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  border-left: 4px solid;\n  animation: slideIn 0.3s ease-out;\n  position: relative;\n}\n\n.notification-success {\n  border-left-color: #10b981;\n}\n\n.notification-error {\n  border-left-color: #ef4444;\n}\n\n.notification-warning {\n  border-left-color: #f59e0b;\n}\n\n.notification-info {\n  border-left-color: #3b82f6;\n}\n\n.notification-icon {\n  flex-shrink: 0;\n  margin-top: 2px;\n}\n\n.notification-success .notification-icon {\n  color: #10b981;\n}\n\n.notification-error .notification-icon {\n  color: #ef4444;\n}\n\n.notification-warning .notification-icon {\n  color: #f59e0b;\n}\n\n.notification-info .notification-icon {\n  color: #3b82f6;\n}\n\n.notification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.notification-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 4px;\n  line-height: 1.3;\n}\n\n.notification-message {\n  font-size: 13px;\n  color: #64748b;\n  line-height: 1.4;\n  word-wrap: break-word;\n}\n\n.notification-close {\n  flex-shrink: 0;\n  width: 24px;\n  height: 24px;\n  border: none;\n  background-color: transparent;\n  color: #94a3b8;\n  cursor: pointer;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n  margin-top: -2px;\n}\n\n.notification-close:hover {\n  background-color: #f1f5f9;\n  color: #64748b;\n}\n\n/* Animations */\n@keyframes slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@keyframes slideOut {\n  from {\n    transform: translateX(0);\n    opacity: 1;\n  }\n  to {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n}\n\n.notification.removing {\n  animation: slideOut 0.3s ease-in forwards;\n}\n\n/* Progress bar for timed notifications */\n.notification::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 2px;\n  background-color: currentColor;\n  opacity: 0.3;\n  animation: progress linear;\n}\n\n.notification-success::after {\n  background-color: #10b981;\n  animation-duration: var(--duration, 5s);\n}\n\n.notification-error::after {\n  background-color: #ef4444;\n  animation-duration: var(--duration, 5s);\n}\n\n.notification-warning::after {\n  background-color: #f59e0b;\n  animation-duration: var(--duration, 5s);\n}\n\n.notification-info::after {\n  background-color: #3b82f6;\n  animation-duration: var(--duration, 3s);\n}\n\n@keyframes progress {\n  from {\n    width: 100%;\n  }\n  to {\n    width: 0%;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .notification-container {\n    top: 70px;\n    right: 12px;\n    left: 12px;\n    max-width: none;\n  }\n\n  .notification {\n    padding: 12px;\n    gap: 10px;\n  }\n\n  .notification-title {\n    font-size: 13px;\n  }\n\n  .notification-message {\n    font-size: 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .notification-container {\n    top: 60px;\n    right: 8px;\n    left: 8px;\n  }\n\n  .notification {\n    padding: 10px;\n    gap: 8px;\n  }\n}\n\n/* Dark theme support */\n.dark .notification {\n  background-color: #334155;\n  color: #e2e8f0;\n}\n\n.dark .notification-title {\n  color: #e2e8f0;\n}\n\n.dark .notification-message {\n  color: #94a3b8;\n}\n\n.dark .notification-close {\n  color: #64748b;\n}\n\n.dark .notification-close:hover {\n  background-color: #475569;\n  color: #94a3b8;\n}\n\n/* Hover effects */\n.notification:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);\n}\n\n/* Focus states for accessibility */\n.notification-close:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .notification {\n    border: 2px solid;\n  }\n  \n  .notification-success {\n    border-color: #10b981;\n  }\n  \n  .notification-error {\n    border-color: #ef4444;\n  }\n  \n  .notification-warning {\n    border-color: #f59e0b;\n  }\n  \n  .notification-info {\n    border-color: #3b82f6;\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  .notification {\n    animation: none;\n  }\n  \n  .notification::after {\n    animation: none;\n  }\n  \n  .notification:hover {\n    transform: none;\n  }\n}\n", ".layout {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8fafc;\n}\n\n.layout-body {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n/* Chat Panel - Left Side */\n.chat-panel {\n  width: 350px;\n  min-width: 300px;\n  max-width: 500px;\n  background-color: white;\n  border-right: 1px solid #e2e8f0;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.chat-panel.collapsed {\n  width: 60px;\n  min-width: 60px;\n}\n\n/* Main Work Area - Center */\n.main-work-area {\n  flex: 1;\n  background-color: white;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n/* Context Panel - Right Side */\n.context-panel {\n  width: 320px;\n  min-width: 280px;\n  max-width: 400px;\n  background-color: white;\n  border-left: 1px solid #e2e8f0;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.context-panel.collapsed {\n  width: 60px;\n  min-width: 60px;\n}\n\n/* Responsive Design */\n@media (max-width: 1200px) {\n  .context-panel {\n    width: 280px;\n  }\n}\n\n@media (max-width: 992px) {\n  .layout-body {\n    position: relative;\n  }\n  \n  .chat-panel {\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    z-index: 100;\n    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);\n    transform: translateX(-100%);\n  }\n  \n  .chat-panel:not(.collapsed) {\n    transform: translateX(0);\n  }\n  \n  .context-panel {\n    position: absolute;\n    right: 0;\n    top: 0;\n    height: 100%;\n    z-index: 100;\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\n    transform: translateX(100%);\n  }\n  \n  .context-panel:not(.collapsed) {\n    transform: translateX(0);\n  }\n}\n\n@media (max-width: 768px) {\n  .chat-panel,\n  .context-panel {\n    width: 100%;\n    max-width: 100%;\n  }\n}\n\n/* Panel Resize Handles */\n.resize-handle {\n  width: 4px;\n  background-color: transparent;\n  cursor: col-resize;\n  position: relative;\n}\n\n.resize-handle:hover {\n  background-color: #3b82f6;\n}\n\n.resize-handle::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -2px;\n  right: -2px;\n  bottom: 0;\n}\n\n/* Smooth transitions */\n.chat-panel,\n.context-panel,\n.main-work-area {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Focus states */\n.chat-panel:focus-within,\n.context-panel:focus-within {\n  box-shadow: inset 2px 0 0 #3b82f6;\n}\n\n/* Loading states */\n.layout.loading {\n  pointer-events: none;\n}\n\n.layout.loading::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.8);\n  z-index: 1000;\n}\n", "/* Global Styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f8fafc;\n  color: #1e293b;\n}\n\ncode {\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;\n}\n\n.App {\n  height: 100vh;\n  overflow: hidden;\n}\n\n/* Scrollbar Styles */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Focus Styles */\nbutton:focus,\ninput:focus,\ntextarea:focus,\nselect:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* Button Reset */\nbutton {\n  border: none;\n  background: none;\n  cursor: pointer;\n  font-family: inherit;\n}\n\n/* Input Reset */\ninput,\ntextarea {\n  font-family: inherit;\n  font-size: inherit;\n}\n\n/* Utility Classes */\n.spinning {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.3s ease-in;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n.slide-up {\n  animation: slideUp 0.3s ease-out;\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(20px);\n    opacity: 0;\n  }\n\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n/* Dark Theme */\n.dark {\n  background-color: #0f172a;\n  color: #e2e8f0;\n}\n\n.dark ::-webkit-scrollbar-track {\n  background: #1e293b;\n}\n\n.dark ::-webkit-scrollbar-thumb {\n  background: #475569;\n}\n\n.dark ::-webkit-scrollbar-thumb:hover {\n  background: #64748b;\n}"], "names": [], "sourceRoot": ""}