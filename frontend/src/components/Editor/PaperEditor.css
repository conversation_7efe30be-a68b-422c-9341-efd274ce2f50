.paper-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  position: relative;
}

.paper-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: white;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.editor-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.editor-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #64748b;
}

.word-count {
  font-weight: 500;
}

.status-indicator .unsaved {
  color: #f59e0b;
  font-weight: 500;
}

.status-indicator .saved {
  color: #10b981;
  font-weight: 500;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.editor-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editor-button:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
  background-color: #f0f9ff;
}

.editor-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.save-button:not(:disabled) {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.save-button:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.editor-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.split-view {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.edit-pane,
.preview-pane {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.edit-pane.full,
.preview-pane.full {
  flex: 1;
}

.split-view .edit-pane,
.split-view .preview-pane {
  flex: 1;
  border-right: 1px solid #e2e8f0;
}

.split-view .preview-pane {
  border-right: none;
  border-left: 1px solid #e2e8f0;
}

.editor-textarea {
  flex: 1;
  width: 100%;
  padding: 20px;
  border: none;
  outline: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #1e293b;
  background-color: white;
  resize: none;
}

.editor-textarea::placeholder {
  color: #94a3b8;
}

.markdown-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: white;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 24px 0 12px 0;
  color: #1e293b;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 32px;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 24px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 6px;
}

.markdown-content h3 {
  font-size: 20px;
}

.markdown-content h4 {
  font-size: 18px;
}

.markdown-content p {
  margin: 12px 0;
  line-height: 1.6;
  color: #374151;
}

.markdown-content ul,
.markdown-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 6px 0;
  line-height: 1.5;
}

.markdown-content blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #3b82f6;
  background-color: #f0f9ff;
  color: #1e40af;
  font-style: italic;
}

.markdown-content code {
  background-color: #f1f5f9;
  color: #e11d48;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-content pre {
  background-color: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-content pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content th {
  background-color: #f8fafc;
  font-weight: 600;
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
  font-size: 12px;
  color: #64748b;
}

.editor-stats {
  display: flex;
  gap: 16px;
}

.editor-help {
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .editor-actions {
    justify-content: flex-end;
  }

  .editor-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  .editor-textarea {
    padding: 16px;
    font-size: 13px;
  }

  .markdown-content {
    padding: 16px;
  }

  .editor-footer {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .editor-stats {
    gap: 12px;
  }
}

/* Dark theme support */
.dark .paper-editor {
  background-color: #1e293b;
}

.dark .editor-header {
  background-color: #0f172a;
  border-bottom-color: #334155;
}

.dark .editor-title h2 {
  color: #e2e8f0;
}

.dark .editor-meta {
  color: #94a3b8;
}

.dark .editor-button {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .editor-button:hover:not(:disabled) {
  border-color: #60a5fa;
  color: #60a5fa;
  background-color: rgba(96, 165, 250, 0.1);
}

.dark .editor-textarea {
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .markdown-content {
  background-color: #1e293b;
}

.dark .markdown-content h1,
.dark .markdown-content h2,
.dark .markdown-content h3,
.dark .markdown-content h4,
.dark .markdown-content h5,
.dark .markdown-content h6 {
  color: #e2e8f0;
}

.dark .markdown-content h1 {
  border-bottom-color: #475569;
}

.dark .markdown-content h2 {
  border-bottom-color: #475569;
}

.dark .markdown-content p {
  color: #d1d5db;
}

.dark .markdown-content blockquote {
  background-color: rgba(96, 165, 250, 0.1);
  border-left-color: #60a5fa;
  color: #93c5fd;
}

.dark .markdown-content code {
  background-color: #374151;
  color: #fbbf24;
}

.dark .markdown-content th {
  background-color: #374151;
}

.dark .markdown-content th,
.dark .markdown-content td {
  border-color: #475569;
}

.dark .editor-footer {
  background-color: #0f172a;
  border-top-color: #334155;
  color: #94a3b8;
}