import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Paper, updateCurrentPaperContent } from '../../store/slices/papersSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { Save, Eye, EyeOff, Download } from 'lucide-react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import './PaperEditor.css';

interface PaperEditorProps {
  paper: Paper;
}

const PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {
  const dispatch = useDispatch();
  const [content, setContent] = useState(paper.content);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    setContent(paper.content);
    setHasUnsavedChanges(false);
  }, [paper.paper_id, paper.content]);

  useEffect(() => {
    setHasUnsavedChanges(content !== paper.content);
  }, [content, paper.content]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
    dispatch(updateCurrentPaperContent(e.target.value));
  };

  const handleSave = async () => {
    if (!hasUnsavedChanges) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/v1/papers/${paper.paper_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          title: paper.title,
        }),
      });

      if (response.ok) {
        setHasUnsavedChanges(false);
        dispatch(addNotification({
          type: 'success',
          title: 'Paper Saved',
          message: 'Your changes have been saved successfully.',
          duration: 3000,
        }));
      } else {
        throw new Error('Failed to save');
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save your changes. Please try again.',
        duration: 5000,
      }));
    } finally {
      setIsSaving(false);
    }
  };

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const renderPreview = () => {
    const html = marked.parse(content, { async: false }) as string;
    const sanitizedHtml = DOMPurify.sanitize(html);
    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
  };

  const getWordCount = () => {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  return (
    <div className="paper-editor">
      <div className="editor-header">
        <div className="editor-title">
          <h2>{paper.title}</h2>
          <div className="editor-meta">
            <span className="word-count">{getWordCount()} words</span>
            <span className="status-indicator">
              {hasUnsavedChanges ? (
                <span className="unsaved">● Unsaved changes</span>
              ) : (
                <span className="saved">✓ Saved</span>
              )}
            </span>
          </div>
        </div>

        <div className="editor-actions">
          <button
            onClick={() => setIsPreviewMode(!isPreviewMode)}
            className="editor-button"
            title={isPreviewMode ? 'Edit mode' : 'Preview mode'}
          >
            {isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}
            {isPreviewMode ? 'Edit' : 'Preview'}
          </button>

          <button
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isSaving}
            className="editor-button save-button"
            title="Save changes"
          >
            <Save size={16} />
            {isSaving ? 'Saving...' : 'Save'}
          </button>

          <button
            onClick={handleExport}
            className="editor-button"
            title="Export as Markdown"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      <div className="editor-content">
        {isPreviewMode ? (
          <div className="preview-pane">
            <div className="markdown-content">
              {renderPreview()}
            </div>
          </div>
        ) : (
          <div className="edit-pane">
            <textarea
              value={content}
              onChange={handleContentChange}
              className="editor-textarea"
              placeholder="Start writing your paper here..."
              spellCheck={true}
            />
          </div>
        )}
      </div>

      <div className="editor-footer">
        <div className="editor-stats">
          <span>Characters: {content.length}</span>
          <span>Words: {getWordCount()}</span>
          <span>Lines: {content.split('\n').length}</span>
        </div>

        <div className="editor-help">
          <span>Markdown supported • Ctrl+S to save</span>
        </div>
      </div>
    </div>
  );
};

export default PaperEditor;
