import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { Paper, updateCurrentPaperContent, setCursorPosition as setCursorPositionAction } from '../../store/slices/papersSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import Editor from '@monaco-editor/react';
import api from '../../services/api';
import './PaperEditor.css';

interface PaperEditorProps {
  paper: Paper;
}

const PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {
  const dispatch = useDispatch();
  const [content, setContent] = useState(paper.content);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSplitView, setIsSplitView] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastSavedContent, setLastSavedContent] = useState(paper.content);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const handleSaveRef = useRef<(() => Promise<void>) | null>(null);

  useEffect(() => {
    console.log('Paper loaded:', { paperId: paper.paper_id, contentLength: paper.content?.length });
    setContent(paper.content);
    setLastSavedContent(paper.content);
    setHasUnsavedChanges(false);
  }, [paper.paper_id]); // Only depend on paper ID, not content

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  const handleContentChange = useCallback((value: string | undefined) => {
    const newContent = value || '';
    setContent(newContent);
    // Don't update Redux on every keystroke - only on save

    // Mark as having unsaved changes
    const hasChanges = newContent !== lastSavedContent;
    setHasUnsavedChanges(hasChanges);
    // Only log when changes state changes
    if (hasChanges) {
      console.log('Content has unsaved changes:', { newLength: newContent.length, lastSavedLength: lastSavedContent.length });
    }

    // Clear existing timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new timeout for auto-save (3 seconds after user stops typing)
    if (hasChanges) {
      autoSaveTimeoutRef.current = setTimeout(() => {
        console.log('Auto-saving...');
        if (handleSaveRef.current) {
          handleSaveRef.current();
        }
      }, 3000);
    }
  }, [lastSavedContent]);

  const handleCursorPositionChange = useCallback((editor: any) => {
    const position = editor.getPosition();
    if (position) {
      const newPosition = { line: position.lineNumber, column: position.column };
      dispatch(setCursorPositionAction(newPosition));
    }
  }, [dispatch]);



  const handleSave = async () => {
    console.log('handleSave called:', { hasUnsavedChanges, isSaving, contentLength: content.length });
    if (!hasUnsavedChanges) {
      console.log('No unsaved changes, skipping save');
      return;
    }

    // Clear any pending auto-save
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    setIsSaving(true);
    console.log('Starting save...');
    try {
      const result = await api.papers.update(paper.paper_id, paper.title, content);
      console.log('Save result:', result);

      if (result.data) {
        setLastSavedContent(content);
        setHasUnsavedChanges(false);
        // Update Redux store only on successful save
        dispatch(updateCurrentPaperContent(content));
        dispatch(addNotification({
          type: 'success',
          title: 'Paper Saved',
          message: 'Your changes have been saved successfully.',
          duration: 3000,
        }));
      } else {
        throw new Error(result.error || 'Failed to save');
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Save Failed',
        message: 'Failed to save your changes. Please try again.',
        duration: 5000,
      }));
    } finally {
      setIsSaving(false);
    }
  };

  // Set the ref so auto-save can access the function
  handleSaveRef.current = handleSave;

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const renderPreview = () => {
    const contentToRender = content || '';
    const html = marked.parse(contentToRender, { async: false }) as string;
    const sanitizedHtml = DOMPurify.sanitize(html);
    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
  };

  const getWordCount = () => {
    if (!content) return 0;
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const toggleView = () => {
    if (isSplitView) {
      setIsPreviewMode(!isPreviewMode);
      setIsSplitView(false);
    } else {
      setIsSplitView(true);
      setIsPreviewMode(false);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Save before page unload
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Try to save before leaving
        handleSave();

        // Show confirmation dialog
        event.preventDefault();
        return 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, handleSave]);

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges) {
      const timer = setTimeout(() => {
        handleSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timer);
    }
  }, [content, hasUnsavedChanges, handleSave]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        toggleView();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, toggleView]);

  return (
    <div className={`paper-editor ${isFullscreen ? 'fullscreen' : ''}`}>
      <div className="editor-header">
        <div className="editor-title">
          <h2>{paper.title}</h2>
          <div className="editor-meta">
            <span className="word-count">{getWordCount()} words</span>
            <span className="status-indicator">
              {hasUnsavedChanges ? (
                <span className="unsaved">● Unsaved changes</span>
              ) : (
                <span className="saved">✓ Saved</span>
              )}
            </span>
          </div>
        </div>

        <div className="editor-actions">
          <button
            onClick={toggleView}
            className="editor-button"
            title={isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode'}
          >
            {isSplitView ? <Split size={16} /> : isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}
            {isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview'}
          </button>

          <button
            onClick={toggleFullscreen}
            className="editor-button"
            title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
          >
            <Maximize2 size={16} />
            {isFullscreen ? 'Exit' : 'Fullscreen'}
          </button>

          <button
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isSaving}
            className="editor-button save-button"
            title={`Save changes (Ctrl+S) - hasUnsavedChanges: ${hasUnsavedChanges}, isSaving: ${isSaving}`}
          >
            <Save size={16} />
            {isSaving ? 'Saving...' : hasUnsavedChanges ? 'Save' : 'Saved'}
          </button>

          <button
            onClick={handleExport}
            className="editor-button"
            title="Export as Markdown"
          >
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      <div className="editor-content">
        {isSplitView ? (
          <div className="split-view">
            <div className="edit-pane">
              <Editor
                height="100%"
                defaultLanguage="markdown"
                value={content}
                onChange={handleContentChange}
                onMount={(editor) => {
                  // Track cursor position changes
                  editor.onDidChangeCursorPosition(() => {
                    handleCursorPositionChange(editor);
                  });
                }}
                theme="vs-light"
                options={{
                  minimap: { enabled: false },
                  wordWrap: 'on',
                  lineNumbers: 'on',
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                  fontSize: 14,
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  padding: { top: 16, bottom: 16 },
                  suggest: {
                    showKeywords: false,
                    showSnippets: false,
                  },
                }}
              />
            </div>
            <div className="preview-pane">
              <div className="markdown-content">
                {renderPreview()}
              </div>
            </div>
          </div>
        ) : isPreviewMode ? (
          <div className="preview-pane full">
            <div className="markdown-content">
              {renderPreview()}
            </div>
          </div>
        ) : (
          <div className="edit-pane full">
            <Editor
              height="100%"
              defaultLanguage="markdown"
              value={content}
              onChange={handleContentChange}
              onMount={(editor) => {
                // Track cursor position changes
                editor.onDidChangeCursorPosition(() => {
                  handleCursorPositionChange(editor);
                });
              }}
              theme="vs-light"
              options={{
                minimap: { enabled: true },
                wordWrap: 'on',
                lineNumbers: 'on',
                scrollBeyondLastLine: false,
                automaticLayout: true,
                fontSize: 14,
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                padding: { top: 20, bottom: 20 },
              }}
            />
          </div>
        )}
      </div>

      <div className="editor-footer">
        <div className="editor-stats">
          <span>Characters: {content?.length || 0}</span>
          <span>Words: {getWordCount()}</span>
          <span>Lines: {content?.split('\n').length || 0}</span>
        </div>

        <div className="editor-help">
          <span>Markdown supported • Ctrl+S to save • Ctrl+P to toggle view</span>
        </div>
      </div>
    </div>
  );
};

export default PaperEditor;
