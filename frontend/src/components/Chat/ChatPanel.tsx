import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { addMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';
import { addNotification } from '../../store/slices/uiSlice';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ToolStatusList from './ToolStatusList';
import { Send } from 'lucide-react';
import './ChatPanel.css';

const ChatPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentPaper } = useSelector((state: RootState) => state.papers);
  const [inputValue, setInputValue] = useState('');
  const eventSourceRef = useRef<EventSource | null>(null);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isStreaming || isLoading) return;

    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: inputValue.trim(),
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMessage));
    setInputValue('');
    dispatch(setLoading(true));

    try {
      // Start streaming chat
      const response = await fetch('http://localhost:8000/api/v1/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputValue.trim(),
          session_id: currentSessionId,
          paper_id: currentPaper?.paper_id,
          stream: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Close existing EventSource if any
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      // Create new EventSource for streaming
      const eventSource = new EventSource(`http://localhost:8000/api/v1/chat/stream?session_id=${currentSessionId}&message=${encodeURIComponent(inputValue.trim())}`);
      eventSourceRef.current = eventSource;

      dispatch(setLoading(false));
      dispatch(setStreaming(true));

      let assistantMessageId: string | null = null;
      let assistantContent = '';

      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);

        switch (data.event) {
          case 'session':
            // Handle session info
            break;

          case 'start':
            // Create assistant message
            assistantMessageId = Date.now().toString();
            dispatch(addMessage({
              id: assistantMessageId,
              role: 'assistant',
              content: '',
              timestamp: new Date().toISOString(),
            }));
            break;

          case 'step':
            // Handle intermediate steps (tool executions)
            if (data.data.type === 'tool_call') {
              dispatch(addNotification({
                type: 'info',
                title: 'Tool Execution',
                message: `Executing ${data.data.tool_name}...`,
                duration: 3000,
              }));
            }
            break;

          case 'response':
            // Update assistant message content
            if (assistantMessageId && data.data.message) {
              assistantContent = data.data.message;
              dispatch(addMessage({
                id: assistantMessageId,
                role: 'assistant',
                content: assistantContent,
                timestamp: new Date().toISOString(),
              }));
            }
            break;

          case 'complete':
            dispatch(setStreaming(false));
            eventSource.close();
            break;

          case 'error':
            dispatch(setStreaming(false));
            dispatch(addNotification({
              type: 'error',
              title: 'Chat Error',
              message: data.data.error || 'An error occurred',
              duration: 5000,
            }));
            eventSource.close();
            break;
        }
      };

      eventSource.onerror = () => {
        dispatch(setStreaming(false));
        dispatch(addNotification({
          type: 'error',
          title: 'Connection Error',
          message: 'Lost connection to the server',
          duration: 5000,
        }));
        eventSource.close();
      };

    } catch (error) {
      dispatch(setLoading(false));
      dispatch(addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to send message',
        duration: 5000,
      }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Cleanup EventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  if (sidebarCollapsed) {
    return (
      <div className="chat-panel-collapsed">
        <div className="collapsed-icon">
          <Send size={20} />
        </div>
      </div>
    );
  }

  return (
    <div className="chat-panel-content">
      <div className="chat-header">
        <h2>Chat</h2>
        {currentPaper && (
          <div className="current-paper-indicator">
            <span>📄 {currentPaper.title}</span>
          </div>
        )}
      </div>

      <div className="chat-body">
        <MessageList messages={messages} />
        <ToolStatusList />
      </div>

      <div className="chat-footer">
        <MessageInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSendMessage}
          onKeyPress={handleKeyPress}
          disabled={isStreaming || isLoading}
          placeholder="Ask me anything about research papers..."
        />
      </div>
    </div>
  );
};

export default ChatPanel;
