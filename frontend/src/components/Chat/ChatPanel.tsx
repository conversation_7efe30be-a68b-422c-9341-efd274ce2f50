import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { addChart } from '../../store/slices/toolsSlice';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ToolStatusList from './ToolStatusList';
import { Send } from 'lucide-react';
import api from '../../services/api';
import './ChatPanel.css';

const ChatPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentPaper, cursorPosition } = useSelector((state: RootState) => state.papers);
  const [inputValue, setInputValue] = useState('');

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isStreaming || isLoading) return;

    console.log('Sending message:', inputValue.trim());
    console.log('Current session ID:', currentSessionId);
    console.log('Current paper ID:', currentPaper?.paper_id);

    const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const userMessage = {
      id: userMessageId,
      role: 'user' as const,
      content: inputValue.trim(),
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMessage));
    const messageContent = inputValue.trim(); // Store before clearing
    setInputValue('');
    dispatch(setLoading(true));

    try {
      // Create assistant message placeholder
      const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      dispatch(addMessage({
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
      }));

      dispatch(setLoading(false));
      dispatch(setStreaming(true));

      // Start streaming chat
      const response = await api.chat.sendMessage(
        messageContent,
        currentSessionId || 'default-session',
        currentPaper?.paper_id,
        cursorPosition
      );

      console.log('Response status:', response.status, response.ok);

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Read the streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let assistantContent = '';

      console.log('Starting to read streaming response...');

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log('Streaming completed');
            break;
          }

          const chunk = decoder.decode(value);
          console.log('Received chunk:', chunk);
          const lines = chunk.split('\n');

          let currentEvent = '';

          for (const line of lines) {
            if (line.startsWith('event: ')) {
              currentEvent = line.slice(7).trim();
              console.log('Event type:', currentEvent);
            } else if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                console.log('Event data:', currentEvent, data);

                switch (currentEvent) {
                  case 'start':
                    console.log('Chat started');
                    break;

                  case 'step':
                    if (data.type === 'tool_call') {
                      dispatch(addNotification({
                        type: 'info',
                        title: 'Tool Execution',
                        message: `Executing ${data.tool_name}...`,
                        duration: 3000,
                      }));
                    } else if (data.type === 'tool_result') {
                      // Handle tool results, especially chart creation
                      if (data.tool_name && data.tool_name.includes('chart') && data.result?.success) {
                        const result = data.result;
                        dispatch(addChart({
                          type: result.chart_type || 'line',
                          title: result.title || 'Chart',
                          url: result.chart_url,
                          data: result.data || {},
                        }));

                        dispatch(addNotification({
                          type: 'success',
                          title: 'Chart Created',
                          message: `${result.title || 'Chart'} has been created successfully!`,
                          duration: 5000,
                        }));
                      }
                    } else if (data.type === 'agent_response' && data.content) {
                      // This is the actual response content
                      assistantContent = data.content;
                      console.log('Updating message with content from step:', assistantContent);
                      dispatch(updateMessage({
                        id: assistantMessageId,
                        content: assistantContent,
                      }));
                    }
                    break;

                  case 'response':
                    // This event contains metadata, the actual content is in the step event
                    console.log('Response event received:', data);
                    break;

                  case 'complete':
                    console.log('Chat completed');
                    dispatch(setStreaming(false));
                    return;

                  case 'error':
                    console.error('Chat error:', data);
                    dispatch(setStreaming(false));
                    dispatch(addNotification({
                      type: 'error',
                      title: 'Chat Error',
                      message: data.error || 'An error occurred',
                      duration: 5000,
                    }));
                    return;
                }
              } catch (e) {
                // Skip invalid JSON lines
                console.warn('Failed to parse SSE data:', line, e);
              }
            }
          }
        }
      }

      dispatch(setStreaming(false));

    } catch (error) {
      console.error('Chat error:', error);
      dispatch(setStreaming(false));
      dispatch(setLoading(false));
      dispatch(addNotification({
        type: 'error',
        title: 'Chat Error',
        message: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: 5000,
      }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const testConnection = async () => {
    try {
      console.log('Testing backend connection...');
      const response = await fetch('http://localhost:8000/api/v1/health');
      const data = await response.json();
      console.log('Backend health check:', data);

      dispatch(addNotification({
        type: 'success',
        title: 'Connection Test',
        message: `Backend is ${data.status}`,
        duration: 3000,
      }));
    } catch (error) {
      console.error('Connection test failed:', error);
      dispatch(addNotification({
        type: 'error',
        title: 'Connection Test',
        message: 'Failed to connect to backend',
        duration: 5000,
      }));
    }
  };



  if (sidebarCollapsed) {
    return (
      <div className="chat-panel-collapsed">
        <div className="collapsed-icon">
          <Send size={20} />
        </div>
      </div>
    );
  }

  return (
    <div className="chat-panel-content">
      <div className="chat-header">
        <h2>Chat</h2>
        <button onClick={testConnection} style={{ fontSize: '12px', padding: '4px 8px' }}>
          Test Connection
        </button>
        {currentPaper && (
          <div className="current-paper-indicator">
            <span>📄 {currentPaper.title}</span>
          </div>
        )}
      </div>

      <div className="chat-body">
        <MessageList messages={messages} />
        <ToolStatusList />
      </div>

      <div className="chat-footer">
        <MessageInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSendMessage}
          onKeyPress={handleKeyPress}
          disabled={isStreaming || isLoading}
          placeholder="Ask me anything about research papers..."
        />
      </div>
    </div>
  );
};

export default ChatPanel;
