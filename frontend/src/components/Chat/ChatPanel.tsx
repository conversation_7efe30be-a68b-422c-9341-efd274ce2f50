import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { addMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';
import { addNotification } from '../../store/slices/uiSlice';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ToolStatusList from './ToolStatusList';
import { Send } from 'lucide-react';
import api from '../../services/api';
import './ChatPanel.css';

const ChatPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentPaper } = useSelector((state: RootState) => state.papers);
  const [inputValue, setInputValue] = useState('');

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isStreaming || isLoading) return;

    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: inputValue.trim(),
      timestamp: new Date().toISOString(),
    };

    dispatch(addMessage(userMessage));
    setInputValue('');
    dispatch(setLoading(true));

    try {
      // Create assistant message placeholder
      const assistantMessageId = Date.now().toString();
      dispatch(addMessage({
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
      }));

      dispatch(setLoading(false));
      dispatch(setStreaming(true));

      // Start streaming chat
      const response = await api.chat.sendMessage(
        inputValue.trim(),
        currentSessionId || '',
        currentPaper?.paper_id
      );

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      // Read the streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let assistantContent = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                switch (data.event) {
                  case 'start':
                    // Message already created
                    break;

                  case 'step':
                    if (data.data.type === 'tool_call') {
                      dispatch(addNotification({
                        type: 'info',
                        title: 'Tool Execution',
                        message: `Executing ${data.data.tool_name}...`,
                        duration: 3000,
                      }));
                    }
                    break;

                  case 'response':
                    if (data.data.message) {
                      assistantContent = data.data.message;
                      dispatch(updateMessage({
                        id: assistantMessageId,
                        content: assistantContent,
                      }));
                    }
                    break;

                  case 'complete':
                    dispatch(setStreaming(false));
                    return;

                  case 'error':
                    dispatch(setStreaming(false));
                    dispatch(addNotification({
                      type: 'error',
                      title: 'Chat Error',
                      message: data.data.error || 'An error occurred',
                      duration: 5000,
                    }));
                    return;
                }
              } catch (e) {
                // Skip invalid JSON lines
              }
            }
          }
        }
      }

      dispatch(setStreaming(false));

    } catch (error) {
      dispatch(setLoading(false));
      dispatch(addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to send message',
        duration: 5000,
      }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };



  if (sidebarCollapsed) {
    return (
      <div className="chat-panel-collapsed">
        <div className="collapsed-icon">
          <Send size={20} />
        </div>
      </div>
    );
  }

  return (
    <div className="chat-panel-content">
      <div className="chat-header">
        <h2>Chat</h2>
        {currentPaper && (
          <div className="current-paper-indicator">
            <span>📄 {currentPaper.title}</span>
          </div>
        )}
      </div>

      <div className="chat-body">
        <MessageList messages={messages} />
        <ToolStatusList />
      </div>

      <div className="chat-footer">
        <MessageInput
          value={inputValue}
          onChange={setInputValue}
          onSend={handleSendMessage}
          onKeyPress={handleKeyPress}
          disabled={isStreaming || isLoading}
          placeholder="Ask me anything about research papers..."
        />
      </div>
    </div>
  );
};

export default ChatPanel;
