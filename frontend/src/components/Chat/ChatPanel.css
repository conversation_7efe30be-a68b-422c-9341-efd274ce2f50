.chat-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.chat-panel-collapsed {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.collapsed-icon {
  color: #64748b;
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Chat Header */
.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.chat-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.current-paper-indicator {
  font-size: 12px;
  color: #64748b;
  background-color: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.current-paper-indicator span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

/* Chat Body */
.chat-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Chat Footer */
.chat-footer {
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background-color: white;
}

/* Message List Styles */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.message-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Message Bubble */
.message-bubble {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message-bubble.user {
  align-self: flex-end;
  background-color: #3b82f6;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-bubble.assistant {
  align-self: flex-start;
  background-color: #f1f5f9;
  color: #1e293b;
  border-bottom-left-radius: 4px;
  border: 1px solid #e2e8f0;
}

.message-bubble.system {
  align-self: center;
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
  font-size: 12px;
  max-width: 70%;
  text-align: center;
}

/* Message Metadata */
.message-metadata {
  font-size: 11px;
  color: #64748b;
  margin-top: 4px;
  text-align: right;
}

.message-bubble.assistant .message-metadata {
  text-align: left;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f1f5f9;
  border-radius: 12px;
  border-bottom-left-radius: 4px;
  border: 1px solid #e2e8f0;
  max-width: 85%;
  align-self: flex-start;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background-color: #64748b;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Tool Status */
.tool-status-list {
  padding: 8px 20px;
  border-top: 1px solid #f1f5f9;
  background-color: #fafbfc;
  max-height: 120px;
  overflow-y: auto;
}

.tool-status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 12px;
  color: #64748b;
}

.tool-status-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tool-status-icon.running {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.tool-status-icon.completed {
  color: #10b981;
}

.tool-status-icon.failed {
  color: #ef4444;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Progress Bar */
.tool-progress {
  flex: 1;
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin: 0 8px;
}

.tool-progress-bar {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* Message Input Styles */
.message-input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.message-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.message-input {
  flex: 1;
  min-height: 20px;
  max-height: 120px;
  padding: 8px 12px;
  border: none;
  background-color: white;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.4;
  resize: none;
  outline: none;
}

.message-input:focus {
  box-shadow: 0 0 0 2px #3b82f6;
}

.send-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.send-button:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #64748b;
  padding: 0 4px;
}

.character-count {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.input-hint {
  font-style: italic;
}

/* Empty State Styles */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #64748b;
}

.empty-state-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.empty-state-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.empty-state-content ul {
  text-align: left;
  max-width: 300px;
  margin: 0 auto 16px auto;
  padding-left: 20px;
}

.empty-state-content li {
  margin-bottom: 8px;
  font-size: 14px;
}

/* Message Bubble Enhancements */
.message-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.message-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-role {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-content {
  line-height: 1.5;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin: 16px 0 8px 0;
  color: inherit;
}

.message-content p {
  margin: 8px 0;
}

.message-content ul,
.message-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message-content code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 0.9em;
}

.message-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-steps {
  margin-top: 12px;
  border-top: 1px solid #e2e8f0;
  padding-top: 8px;
}

.message-steps summary {
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  padding: 4px 0;
}

.steps-list {
  margin-top: 8px;
  padding-left: 16px;
}

.step-item {
  margin-bottom: 8px;
  font-size: 12px;
}

.step-header {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.step-type {
  background-color: #dbeafe;
  color: #1d4ed8;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
}

.step-tool {
  background-color: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
}

.step-result {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 8px;
  margin-top: 4px;
}

.step-result pre {
  margin: 0;
  font-size: 11px;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }

  .chat-footer {
    padding: 12px 16px;
  }

  .message-list {
    padding: 12px 16px;
  }

  .message-bubble {
    max-width: 90%;
    padding: 10px 12px;
    font-size: 13px;
  }

  .current-paper-indicator span {
    max-width: 200px;
  }

  .message-input-wrapper {
    padding: 8px;
  }

  .send-button {
    width: 32px;
    height: 32px;
  }

  .empty-state {
    padding: 20px 16px;
  }
}

/* Dark theme support */
.dark .chat-panel-content {
  background-color: #1e293b;
  color: white;
}

.dark .chat-header {
  background-color: #0f172a;
  border-bottom-color: #334155;
}

.dark .chat-header h2 {
  color: white;
}

.dark .current-paper-indicator {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .message-bubble.assistant {
  background-color: #334155;
  color: #e2e8f0;
  border-color: #475569;
}

.dark .chat-footer {
  background-color: #1e293b;
  border-top-color: #334155;
}