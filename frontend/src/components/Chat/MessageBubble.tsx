import React from 'react';
import { ChatMessage } from '../../store/slices/chatSlice';
import { User, Bot, Info } from 'lucide-react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderContent = () => {
    if (message.role === 'assistant') {
      // Render markdown for assistant messages
      const html = marked.parse(message.content, { async: false }) as string;
      const sanitizedHtml = DOMPurify.sanitize(html);
      return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
    }
    return <div>{message.content}</div>;
  };

  const getIcon = () => {
    switch (message.role) {
      case 'user':
        return <User size={16} />;
      case 'assistant':
        return <Bot size={16} />;
      case 'system':
        return <Info size={16} />;
      default:
        return null;
    }
  };

  return (
    <div className={`message-bubble ${message.role}`}>
      <div className="message-header">
        <div className="message-icon">
          {getIcon()}
        </div>
        <div className="message-role">
          {message.role === 'user' ? 'You' : message.role === 'assistant' ? 'AI Assistant' : 'System'}
        </div>
      </div>

      <div className="message-content">
        {renderContent()}
      </div>

      {message.metadata?.intermediateSteps && message.metadata.intermediateSteps.length > 0 && (
        <div className="message-steps">
          <details>
            <summary>Tool Executions ({message.metadata.intermediateSteps.length})</summary>
            <div className="steps-list">
              {message.metadata.intermediateSteps.map((step, index) => (
                <div key={index} className="step-item">
                  <div className="step-header">
                    <span className="step-type">{step.type}</span>
                    {step.tool_name && <span className="step-tool">{step.tool_name}</span>}
                  </div>
                  {step.result && (
                    <div className="step-result">
                      <pre>{JSON.stringify(step.result, null, 2)}</pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </details>
        </div>
      )}

      <div className="message-metadata">
        {formatTimestamp(message.timestamp)}
      </div>
    </div>
  );
};

export default MessageBubble;
