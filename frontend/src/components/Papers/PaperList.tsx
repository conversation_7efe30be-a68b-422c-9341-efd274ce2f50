import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { setPapers, setCurrentPaper, setSearchQuery } from '../../store/slices/papersSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { Search, Plus, FileText } from 'lucide-react';
import api from '../../services/api';
import './PaperList.css';

const PaperList: React.FC = () => {
  const dispatch = useDispatch();
  const { papers, filteredPapers, currentPaper, searchQuery, isLoading } = useSelector((state: RootState) => state.papers);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);

  const loadPapers = useCallback(async () => {
    try {
      const result = await api.papers.list();
      if (result.data) {
        dispatch(setPapers(result.data.papers || []));
      } else {
        throw new Error(result.error || 'Failed to load papers');
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load papers',
        duration: 5000,
      }));
    }
  }, [dispatch]);

  useEffect(() => {
    // Load papers on component mount
    loadPapers();
  }, [loadPapers]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setLocalSearchQuery(query);
    dispatch(setSearchQuery(query));
  };

  const handlePaperSelect = (paper: any) => {
    dispatch(setCurrentPaper(paper));
  };

  const handleCreatePaper = async () => {
    try {
      const title = `New Paper ${new Date().toLocaleDateString()}`;
      const content = `# ${title}\n\n## Introduction\n\nStart writing your paper here...`;

      const result = await api.papers.create(title, content);

      if (result.data) {
        loadPapers(); // Reload papers list
        dispatch(addNotification({
          type: 'success',
          title: 'Paper Created',
          message: `Created "${title}"`,
          duration: 3000,
        }));
      } else {
        throw new Error(result.error || 'Failed to create paper');
      }
    } catch (error) {
      dispatch(addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to create paper',
        duration: 5000,
      }));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="paper-list">
      <div className="paper-list-header">
        <h3 className="paper-list-title">Papers</h3>

        <div className="paper-search-container">
          <div className="search-input-wrapper">
            <Search size={16} className="search-icon" />
            <input
              type="text"
              placeholder="Search papers..."
              value={localSearchQuery}
              onChange={handleSearchChange}
              className="paper-search"
            />
          </div>
        </div>

        <button
          onClick={handleCreatePaper}
          className="create-paper-button"
          title="Create new paper"
        >
          <Plus size={16} />
          <span>New Paper</span>
        </button>
      </div>

      <div className="paper-list-content">
        {isLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <span>Loading papers...</span>
          </div>
        ) : filteredPapers.length === 0 ? (
          <div className="empty-state">
            {papers.length === 0 ? (
              <>
                <FileText size={48} className="empty-icon" />
                <h4>No Papers Yet</h4>
                <p>Create your first research paper to get started.</p>
                <button onClick={handleCreatePaper} className="create-first-paper">
                  <Plus size={16} />
                  Create First Paper
                </button>
              </>
            ) : (
              <>
                <Search size={48} className="empty-icon" />
                <h4>No Results Found</h4>
                <p>No papers match your search query.</p>
              </>
            )}
          </div>
        ) : (
          <div className="papers-grid">
            {filteredPapers.map((paper) => (
              <div
                key={paper.paper_id}
                className={`paper-item ${currentPaper?.paper_id === paper.paper_id ? 'active' : ''}`}
                onClick={() => handlePaperSelect(paper)}
              >
                <div className="paper-item-header">
                  <h4 className="paper-item-title">{paper.title}</h4>
                  <span className={`paper-item-status ${paper.status}`}>
                    {paper.status.replace('_', ' ')}
                  </span>
                </div>

                <div className="paper-item-content">
                  {paper.content && (
                    <p className="paper-item-preview">
                      {paper.content.replace(/[#*`]/g, '').substring(0, 100)}...
                    </p>
                  )}
                </div>

                <div className="paper-item-meta">
                  <span className="paper-item-date">
                    Updated {formatDate(paper.updated_at)}
                  </span>
                  {paper.metadata?.word_count && (
                    <span className="paper-item-words">
                      {paper.metadata.word_count} words
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PaperList;
