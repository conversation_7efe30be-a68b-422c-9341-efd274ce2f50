.paper-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.paper-list-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.paper-list-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.paper-search-container {
  margin-bottom: 16px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #64748b;
  z-index: 1;
}

.paper-search {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  transition: all 0.2s ease;
}

.paper-search:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.create-paper-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-paper-button:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.paper-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.papers-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.paper-item {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
  position: relative;
}

.paper-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.paper-item.active {
  border-color: #3b82f6;
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.paper-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.paper-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.paper-item-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.paper-item-status.draft {
  background-color: #fef3c7;
  color: #92400e;
}

.paper-item-status.in_progress {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.paper-item-status.completed {
  background-color: #d1fae5;
  color: #065f46;
}

.paper-item-content {
  margin-bottom: 12px;
}

.paper-item-preview {
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.paper-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #94a3b8;
}

.paper-item-date {
  font-weight: 500;
}

.paper-item-words {
  background-color: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

.empty-icon {
  color: #cbd5e1;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
  max-width: 300px;
  line-height: 1.5;
}

.create-first-paper {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-first-paper:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .paper-list-header {
    padding: 12px 16px;
  }
  
  .paper-list-content {
    padding: 12px 16px;
  }
  
  .paper-item {
    padding: 12px;
  }
  
  .paper-item-title {
    font-size: 15px;
  }
  
  .paper-item-preview {
    font-size: 13px;
    -webkit-line-clamp: 3;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
}

/* Dark theme support */
.dark .paper-list {
  background-color: #1e293b;
}

.dark .paper-list-header {
  background-color: #0f172a;
  border-bottom-color: #334155;
}

.dark .paper-list-title {
  color: #e2e8f0;
}

.dark .paper-search {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .paper-search:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.dark .paper-item {
  background-color: #334155;
  border-color: #475569;
}

.dark .paper-item:hover {
  border-color: #60a5fa;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.15);
}

.dark .paper-item.active {
  background-color: rgba(96, 165, 250, 0.1);
  border-color: #60a5fa;
}

.dark .paper-item-title {
  color: #e2e8f0;
}

.dark .paper-item-preview {
  color: #94a3b8;
}

.dark .paper-item-words {
  background-color: #475569;
  color: #e2e8f0;
}

.dark .empty-state h4 {
  color: #e2e8f0;
}
