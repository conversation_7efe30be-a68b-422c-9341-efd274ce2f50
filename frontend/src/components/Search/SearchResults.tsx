import React, { useState } from 'react';
import { SearchResult } from '../../store/slices/toolsSlice';
import { Search, ExternalLink, Calendar, Globe } from 'lucide-react';

interface SearchResultsProps {
  results: SearchResult[];
}

const SearchResults: React.FC<SearchResultsProps> = ({ results }) => {
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(results[0] || null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (results.length === 0) {
    return (
      <div className="search-results-empty">
        <Search size={64} className="empty-icon" />
        <h3>No Search Results</h3>
        <p>Use the chat to search for research papers and view results here.</p>
      </div>
    );
  }

  return (
    <div className="search-results">
      <div className="results-sidebar">
        <h3 className="sidebar-title">Search Results</h3>
        <div className="results-list">
          {results.map((result) => (
            <div
              key={result.id}
              className={`result-list-item ${selectedResult?.id === result.id ? 'active' : ''}`}
              onClick={() => setSelectedResult(result)}
            >
              <div className="result-list-header">
                <div className="result-query">{result.query}</div>
                <div className="result-count">{result.count} results</div>
              </div>
              <div className="result-meta">
                <span className="result-source">{result.source}</span>
                <span className="result-date">{formatDate(result.timestamp)}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="results-main">
        {selectedResult ? (
          <>
            <div className="results-header">
              <div className="results-info">
                <h2 className="results-title">"{selectedResult.query}"</h2>
                <div className="results-meta">
                  <span className="results-count">
                    {selectedResult.count} results found
                  </span>
                  <span className="results-source">
                    <Globe size={16} />
                    {selectedResult.source}
                  </span>
                  <span className="results-date">
                    <Calendar size={16} />
                    {formatDate(selectedResult.timestamp)}
                  </span>
                </div>
              </div>
            </div>

            <div className="results-content">
              {selectedResult.results.map((item, index) => (
                <div key={index} className="result-item">
                  <div className="result-item-header">
                    <h4 className="result-title">
                      {item.title || `Result ${index + 1}`}
                    </h4>
                    {item.url && (
                      <a
                        href={item.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="result-link"
                        title="Open in new tab"
                      >
                        <ExternalLink size={16} />
                      </a>
                    )}
                  </div>

                  {item.description && (
                    <p className="result-description">{item.description}</p>
                  )}

                  {item.snippet && (
                    <div className="result-snippet">
                      <p>{item.snippet}</p>
                    </div>
                  )}

                  {item.authors && (
                    <div className="result-authors">
                      <strong>Authors: <AUTHORS>
                    </div>
                  )}

                  {item.published_date && (
                    <div className="result-published">
                      <strong>Published:</strong> {item.published_date}
                    </div>
                  )}

                  {item.journal && (
                    <div className="result-journal">
                      <strong>Journal:</strong> {item.journal}
                    </div>
                  )}

                  {item.doi && (
                    <div className="result-doi">
                      <strong>DOI:</strong> 
                      <a
                        href={`https://doi.org/${item.doi}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="doi-link"
                      >
                        {item.doi}
                      </a>
                    </div>
                  )}

                  {item.tags && item.tags.length > 0 && (
                    <div className="result-tags">
                      {item.tags.map((tag: string, tagIndex: number) => (
                        <span key={tagIndex} className="result-tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="no-result-selected">
            <Search size={64} className="empty-icon" />
            <h3>Select Search Results</h3>
            <p>Choose a search query from the sidebar to view its results.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResults;
