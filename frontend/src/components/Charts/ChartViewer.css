.chart-viewer {
  height: 100%;
  display: flex;
  background-color: white;
}

.chart-viewer-empty,
.no-chart-selected {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #64748b;
  padding: 40px;
}

.chart-viewer-empty .empty-icon,
.no-chart-selected .empty-icon {
  color: #cbd5e1;
  margin-bottom: 16px;
}

.chart-viewer-empty h3,
.no-chart-selected h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.chart-viewer-empty p,
.no-chart-selected p {
  margin: 0;
  font-size: 16px;
  max-width: 400px;
  line-height: 1.5;
}

.chart-sidebar {
  width: 300px;
  min-width: 280px;
  border-right: 1px solid #e2e8f0;
  background-color: #f8fafc;
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  padding: 20px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  border-bottom: 1px solid #e2e8f0;
}

.chart-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.chart-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #e2e8f0;
}

.chart-list-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.chart-list-item.active {
  border-color: #3b82f6;
  background-color: #f0f9ff;
}

.chart-list-icon {
  color: #3b82f6;
  flex-shrink: 0;
}

.chart-list-content {
  flex: 1;
  min-width: 0;
}

.chart-list-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-list-type {
  font-size: 12px;
  color: #64748b;
  text-transform: capitalize;
}

.chart-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.chart-info {
  flex: 1;
}

.chart-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.chart-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #64748b;
}

.chart-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.chart-action-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background-color: #f0f9ff;
}

.chart-display {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: white;
}

.chart-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  cursor: zoom-in;
}

.chart-image:hover {
  transform: scale(1.02);
}

.chart-image.zoomed {
  cursor: zoom-out;
  transform: scale(1.5);
}

.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #64748b;
  gap: 16px;
}

.chart-error.hidden {
  display: none;
}

.chart-error h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.chart-error p {
  margin: 0;
  font-size: 14px;
}

.retry-button {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #2563eb;
}

.chart-data {
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.chart-data details {
  padding: 16px 20px;
}

.chart-data summary {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  padding: 4px 0;
}

.data-preview {
  margin: 12px 0 0 0;
  padding: 12px;
  background-color: #1e293b;
  color: #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-viewer {
    flex-direction: column;
  }

  .chart-sidebar {
    width: 100%;
    min-width: auto;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    max-height: 200px;
  }

  .chart-list {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 12px;
  }

  .chart-list-item {
    min-width: 200px;
    margin-bottom: 0;
  }

  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .chart-actions {
    justify-content: flex-end;
  }

  .chart-display {
    padding: 16px;
  }
}

/* Dark theme support */
.dark .chart-viewer {
  background-color: #1e293b;
}

.dark .chart-sidebar {
  background-color: #0f172a;
  border-right-color: #334155;
}

.dark .sidebar-title {
  color: #e2e8f0;
  border-bottom-color: #334155;
}

.dark .chart-list-item {
  background-color: #334155;
  border-color: #475569;
}

.dark .chart-list-item:hover {
  border-color: #60a5fa;
}

.dark .chart-list-item.active {
  background-color: rgba(96, 165, 250, 0.1);
  border-color: #60a5fa;
}

.dark .chart-list-title {
  color: #e2e8f0;
}

.dark .chart-header {
  background-color: #0f172a;
  border-bottom-color: #334155;
}

.dark .chart-title {
  color: #e2e8f0;
}

.dark .chart-action-button {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .chart-action-button:hover {
  border-color: #60a5fa;
  color: #60a5fa;
  background-color: rgba(96, 165, 250, 0.1);
}

.dark .chart-display {
  background-color: #1e293b;
}

.dark .chart-error h4 {
  color: #e2e8f0;
}

.dark .chart-data {
  background-color: #0f172a;
  border-top-color: #334155;
}

.dark .chart-data summary {
  color: #e2e8f0;
}