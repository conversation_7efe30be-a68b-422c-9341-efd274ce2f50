import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { removeChart } from '../../store/slices/toolsSlice';
import { BarChart3, Trash2, ExternalLink, Copy } from 'lucide-react';
import './ChartsList.css';

const ChartsList: React.FC = () => {
  const dispatch = useDispatch();
  const { charts } = useSelector((state: RootState) => state.tools);

  const handleRemoveChart = (chartId: string) => {
    if (window.confirm('Are you sure you want to remove this chart?')) {
      dispatch(removeChart(chartId));
    }
  };

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    // You could add a notification here
  };

  const handleOpenChart = (url: string) => {
    window.open(url, '_blank');
  };

  if (charts.length === 0) {
    return (
      <div className="charts-list-empty">
        <BarChart3 size={48} className="empty-icon" />
        <h3>No Charts Created</h3>
        <p>Charts you create will appear here for easy access.</p>
      </div>
    );
  }

  return (
    <div className="charts-list">
      <div className="charts-header">
        <h2>
          <BarChart3 size={24} />
          Saved Charts ({charts.length})
        </h2>
      </div>
      
      <div className="charts-grid">
        {charts.map((chart) => (
          <div key={chart.id} className="chart-card">
            <div className="chart-preview">
              <img 
                src={chart.url} 
                alt={chart.title}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder-chart.png';
                }}
              />
            </div>
            
            <div className="chart-info">
              <h3 className="chart-title">{chart.title}</h3>
              <div className="chart-meta">
                <span className="chart-type">{chart.type}</span>
                <span className="chart-date">
                  {new Date(chart.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            <div className="chart-actions">
              <button
                onClick={() => handleOpenChart(chart.url)}
                className="action-btn primary"
                title="Open chart in new tab"
              >
                <ExternalLink size={16} />
              </button>
              
              <button
                onClick={() => handleCopyUrl(chart.url)}
                className="action-btn secondary"
                title="Copy chart URL"
              >
                <Copy size={16} />
              </button>
              
              <button
                onClick={() => handleRemoveChart(chart.id)}
                className="action-btn danger"
                title="Remove chart"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChartsList;
