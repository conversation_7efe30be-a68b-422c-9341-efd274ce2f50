import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { BarChart3, Trending<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Circle } from 'lucide-react';

const ChartGallery: React.FC = () => {
  const { charts } = useSelector((state: RootState) => state.tools);

  const getChartIcon = (type: string) => {
    switch (type) {
      case 'line':
        return <TrendingUp size={16} />;
      case 'bar':
        return <BarChart3 size={16} />;
      case 'pie':
        return <PieChart size={16} />;
      case 'doughnut':
        return <Circle size={16} />;
      default:
        return <BarChart3 size={16} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="chart-gallery">
      <div className="chart-gallery-header">
        <h3 className="chart-gallery-title">Charts ({charts.length})</h3>
      </div>

      <div className="chart-gallery-content">
        {charts.length === 0 ? (
          <div className="empty-state">
            <BarChart3 size={48} className="empty-icon" />
            <h4>No Charts Generated</h4>
            <p>Ask the AI to create visualizations from your research data.</p>
            <div className="example-requests">
              <p>Try asking:</p>
              <ul>
                <li>"Create a bar chart of research trends"</li>
                <li>"Generate a pie chart of data distribution"</li>
                <li>"Make a line chart showing progress over time"</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="charts-grid">
            {charts.map((chart) => (
              <div key={chart.id} className="chart-item">
                <div className="chart-item-header">
                  <div className="chart-info">
                    <div className="chart-icon">
                      {getChartIcon(chart.type)}
                    </div>
                    <div className="chart-details">
                      <h4 className="chart-item-title">{chart.title}</h4>
                      <span className="chart-item-type">{chart.type} chart</span>
                    </div>
                  </div>
                  <div className="chart-item-date">
                    {formatDate(chart.created_at)}
                  </div>
                </div>

                <div className="chart-item-preview">
                  <img
                    src={chart.url}
                    alt={chart.title}
                    className="chart-item-image"
                    loading="lazy"
                  />
                </div>

                <div className="chart-item-actions">
                  <button
                    className="chart-action-button"
                    onClick={() => window.open(chart.url, '_blank')}
                  >
                    View Full Size
                  </button>
                  <button
                    className="chart-action-button"
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = chart.url;
                      link.download = `${chart.title}.png`;
                      link.click();
                    }}
                  >
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartGallery;
