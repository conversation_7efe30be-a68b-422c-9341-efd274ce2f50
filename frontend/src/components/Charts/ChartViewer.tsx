import React, { useState } from 'react';
import { Chart } from '../../store/slices/toolsSlice';
import { BarChart3, TrendingUp, <PERSON><PERSON><PERSON>, Circle, Download, ExternalLink } from 'lucide-react';

interface ChartViewerProps {
  charts: Chart[];
}

const ChartViewer: React.FC<ChartViewerProps> = ({ charts }) => {
  const [selectedChart, setSelectedChart] = useState<Chart | null>(charts[0] || null);

  const getChartIcon = (type: string) => {
    switch (type) {
      case 'line':
        return <TrendingUp size={20} />;
      case 'bar':
        return <BarChart3 size={20} />;
      case 'pie':
        return <PieChart size={20} />;
      case 'doughnut':
        return <Circle size={20} />;
      default:
        return <BarChart3 size={20} />;
    }
  };

  const handleDownload = (chart: Chart) => {
    const link = document.createElement('a');
    link.href = chart.url;
    link.download = `${chart.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;
    link.click();
  };

  const handleOpenInNewTab = (chart: Chart) => {
    window.open(chart.url, '_blank');
  };

  if (charts.length === 0) {
    return (
      <div className="chart-viewer-empty">
        <BarChart3 size={64} className="empty-icon" />
        <h3>No Charts Available</h3>
        <p>Ask the AI to create visualizations from your research data.</p>
      </div>
    );
  }

  return (
    <div className="chart-viewer">
      <div className="chart-sidebar">
        <h3 className="sidebar-title">Charts ({charts.length})</h3>
        <div className="chart-list">
          {charts.map((chart) => (
            <div
              key={chart.id}
              className={`chart-list-item ${selectedChart?.id === chart.id ? 'active' : ''}`}
              onClick={() => setSelectedChart(chart)}
            >
              <div className="chart-list-icon">
                {getChartIcon(chart.type)}
              </div>
              <div className="chart-list-content">
                <div className="chart-list-title">{chart.title}</div>
                <div className="chart-list-type">{chart.type} chart</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="chart-main">
        {selectedChart ? (
          <>
            <div className="chart-header">
              <div className="chart-info">
                <h2 className="chart-title">{selectedChart.title}</h2>
                <div className="chart-meta">
                  <span className="chart-type">
                    {getChartIcon(selectedChart.type)}
                    {selectedChart.type} chart
                  </span>
                  <span className="chart-date">
                    Created {new Date(selectedChart.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              <div className="chart-actions">
                <button
                  onClick={() => handleOpenInNewTab(selectedChart)}
                  className="chart-action-button"
                  title="Open in new tab"
                >
                  <ExternalLink size={16} />
                  Open
                </button>
                <button
                  onClick={() => handleDownload(selectedChart)}
                  className="chart-action-button"
                  title="Download chart"
                >
                  <Download size={16} />
                  Download
                </button>
              </div>
            </div>

            <div className="chart-display">
              <img
                src={selectedChart.url}
                alt={selectedChart.title}
                className="chart-image"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="chart-error hidden">
                <BarChart3 size={48} />
                <h4>Failed to Load Chart</h4>
                <p>The chart image could not be loaded.</p>
                <button
                  onClick={() => handleOpenInNewTab(selectedChart)}
                  className="retry-button"
                >
                  Try Opening in New Tab
                </button>
              </div>
            </div>

            {selectedChart.data && (
              <div className="chart-data">
                <details>
                  <summary>Chart Data</summary>
                  <pre className="data-preview">
                    {JSON.stringify(selectedChart.data, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </>
        ) : (
          <div className="no-chart-selected">
            <BarChart3 size={64} className="empty-icon" />
            <h3>Select a Chart</h3>
            <p>Choose a chart from the sidebar to view it here.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartViewer;
