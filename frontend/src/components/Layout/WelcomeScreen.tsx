import React from 'react';
import { Search, FileText, BarChart3, Database, Zap, Brain } from 'lucide-react';

const WelcomeScreen: React.FC = () => {
  const features = [
    {
      icon: <Search size={24} />,
      title: 'Smart Research',
      description: 'Search academic papers and conduct deep research analysis with AI-powered insights.'
    },
    {
      icon: <FileText size={24} />,
      title: 'Live Paper Editor',
      description: 'Write and edit research papers with real-time markdown rendering and auto-save.'
    },
    {
      icon: <BarChart3 size={24} />,
      title: 'Data Visualization',
      description: 'Generate interactive charts and graphs to visualize your research findings.'
    },
    {
      icon: <Database size={24} />,
      title: 'Data Management',
      description: 'Store and organize your research data with integrated Airtable support.'
    },
    {
      icon: <Zap size={24} />,
      title: 'Real-time Streaming',
      description: 'Watch AI tools execute in real-time with live progress updates and results.'
    },
    {
      icon: <Brain size={24} />,
      title: 'AI Assistant',
      description: 'Get intelligent help with research, writing, and data analysis tasks.'
    }
  ];

  const exampleQueries = [
    'Research AI applications in healthcare',
    'Create a chart showing research methodology trends',
    'Write a paper about machine learning',
    'Search for papers on quantum computing',
    'Analyze climate change research data',
    'Generate a literature review outline'
  ];

  const handleExampleClick = (query: string) => {
    // TODO: Implement sending example query to chat
    console.log('Example query clicked:', query);
  };

  return (
    <div className="welcome-screen">
      <div className="welcome-content">
        <h1 className="welcome-title">
          Welcome to Paper Agent
        </h1>

        <p className="welcome-subtitle">
          Your AI-powered research assistant for academic papers, data analysis, and scientific writing.
          Powered by LangGraph with real-time streaming and advanced tool integration.
        </p>

        <div className="welcome-features">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                {feature.icon}
              </div>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="welcome-cta">
          <p className="cta-text">
            Get started by asking a question in the chat panel, or try one of these examples:
          </p>

          <div className="example-queries">
            {exampleQueries.map((query, index) => (
              <button
                key={index}
                className="example-query"
                onClick={() => handleExampleClick(query)}
              >
                {query}
              </button>
            ))}
          </div>
        </div>


      </div>
    </div>
  );
};

export default WelcomeScreen;
