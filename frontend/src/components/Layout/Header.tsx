import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { toggleSidebar, toggleRightPanel, setTheme } from '../../store/slices/uiSlice';
import {
  FileText,
  Settings,
  Download,
  Sun,
  Moon,
  Menu,
  PanelLeftClose,
  PanelRightClose,
  Wifi,
  WifiOff
} from 'lucide-react';
import './Header.css';

const Header: React.FC = () => {
  const dispatch = useDispatch();
  const { theme, sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentSession, isConnected } = useSelector((state: RootState) => state.session);
  const { isStreaming } = useSelector((state: RootState) => state.chat);

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  const handleToggleRightPanel = () => {
    dispatch(toggleRightPanel());
  };

  const handleToggleTheme = () => {
    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export functionality to be implemented');
  };

  return (
    <header className={`header ${theme}`}>
      <div className="header-left">
        <button
          className="header-button"
          onClick={handleToggleSidebar}
          title={sidebarCollapsed ? 'Show chat panel' : 'Hide chat panel'}
        >
          {sidebarCollapsed ? <Menu size={20} /> : <PanelLeftClose size={20} />}
        </button>

        <div className="header-logo">
          <FileText size={24} className="logo-icon" />
          <h1 className="logo-text">Paper Agent</h1>
        </div>
      </div>

      <div className="header-center">
        {currentSession && (
          <div className="session-info">
            <div className="session-status">
              {isConnected ? (
                <Wifi size={16} className="status-icon connected" />
              ) : (
                <WifiOff size={16} className="status-icon disconnected" />
              )}
              <span className="session-id">
                Session: {currentSession.session_id.slice(0, 8)}...
              </span>
            </div>

            {isStreaming && (
              <div className="streaming-indicator">
                <div className="streaming-dot"></div>
                <span>AI is thinking...</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="header-right">
        <button
          className="header-button"
          onClick={handleToggleTheme}
          title={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
        >
          {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
        </button>

        <button
          className="header-button"
          onClick={handleExport}
          title="Export data"
        >
          <Download size={20} />
        </button>

        <button
          className="header-button"
          title="Settings"
        >
          <Settings size={20} />
        </button>

        <button
          className="header-button"
          onClick={handleToggleRightPanel}
          title={rightPanelCollapsed ? 'Show context panel' : 'Hide context panel'}
        >
          {rightPanelCollapsed ? <Menu size={20} /> : <PanelRightClose size={20} />}
        </button>
      </div>
    </header>
  );
};

export default Header;
