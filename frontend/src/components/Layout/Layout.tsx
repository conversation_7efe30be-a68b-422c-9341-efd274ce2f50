import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import Header from './Header';
import ChatPanel from '../Chat/ChatPanel';
import MainWorkArea from './MainWorkArea';
import ContextPanel from './ContextPanel';
import NotificationContainer from '../UI/NotificationContainer';
import { setCurrentSession } from '../../store/slices/sessionSlice';
import './Layout.css';

const Layout: React.FC = () => {
  const dispatch = useDispatch();
  const { sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentSession } = useSelector((state: RootState) => state.session);

  const initializeSession = async () => {
    try {
      const response = await fetch('/api/v1/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const sessionData = await response.json();
        dispatch(setCurrentSession({
          session_id: sessionData.session_id,
          created_at: sessionData.created_at,
          last_activity: new Date().toISOString(),
          metadata: {},
        }));
      }
    } catch (error) {
      console.error('Failed to initialize session:', error);
    }
  };

  useEffect(() => {
    // Initialize session on app load
    if (!currentSession) {
      initializeSession();
    }
  }, [currentSession]);

  return (
    <div className="layout">
      <Header />

      <div className="layout-body">
        {/* Chat Panel - Left Side */}
        <div className={`chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`}>
          <ChatPanel />
        </div>

        {/* Main Work Area - Center */}
        <div className="main-work-area">
          <MainWorkArea />
        </div>

        {/* Context Panel - Right Side */}
        <div className={`context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`}>
          <ContextPanel />
        </div>
      </div>

      {/* Global Notifications */}
      <NotificationContainer />
    </div>
  );
};

export default Layout;
