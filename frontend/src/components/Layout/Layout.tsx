import React, { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import Header from './Header';
import ChatPanel from '../Chat/ChatPanel';
import MainWorkArea from './MainWorkArea';
import ContextPanel from './ContextPanel';
import NotificationContainer from '../UI/NotificationContainer';
import { setCurrentSession } from '../../store/slices/sessionSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { setSessionId } from '../../store/slices/chatSlice';
import api from '../../services/api';
import useRealTimeSync from '../../hooks/useRealTimeSync';
import './Layout.css';

const Layout: React.FC = () => {
  const dispatch = useDispatch();
  const { sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);
  const { currentSession } = useSelector((state: RootState) => state.session);

  // Initialize real-time sync
  useRealTimeSync();

  const initializeSession = useCallback(async () => {
    try {
      const result = await api.session.create();

      if (result.data) {
        dispatch(setCurrentSession({
          session_id: result.data.session_id,
          created_at: result.data.created_at,
          last_activity: new Date().toISOString(),
          metadata: {},
        }));

        // Also set the session ID in the chat slice
        dispatch(setSessionId(result.data.session_id));
      } else {
        throw new Error(result.error || 'Failed to create session');
      }
    } catch (error) {
      console.error('Failed to initialize session:', error);
      dispatch(addNotification({
        type: 'error',
        title: 'Session Error',
        message: 'Failed to initialize session',
        duration: 5000,
      }));
    }
  }, [dispatch]);

  useEffect(() => {
    // Initialize session on app load
    if (!currentSession) {
      initializeSession();
    }
  }, [currentSession, initializeSession]);

  return (
    <div className="layout">
      <Header />

      <div className="layout-body">
        {/* Chat Panel - Left Side */}
        <div className={`chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`}>
          <ChatPanel />
        </div>

        {/* Main Work Area - Center */}
        <div className="main-work-area">
          <MainWorkArea />
        </div>

        {/* Context Panel - Right Side */}
        <div className={`context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`}>
          <ContextPanel />
        </div>
      </div>

      {/* Global Notifications */}
      <NotificationContainer />
    </div>
  );
};

export default Layout;
