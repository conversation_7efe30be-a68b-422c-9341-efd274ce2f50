import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { setCurrentView } from '../../store/slices/uiSlice';
import PaperEditor from '../Editor/PaperEditor';
import ChartViewer from '../Charts/ChartViewer';
import SearchResults from '../Search/SearchResults';
import WelcomeScreen from './WelcomeScreen';
import './MainWorkArea.css';

const MainWorkArea: React.FC = () => {
  const dispatch = useDispatch();
  const { currentView } = useSelector((state: RootState) => state.ui);
  const { currentPaper } = useSelector((state: RootState) => state.papers);
  const { charts } = useSelector((state: RootState) => state.tools);
  const { searchResults } = useSelector((state: RootState) => state.tools);

  const handleViewChange = (view: typeof currentView) => {
    dispatch(setCurrentView(view));
  };

  const renderContent = () => {
    switch (currentView) {
      case 'editor':
        return currentPaper ? (
          <PaperEditor paper={currentPaper} />
        ) : (
          <div className="no-paper-selected">
            <h3>No Paper Selected</h3>
            <p>Select a paper from the context panel or create a new one to start editing.</p>
          </div>
        );

      case 'charts':
        return charts.length > 0 ? (
          <ChartViewer charts={charts} />
        ) : (
          <div className="no-charts">
            <h3>No Charts Generated</h3>
            <p>Ask the AI to create visualizations from your research data.</p>
          </div>
        );

      case 'papers':
        return searchResults.length > 0 ? (
          <SearchResults results={searchResults} />
        ) : (
          <div className="no-search-results">
            <h3>No Search Results</h3>
            <p>Use the chat to search for research papers and view results here.</p>
          </div>
        );

      case 'chat':
      default:
        return <WelcomeScreen />;
    }
  };

  return (
    <div className="main-work-area">
      <div className="work-area-header">
        <div className="view-tabs">
          <button
            className={`tab ${currentView === 'chat' ? 'active' : ''}`}
            onClick={() => handleViewChange('chat')}
          >
            Welcome
          </button>
          <button
            className={`tab ${currentView === 'editor' ? 'active' : ''}`}
            onClick={() => handleViewChange('editor')}
          >
            Paper Editor
          </button>
          <button
            className={`tab ${currentView === 'charts' ? 'active' : ''}`}
            onClick={() => handleViewChange('charts')}
          >
            Charts ({charts.length})
          </button>
          <button
            className={`tab ${currentView === 'papers' ? 'active' : ''}`}
            onClick={() => handleViewChange('papers')}
          >
            Search Results
          </button>
        </div>
      </div>

      <div className="work-area-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default MainWorkArea;
