import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { updateSessionActivity, setConnected, setConnectionError } from '../store/slices/sessionSlice';
import { addNotification } from '../store/slices/uiSlice';

export const useRealTimeSync = () => {
  const dispatch = useDispatch();
  const { currentSession } = useSelector((state: RootState) => state.session);

  const updateActivity = useCallback(() => {
    if (currentSession) {
      dispatch(updateSessionActivity());
    }
  }, [dispatch, currentSession]);

  const checkConnection = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/health');
      if (response.ok) {
        dispatch(setConnected(true));
      } else {
        throw new Error('Health check failed');
      }
    } catch (error) {
      dispatch(setConnected(false));
      dispatch(setConnectionError('Connection lost'));
    }
  }, [dispatch]);

  // Update activity on user interactions
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateActivity]);

  // Periodic connection check
  useEffect(() => {
    checkConnection(); // Initial check
    
    const interval = setInterval(checkConnection, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [checkConnection]);

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      dispatch(setConnected(true));
      dispatch(addNotification({
        type: 'success',
        title: 'Connection Restored',
        message: 'You are back online',
        duration: 3000,
      }));
      checkConnection();
    };

    const handleOffline = () => {
      dispatch(setConnected(false));
      dispatch(setConnectionError('No internet connection'));
      dispatch(addNotification({
        type: 'warning',
        title: 'Connection Lost',
        message: 'You are offline. Changes will be saved when connection is restored.',
        duration: 5000,
      }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [dispatch, checkConnection]);

  return {
    updateActivity,
    checkConnection,
  };
};

export default useRealTimeSync;
