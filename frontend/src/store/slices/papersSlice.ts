import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Paper {
  paper_id: string;
  title: string;
  content: string;
  status: 'draft' | 'in_progress' | 'completed';
  created_at: string;
  updated_at: string;
  airtable_record_id?: string;
  metadata?: {
    word_count?: number;
    citations?: any[];
    outline?: any;
  };
}

export interface PapersState {
  papers: Paper[];
  currentPaper: Paper | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  filteredPapers: Paper[];
  cursorPosition: { line: number; column: number } | null;
}

const initialState: PapersState = {
  papers: [],
  currentPaper: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  filteredPapers: [],
  cursorPosition: null,
};

const papersSlice = createSlice({
  name: 'papers',
  initialState,
  reducers: {
    setPapers: (state, action: PayloadAction<Paper[]>) => {
      state.papers = action.payload;
      state.filteredPapers = action.payload;
    },
    addPaper: (state, action: PayloadAction<Paper>) => {
      state.papers.push(action.payload);
      state.filteredPapers = state.papers.filter(paper =>
        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())
      );
    },
    updatePaper: (state, action: PayloadAction<Paper>) => {
      const index = state.papers.findIndex(p => p.paper_id === action.payload.paper_id);
      if (index !== -1) {
        state.papers[index] = action.payload;
        if (state.currentPaper?.paper_id === action.payload.paper_id) {
          state.currentPaper = action.payload;
        }
      }
      state.filteredPapers = state.papers.filter(paper =>
        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())
      );
    },
    deletePaper: (state, action: PayloadAction<string>) => {
      state.papers = state.papers.filter(p => p.paper_id !== action.payload);
      if (state.currentPaper?.paper_id === action.payload) {
        state.currentPaper = null;
      }
      state.filteredPapers = state.papers.filter(paper =>
        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())
      );
    },
    setCurrentPaper: (state, action: PayloadAction<Paper | null>) => {
      state.currentPaper = action.payload;
    },
    updateCurrentPaperContent: (state, action: PayloadAction<string>) => {
      if (state.currentPaper) {
        state.currentPaper.content = action.payload;
        state.currentPaper.updated_at = new Date().toISOString();

        // Update in papers array too
        const index = state.papers.findIndex(p => p.paper_id === state.currentPaper!.paper_id);
        if (index !== -1) {
          state.papers[index] = { ...state.currentPaper };
        }
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.filteredPapers = state.papers.filter(paper =>
        paper.title.toLowerCase().includes(action.payload.toLowerCase()) ||
        paper.content.toLowerCase().includes(action.payload.toLowerCase())
      );
    },
    setCursorPosition: (state, action: PayloadAction<{ line: number; column: number } | null>) => {
      state.cursorPosition = action.payload;
    },
  },
});

export const {
  setPapers,
  addPaper,
  updatePaper,
  deletePaper,
  setCurrentPaper,
  updateCurrentPaperContent,
  setLoading,
  setError,
  setSearchQuery,
  setCursorPosition,
} = papersSlice.actions;

export default papersSlice.reducer;
