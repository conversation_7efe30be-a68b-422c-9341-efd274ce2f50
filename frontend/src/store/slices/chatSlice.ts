import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: {
    toolCalls?: any[];
    intermediateSteps?: any[];
  };
}

export interface ChatState {
  messages: ChatMessage[];
  isStreaming: boolean;
  currentSessionId: string | null;
  isLoading: boolean;
  error: string | null;
}

// Load session from localStorage
const loadSessionFromStorage = (): Partial<ChatState> => {
  try {
    const savedSession = localStorage.getItem('paper-agent-session');
    if (savedSession) {
      const parsed = JSON.parse(savedSession);
      return {
        currentSessionId: parsed.currentSessionId,
        messages: parsed.messages || [],
      };
    }
  } catch (error) {
    console.warn('Failed to load session from localStorage:', error);
  }
  return {};
};

// Save session to localStorage
const saveSessionToStorage = (state: ChatState) => {
  try {
    const sessionData = {
      currentSessionId: state.currentSessionId,
      messages: state.messages,
    };
    localStorage.setItem('paper-agent-session', JSON.stringify(sessionData));
  } catch (error) {
    console.warn('Failed to save session to localStorage:', error);
  }
};

const initialState: ChatState = {
  messages: [],
  isStreaming: false,
  currentSessionId: null,
  isLoading: false,
  error: null,
  ...loadSessionFromStorage(),
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages.push(action.payload);
      saveSessionToStorage(state);
    },
    updateMessage: (state, action: PayloadAction<{ id: string; content: string }>) => {
      const message = state.messages.find(m => m.id === action.payload.id);
      if (message) {
        message.content = action.payload.content;
        saveSessionToStorage(state);
      }
    },
    setStreaming: (state, action: PayloadAction<boolean>) => {
      state.isStreaming = action.payload;
    },
    setSessionId: (state, action: PayloadAction<string>) => {
      state.currentSessionId = action.payload;
      saveSessionToStorage(state);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    addIntermediateStep: (state, action: PayloadAction<{ messageId: string; step: any }>) => {
      const message = state.messages.find(m => m.id === action.payload.messageId);
      if (message) {
        if (!message.metadata) message.metadata = {};
        if (!message.metadata.intermediateSteps) message.metadata.intermediateSteps = [];
        message.metadata.intermediateSteps.push(action.payload.step);
      }
    },
  },
});

export const {
  addMessage,
  updateMessage,
  setStreaming,
  setSessionId,
  setLoading,
  setError,
  clearMessages,
  addIntermediateStep,
} = chatSlice.actions;

export default chatSlice.reducer;
