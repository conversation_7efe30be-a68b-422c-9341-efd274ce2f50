import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata?: {
    toolCalls?: any[];
    intermediateSteps?: any[];
  };
}

export interface ChatState {
  messages: ChatMessage[];
  isStreaming: boolean;
  currentSessionId: string | null;
  currentPaperId: string | null;
  paperChatHistory: Record<string, ChatMessage[]>; // paper_id -> messages
  isLoading: boolean;
  error: string | null;
}

// Load session from localStorage
const loadSessionFromStorage = (): Partial<ChatState> => {
  try {
    const savedSession = localStorage.getItem('paper-agent-session');
    if (savedSession) {
      const parsed = JSON.parse(savedSession);
      return {
        currentSessionId: parsed.currentSessionId,
        currentPaperId: parsed.currentPaperId || null,
        paperChatHistory: parsed.paperChatHistory || {},
        messages: parsed.messages || [],
      };
    }
  } catch (error) {
    console.warn('Failed to load session from localStorage:', error);
  }
  return {};
};

// Save session to localStorage
const saveSessionToStorage = (state: ChatState) => {
  try {
    const sessionData = {
      currentSessionId: state.currentSessionId,
      currentPaperId: state.currentPaperId,
      paperChatHistory: state.paperChatHistory,
      messages: state.messages,
    };
    localStorage.setItem('paper-agent-session', JSON.stringify(sessionData));
  } catch (error) {
    console.warn('Failed to save session to localStorage:', error);
  }
};

const initialState: ChatState = {
  messages: [],
  isStreaming: false,
  currentSessionId: null,
  currentPaperId: null,
  paperChatHistory: {},
  isLoading: false,
  error: null,
  ...loadSessionFromStorage(),
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<ChatMessage>) => {
      state.messages.push(action.payload);

      // Also save to paper-specific history if we have a current paper
      if (state.currentPaperId) {
        if (!state.paperChatHistory[state.currentPaperId]) {
          state.paperChatHistory[state.currentPaperId] = [];
        }
        state.paperChatHistory[state.currentPaperId].push(action.payload);
      }

      saveSessionToStorage(state);
    },
    updateMessage: (state, action: PayloadAction<{ id: string; content: string }>) => {
      const message = state.messages.find(m => m.id === action.payload.id);
      if (message) {
        message.content = action.payload.content;
        saveSessionToStorage(state);
      }
    },
    setStreaming: (state, action: PayloadAction<boolean>) => {
      state.isStreaming = action.payload;
    },
    setSessionId: (state, action: PayloadAction<string>) => {
      state.currentSessionId = action.payload;
      saveSessionToStorage(state);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];

      // Also clear paper-specific history if we have a current paper
      if (state.currentPaperId) {
        state.paperChatHistory[state.currentPaperId] = [];
      }

      saveSessionToStorage(state);
    },
    setPaperId: (state, action: PayloadAction<string | null>) => {
      const newPaperId = action.payload;

      // Save current messages to the old paper's history
      if (state.currentPaperId && state.messages.length > 0) {
        state.paperChatHistory[state.currentPaperId] = [...state.messages];
      }

      // Switch to new paper
      state.currentPaperId = newPaperId;

      // Load messages for the new paper
      if (newPaperId && state.paperChatHistory[newPaperId]) {
        state.messages = [...state.paperChatHistory[newPaperId]];
      } else {
        state.messages = [];
      }

      saveSessionToStorage(state);
    },
    loadPaperChat: (state, action: PayloadAction<{ paperId: string; messages: ChatMessage[] }>) => {
      const { paperId, messages } = action.payload;
      state.paperChatHistory[paperId] = messages;

      // If this is the current paper, also update the main messages
      if (state.currentPaperId === paperId) {
        state.messages = [...messages];
      }

      saveSessionToStorage(state);
    },
    addIntermediateStep: (state, action: PayloadAction<{ messageId: string; step: any }>) => {
      const message = state.messages.find(m => m.id === action.payload.messageId);
      if (message) {
        if (!message.metadata) message.metadata = {};
        if (!message.metadata.intermediateSteps) message.metadata.intermediateSteps = [];
        message.metadata.intermediateSteps.push(action.payload.step);
      }
    },
  },
});

export const {
  addMessage,
  updateMessage,
  setStreaming,
  setSessionId,
  setLoading,
  setError,
  clearMessages,
  setPaperId,
  loadPaperChat,
  addIntermediateStep,
} = chatSlice.actions;

export default chatSlice.reducer;
