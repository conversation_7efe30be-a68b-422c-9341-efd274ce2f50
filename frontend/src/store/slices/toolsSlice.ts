import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ToolExecution {
  id: string;
  toolName: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  startTime: string;
  endTime?: string;
  input: any;
  output?: any;
  error?: string;
}

export interface Chart {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'polar' | 'doughnut';
  title: string;
  url: string;
  data: any;
  created_at: string;
}

export interface SearchResult {
  id: string;
  query: string;
  results: any[];
  count: number;
  timestamp: string;
  source: 'firecrawl' | 'deep_research';
}

export interface ToolsState {
  executions: ToolExecution[];
  charts: Chart[];
  searchResults: SearchResult[];
  isExecuting: boolean;
}

// Load charts from localStorage
const loadChartsFromStorage = (): Chart[] => {
  try {
    const savedCharts = localStorage.getItem('paper-agent-charts');
    if (savedCharts) {
      return JSON.parse(savedCharts);
    }
  } catch (error) {
    console.warn('Failed to load charts from localStorage:', error);
  }
  return [];
};

// Save charts to localStorage
const saveChartsToStorage = (charts: Chart[]) => {
  try {
    localStorage.setItem('paper-agent-charts', JSON.stringify(charts));
  } catch (error) {
    console.warn('Failed to save charts to localStorage:', error);
  }
};

const initialState: ToolsState = {
  executions: [],
  charts: loadChartsFromStorage(),
  searchResults: [],
  isExecuting: false,
};

const toolsSlice = createSlice({
  name: 'tools',
  initialState,
  reducers: {
    startToolExecution: (state, action: PayloadAction<Omit<ToolExecution, 'id' | 'startTime' | 'status'>>) => {
      const execution: ToolExecution = {
        ...action.payload,
        id: Date.now().toString(),
        status: 'running',
        startTime: new Date().toISOString(),
      };
      state.executions.push(execution);
      state.isExecuting = true;
    },
    updateToolExecution: (state, action: PayloadAction<{ id: string; updates: Partial<ToolExecution> }>) => {
      const execution = state.executions.find(e => e.id === action.payload.id);
      if (execution) {
        Object.assign(execution, action.payload.updates);
        if (action.payload.updates.status === 'completed' || action.payload.updates.status === 'failed') {
          execution.endTime = new Date().toISOString();
        }
      }

      // Check if any tools are still running
      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');
    },
    completeToolExecution: (state, action: PayloadAction<{ id: string; output: any }>) => {
      const execution = state.executions.find(e => e.id === action.payload.id);
      if (execution) {
        execution.status = 'completed';
        execution.output = action.payload.output;
        execution.endTime = new Date().toISOString();
      }

      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');
    },
    failToolExecution: (state, action: PayloadAction<{ id: string; error: string }>) => {
      const execution = state.executions.find(e => e.id === action.payload.id);
      if (execution) {
        execution.status = 'failed';
        execution.error = action.payload.error;
        execution.endTime = new Date().toISOString();
      }

      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');
    },
    addChart: (state, action: PayloadAction<Omit<Chart, 'id' | 'created_at'>>) => {
      const chart: Chart = {
        ...action.payload,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
      };
      state.charts.push(chart);
      saveChartsToStorage(state.charts);
    },
    removeChart: (state, action: PayloadAction<string>) => {
      state.charts = state.charts.filter(c => c.id !== action.payload);
      saveChartsToStorage(state.charts);
    },
    addSearchResult: (state, action: PayloadAction<Omit<SearchResult, 'id' | 'timestamp'>>) => {
      const searchResult: SearchResult = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
      };
      state.searchResults.push(searchResult);

      // Keep only the last 10 search results
      if (state.searchResults.length > 10) {
        state.searchResults = state.searchResults.slice(-10);
      }
    },
    clearExecutions: (state) => {
      state.executions = [];
      state.isExecuting = false;
    },
  },
});

export const {
  startToolExecution,
  updateToolExecution,
  completeToolExecution,
  failToolExecution,
  addChart,
  removeChart,
  addSearchResult,
  clearExecutions,
} = toolsSlice.actions;

export default toolsSlice.reducer;
