// WebSocket service for real-time updates

export interface WebSocketMessage {
  type: string;
  paper_id?: string;
  title?: string;
  timestamp?: string;
  [key: string]: any;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private currentPaperId: string | null = null;

  connect(paperId?: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.disconnect();
    }

    this.currentPaperId = paperId || null;
    const wsUrl = paperId
      ? `ws://localhost:8000/api/v1/ws/${paperId}`
      : `ws://localhost:8000/api/v1/ws`;

    console.log('🔌 Connecting to WebSocket:', wsUrl);

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        this.reconnectAttempts = 0;
        this.emit('connected', { type: 'connected' });
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', message);

          // Handle different message types
          if (message.type === 'ping') {
            // Respond to ping with pong to keep connection alive
            this.send({ type: 'pong', timestamp: new Date().toISOString() });
          } else if (message.type === 'paper_updated') {
            console.log('📄 Paper update notification received:', message);
            // Emit paper update event for components to handle
            this.emit('paper_updated', message);
          } else {
            // Emit other message types
            this.emit(message.type, message);
          }
        } catch (error) {
          console.error('❌ Failed to parse WebSocket message:', error, event.data);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', { code: event.code, reason: event.reason });
        this.emit('disconnected', { type: 'disconnected' });

        // Only attempt reconnect if it wasn't a normal closure and we haven't exceeded max attempts
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          console.log('⚠️ Unexpected WebSocket closure, attempting reconnect...');
          this.attemptReconnect();
        } else if (event.code === 1000) {
          console.log('✅ WebSocket closed normally, no reconnection needed');
        } else {
          console.log('❌ Max reconnection attempts reached, stopping reconnection');
        }
      };

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        this.emit('error', { type: 'error', error });
      };

    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.currentPaperId = null;
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000); // Max 30 seconds
      console.log(`⏳ Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);

      setTimeout(() => {
        if (this.reconnectAttempts <= this.maxReconnectAttempts) {
          console.log(`🔄 Reconnecting attempt ${this.reconnectAttempts}...`);
          this.connect(this.currentPaperId || undefined);
        }
      }, delay);
    } else {
      console.error('❌ Max reconnection attempts reached, stopping reconnection');
    }
  }

  on(eventType: string, handler: WebSocketEventHandler) {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  off(eventType: string, handler: WebSocketEventHandler) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(eventType: string, message: WebSocketMessage) {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        console.log('📤 WebSocket message sent:', message);
      } catch (error) {
        console.error('❌ Failed to send WebSocket message:', error);
      }
    } else {
      console.warn('⚠️ WebSocket is not connected, cannot send message:', message);
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
