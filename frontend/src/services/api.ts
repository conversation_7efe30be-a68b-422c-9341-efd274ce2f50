// API service for backend communication

const API_BASE_URL = 'http://localhost:8000/api/v1';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

export interface Chart {
  id: string;
  type: 'line' | 'bar' | 'pie' | 'polar' | 'doughnut';
  title: string;
  url: string;
  data: any;
  created_at: string;
}

export interface Paper {
  paper_id: string;
  title: string;
  content: string;
  status: 'draft' | 'in_progress' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface Session {
  session_id: string;
  created_at: string;
}

// Session API
export const sessionApi = {
  async create(): Promise<ApiResponse<Session>> {
    try {
      const response = await fetch(`${API_BASE_URL}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to create session');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },
};

// Papers API
export const papersApi = {
  async list(): Promise<ApiResponse<{ papers: Paper[] }>> {
    try {
      const response = await fetch(`${API_BASE_URL}/papers`);

      if (!response.ok) {
        throw new Error('Failed to fetch papers');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async create(title: string, content: string = ''): Promise<ApiResponse<Paper>> {
    try {
      const response = await fetch(`${API_BASE_URL}/papers?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to create paper');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async update(paperId: string, title?: string, content?: string): Promise<ApiResponse<Paper>> {
    try {
      const params = new URLSearchParams();
      if (title) params.append('title', title);
      if (content) params.append('content', content);

      const response = await fetch(`${API_BASE_URL}/papers/${paperId}?${params.toString()}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to update paper');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async get(paperId: string, refresh: boolean = false): Promise<ApiResponse<Paper>> {
    try {
      const params = new URLSearchParams();
      if (refresh) params.append('refresh', 'true');

      const url = `${API_BASE_URL}/papers/${paperId}${params.toString() ? '?' + params.toString() : ''}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('Failed to fetch paper');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async updateTitle(paperId: string, title: string): Promise<ApiResponse<Paper>> {
    try {
      const response = await fetch(`${API_BASE_URL}/papers/${paperId}/title?title=${encodeURIComponent(title)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to update paper title');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },
};

// Charts API
export const chartsApi = {
  async list(): Promise<ApiResponse<{ charts: Chart[] }>> {
    try {
      const response = await fetch(`${API_BASE_URL}/charts`);

      if (!response.ok) {
        throw new Error('Failed to fetch charts');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },
};

// Chat API
export const chatApi = {
  async sendMessage(message: string, sessionId: string, paperId?: string, cursorPosition?: { line: number; column: number } | null): Promise<Response> {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        session_id: sessionId,
        paper_id: paperId,
        cursor_position: cursorPosition,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send message');
    }

    return response;
  },
};

export default {
  session: sessionApi,
  papers: papersApi,
  charts: chartsApi,
  chat: chatApi,
};
