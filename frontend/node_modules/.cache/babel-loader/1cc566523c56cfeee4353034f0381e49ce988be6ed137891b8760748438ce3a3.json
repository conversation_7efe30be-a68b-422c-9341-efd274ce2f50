{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatPanel = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    messages,\n    isStreaming,\n    isLoading,\n    currentSessionId\n  } = useSelector(state => state.chat);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper,\n    cursorPosition\n  } = useSelector(state => state.papers);\n  const [inputValue, setInputValue] = useState('');\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n    console.log('Sending message:', inputValue.trim());\n    console.log('Current session ID:', currentSessionId);\n    console.log('Current paper ID:', currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id);\n    const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    const userMessage = {\n      id: userMessageId,\n      role: 'user',\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString()\n    };\n    dispatch(addMessage(userMessage));\n    const messageContent = inputValue.trim(); // Store before clearing\n    setInputValue('');\n    dispatch(setLoading(true));\n    try {\n      var _response$body;\n      // Create assistant message placeholder\n      const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString()\n      }));\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(messageContent, currentSessionId || 'default-session', currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id);\n      console.log('Response status:', response.status, response.ok);\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = (_response$body = response.body) === null || _response$body === void 0 ? void 0 : _response$body.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n      console.log('Starting to read streaming response...');\n      if (reader) {\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) {\n            console.log('Streaming completed');\n            break;\n          }\n          const chunk = decoder.decode(value);\n          console.log('Received chunk:', chunk);\n          const lines = chunk.split('\\n');\n          let currentEvent = '';\n          for (const line of lines) {\n            if (line.startsWith('event: ')) {\n              currentEvent = line.slice(7).trim();\n              console.log('Event type:', currentEvent);\n            } else if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n                console.log('Event data:', currentEvent, data);\n                switch (currentEvent) {\n                  case 'start':\n                    console.log('Chat started');\n                    break;\n                  case 'step':\n                    if (data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.tool_name}...`,\n                        duration: 3000\n                      }));\n                    } else if (data.type === 'agent_response' && data.content) {\n                      // This is the actual response content\n                      assistantContent = data.content;\n                      console.log('Updating message with content from step:', assistantContent);\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent\n                      }));\n                    }\n                    break;\n                  case 'response':\n                    // This event contains metadata, the actual content is in the step event\n                    console.log('Response event received:', data);\n                    break;\n                  case 'complete':\n                    console.log('Chat completed');\n                    dispatch(setStreaming(false));\n                    return;\n                  case 'error':\n                    console.error('Chat error:', data);\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.error || 'An error occurred',\n                      duration: 5000\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n                console.warn('Failed to parse SSE data:', line, e);\n              }\n            }\n          }\n        }\n      }\n      dispatch(setStreaming(false));\n    } catch (error) {\n      console.error('Chat error:', error);\n      dispatch(setStreaming(false));\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Chat Error',\n        message: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000\n      }));\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const testConnection = async () => {\n    try {\n      console.log('Testing backend connection...');\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      const data = await response.json();\n      console.log('Backend health check:', data);\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Test',\n        message: `Backend is ${data.status}`,\n        duration: 3000\n      }));\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Connection Test',\n        message: 'Failed to connect to backend',\n        duration: 5000\n      }));\n    }\n  };\n  if (sidebarCollapsed) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-panel-collapsed\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapsed-icon\",\n        children: /*#__PURE__*/_jsxDEV(Send, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-panel-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testConnection,\n        style: {\n          fontSize: '12px',\n          padding: '4px 8px'\n        },\n        children: \"Test Connection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), currentPaper && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-paper-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC4 \", currentPaper.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-body\",\n      children: [/*#__PURE__*/_jsxDEV(MessageList, {\n        messages: messages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToolStatusList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-footer\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        value: inputValue,\n        onChange: setInputValue,\n        onSend: handleSendMessage,\n        onKeyPress: handleKeyPress,\n        disabled: isStreaming || isLoading,\n        placeholder: \"Ask me anything about research papers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatPanel, \"zzZiqSSneNMKn98OwTscWNYsGHY=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = ChatPanel;\nexport default ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "addMessage", "updateMessage", "setStreaming", "setLoading", "addNotification", "MessageList", "MessageInput", "ToolStatusList", "Send", "api", "jsxDEV", "_jsxDEV", "ChatPanel", "_s", "dispatch", "messages", "isStreaming", "isLoading", "currentSessionId", "state", "chat", "sidebarCollapsed", "ui", "currentPaper", "cursorPosition", "papers", "inputValue", "setInputValue", "handleSendMessage", "trim", "console", "log", "paper_id", "userMessageId", "Date", "now", "Math", "random", "toString", "substr", "userMessage", "id", "role", "content", "timestamp", "toISOString", "messageContent", "_response$body", "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "sendMessage", "status", "ok", "Error", "reader", "body", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "chunk", "decode", "lines", "split", "currentEvent", "line", "startsWith", "slice", "data", "JSON", "parse", "type", "title", "message", "tool_name", "duration", "error", "e", "warn", "handleKeyPress", "key", "shift<PERSON>ey", "preventDefault", "testConnection", "fetch", "json", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "fontSize", "padding", "onChange", "onSend", "onKeyPress", "disabled", "placeholder", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\n\nconst ChatPanel: React.FC = () => {\n  const dispatch = useDispatch();\n  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentPaper, cursorPosition } = useSelector((state: RootState) => state.papers);\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n\n    console.log('Sending message:', inputValue.trim());\n    console.log('Current session ID:', currentSessionId);\n    console.log('Current paper ID:', currentPaper?.paper_id);\n\n    const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    const userMessage = {\n      id: userMessageId,\n      role: 'user' as const,\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString(),\n    };\n\n    dispatch(addMessage(userMessage));\n    const messageContent = inputValue.trim(); // Store before clearing\n    setInputValue('');\n    dispatch(setLoading(true));\n\n    try {\n      // Create assistant message placeholder\n      const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString(),\n      }));\n\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(\n        messageContent,\n        currentSessionId || 'default-session',\n        currentPaper?.paper_id\n      );\n\n      console.log('Response status:', response.status, response.ok);\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = response.body?.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n\n      console.log('Starting to read streaming response...');\n\n      if (reader) {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) {\n            console.log('Streaming completed');\n            break;\n          }\n\n          const chunk = decoder.decode(value);\n          console.log('Received chunk:', chunk);\n          const lines = chunk.split('\\n');\n\n          let currentEvent = '';\n\n          for (const line of lines) {\n            if (line.startsWith('event: ')) {\n              currentEvent = line.slice(7).trim();\n              console.log('Event type:', currentEvent);\n            } else if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n                console.log('Event data:', currentEvent, data);\n\n                switch (currentEvent) {\n                  case 'start':\n                    console.log('Chat started');\n                    break;\n\n                  case 'step':\n                    if (data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.tool_name}...`,\n                        duration: 3000,\n                      }));\n                    } else if (data.type === 'agent_response' && data.content) {\n                      // This is the actual response content\n                      assistantContent = data.content;\n                      console.log('Updating message with content from step:', assistantContent);\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent,\n                      }));\n                    }\n                    break;\n\n                  case 'response':\n                    // This event contains metadata, the actual content is in the step event\n                    console.log('Response event received:', data);\n                    break;\n\n                  case 'complete':\n                    console.log('Chat completed');\n                    dispatch(setStreaming(false));\n                    return;\n\n                  case 'error':\n                    console.error('Chat error:', data);\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.error || 'An error occurred',\n                      duration: 5000,\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n                console.warn('Failed to parse SSE data:', line, e);\n              }\n            }\n          }\n        }\n      }\n\n      dispatch(setStreaming(false));\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      dispatch(setStreaming(false));\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Chat Error',\n        message: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const testConnection = async () => {\n    try {\n      console.log('Testing backend connection...');\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      const data = await response.json();\n      console.log('Backend health check:', data);\n\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Test',\n        message: `Backend is ${data.status}`,\n        duration: 3000,\n      }));\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Connection Test',\n        message: 'Failed to connect to backend',\n        duration: 5000,\n      }));\n    }\n  };\n\n\n\n  if (sidebarCollapsed) {\n    return (\n      <div className=\"chat-panel-collapsed\">\n        <div className=\"collapsed-icon\">\n          <Send size={20} />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"chat-panel-content\">\n      <div className=\"chat-header\">\n        <h2>Chat</h2>\n        <button onClick={testConnection} style={{ fontSize: '12px', padding: '4px 8px' }}>\n          Test Connection\n        </button>\n        {currentPaper && (\n          <div className=\"current-paper-indicator\">\n            <span>📄 {currentPaper.title}</span>\n          </div>\n        )}\n      </div>\n\n      <div className=\"chat-body\">\n        <MessageList messages={messages} />\n        <ToolStatusList />\n      </div>\n\n      <div className=\"chat-footer\">\n        <MessageInput\n          value={inputValue}\n          onChange={setInputValue}\n          onSend={handleSendMessage}\n          onKeyPress={handleKeyPress}\n          disabled={isStreaming || isLoading}\n          placeholder=\"Ask me anything about research papers...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ChatPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,UAAU,EAAEC,aAAa,EAAEC,YAAY,EAAEC,UAAU,QAAQ,8BAA8B;AAClG,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,QAAQ;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAiB,CAAC,GAAGnB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC5G,MAAM;IAAEC;EAAiB,CAAC,GAAGtB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EACxE,MAAM;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGzB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACM,MAAM,CAAC;EACxF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM+B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,CAAC,IAAIb,WAAW,IAAIC,SAAS,EAAE;IAEpDa,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEL,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,gBAAgB,CAAC;IACpDY,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAER,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAAQ,CAAC;IAExD,MAAMC,aAAa,GAAG,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACrF,MAAMC,WAAW,GAAG;MAClBC,EAAE,EAAER,aAAa;MACjBS,IAAI,EAAE,MAAe;MACrBC,OAAO,EAAEjB,UAAU,CAACG,IAAI,CAAC,CAAC;MAC1Be,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;IACpC,CAAC;IAED/B,QAAQ,CAACd,UAAU,CAACwC,WAAW,CAAC,CAAC;IACjC,MAAMM,cAAc,GAAGpB,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1CF,aAAa,CAAC,EAAE,CAAC;IACjBb,QAAQ,CAACX,UAAU,CAAC,IAAI,CAAC,CAAC;IAE1B,IAAI;MAAA,IAAA4C,cAAA;MACF;MACA,MAAMC,kBAAkB,GAAG,aAAad,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/FzB,QAAQ,CAACd,UAAU,CAAC;QAClByC,EAAE,EAAEO,kBAAkB;QACtBN,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC;MAEH/B,QAAQ,CAACX,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BW,QAAQ,CAACZ,YAAY,CAAC,IAAI,CAAC,CAAC;;MAE5B;MACA,MAAM+C,QAAQ,GAAG,MAAMxC,GAAG,CAACW,IAAI,CAAC8B,WAAW,CACzCJ,cAAc,EACd5B,gBAAgB,IAAI,iBAAiB,EACrCK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAChB,CAAC;MAEDF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkB,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,EAAE,CAAC;MAE7D,IAAI,CAACH,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;;MAEA;MACA,MAAMC,MAAM,IAAAP,cAAA,GAAGE,QAAQ,CAACM,IAAI,cAAAR,cAAA,uBAAbA,cAAA,CAAeS,SAAS,CAAC,CAAC;MACzC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MACjC,IAAIC,gBAAgB,GAAG,EAAE;MAEzB7B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAIuB,MAAM,EAAE;QACV,OAAO,IAAI,EAAE;UACX,MAAM;YAAEM,IAAI;YAAEC;UAAM,CAAC,GAAG,MAAMP,MAAM,CAACQ,IAAI,CAAC,CAAC;UAC3C,IAAIF,IAAI,EAAE;YACR9B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;YAClC;UACF;UAEA,MAAMgC,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACH,KAAK,CAAC;UACnC/B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgC,KAAK,CAAC;UACrC,MAAME,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;UAE/B,IAAIC,YAAY,GAAG,EAAE;UAErB,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;YACxB,IAAIG,IAAI,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE;cAC9BF,YAAY,GAAGC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAACzC,IAAI,CAAC,CAAC;cACnCC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoC,YAAY,CAAC;YAC1C,CAAC,MAAM,IAAIC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;cACpC,IAAI;gBACF,MAAME,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtCxC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoC,YAAY,EAAEI,IAAI,CAAC;gBAE9C,QAAQJ,YAAY;kBAClB,KAAK,OAAO;oBACVrC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;oBAC3B;kBAEF,KAAK,MAAM;oBACT,IAAIwC,IAAI,CAACG,IAAI,KAAK,WAAW,EAAE;sBAC7B5D,QAAQ,CAACV,eAAe,CAAC;wBACvBsE,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,gBAAgB;wBACvBC,OAAO,EAAE,aAAaL,IAAI,CAACM,SAAS,KAAK;wBACzCC,QAAQ,EAAE;sBACZ,CAAC,CAAC,CAAC;oBACL,CAAC,MAAM,IAAIP,IAAI,CAACG,IAAI,KAAK,gBAAgB,IAAIH,IAAI,CAAC5B,OAAO,EAAE;sBACzD;sBACAgB,gBAAgB,GAAGY,IAAI,CAAC5B,OAAO;sBAC/Bb,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE4B,gBAAgB,CAAC;sBACzE7C,QAAQ,CAACb,aAAa,CAAC;wBACrBwC,EAAE,EAAEO,kBAAkB;wBACtBL,OAAO,EAAEgB;sBACX,CAAC,CAAC,CAAC;oBACL;oBACA;kBAEF,KAAK,UAAU;oBACb;oBACA7B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwC,IAAI,CAAC;oBAC7C;kBAEF,KAAK,UAAU;oBACbzC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;oBAC7BjB,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7B;kBAEF,KAAK,OAAO;oBACV4B,OAAO,CAACiD,KAAK,CAAC,aAAa,EAAER,IAAI,CAAC;oBAClCzD,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7BY,QAAQ,CAACV,eAAe,CAAC;sBACvBsE,IAAI,EAAE,OAAO;sBACbC,KAAK,EAAE,YAAY;sBACnBC,OAAO,EAAEL,IAAI,CAACQ,KAAK,IAAI,mBAAmB;sBAC1CD,QAAQ,EAAE;oBACZ,CAAC,CAAC,CAAC;oBACH;gBACJ;cACF,CAAC,CAAC,OAAOE,CAAC,EAAE;gBACV;gBACAlD,OAAO,CAACmD,IAAI,CAAC,2BAA2B,EAAEb,IAAI,EAAEY,CAAC,CAAC;cACpD;YACF;UACF;QACF;MACF;MAEAlE,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;IAE/B,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCjE,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;MAC7BY,QAAQ,CAACX,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BW,QAAQ,CAACV,eAAe,CAAC;QACvBsE,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE,2BAA2BG,KAAK,YAAY1B,KAAK,GAAG0B,KAAK,CAACH,OAAO,GAAG,eAAe,EAAE;QAC9FE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,cAAc,GAAIF,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI,CAACH,CAAC,CAACI,QAAQ,EAAE;MACpCJ,CAAC,CAACK,cAAc,CAAC,CAAC;MAClBzD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM0D,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFxD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMkB,QAAQ,GAAG,MAAMsC,KAAK,CAAC,qCAAqC,CAAC;MACnE,MAAMhB,IAAI,GAAG,MAAMtB,QAAQ,CAACuC,IAAI,CAAC,CAAC;MAClC1D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwC,IAAI,CAAC;MAE1CzD,QAAQ,CAACV,eAAe,CAAC;QACvBsE,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,cAAcL,IAAI,CAACpB,MAAM,EAAE;QACpC2B,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjE,QAAQ,CAACV,eAAe,CAAC;QACvBsE,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,8BAA8B;QACvCE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAID,IAAIzD,gBAAgB,EAAE;IACpB,oBACEV,OAAA;MAAK8E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC/E,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B/E,OAAA,CAACH,IAAI;UAACmF,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpF,OAAA;IAAK8E,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC/E,OAAA;MAAK8E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/E,OAAA;QAAA+E,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACbpF,OAAA;QAAQqF,OAAO,EAAEV,cAAe;QAACW,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAU,CAAE;QAAAT,QAAA,EAAC;MAElF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRxE,YAAY,iBACXZ,OAAA;QAAK8E,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtC/E,OAAA;UAAA+E,QAAA,GAAM,eAAG,EAACnE,YAAY,CAACoD,KAAK;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENpF,OAAA;MAAK8E,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB/E,OAAA,CAACN,WAAW;QAACU,QAAQ,EAAEA;MAAS;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCpF,OAAA,CAACJ,cAAc;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENpF,OAAA;MAAK8E,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B/E,OAAA,CAACL,YAAY;QACXuD,KAAK,EAAEnC,UAAW;QAClB0E,QAAQ,EAAEzE,aAAc;QACxB0E,MAAM,EAAEzE,iBAAkB;QAC1B0E,UAAU,EAAEpB,cAAe;QAC3BqB,QAAQ,EAAEvF,WAAW,IAAIC,SAAU;QACnCuF,WAAW,EAAC;MAA0C;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClF,EAAA,CAlOID,SAAmB;EAAA,QACNd,WAAW,EACmCC,WAAW,EAC7CA,WAAW,EACCA,WAAW;AAAA;AAAA0G,EAAA,GAJhD7F,SAAmB;AAoOzB,eAAeA,SAAS;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}