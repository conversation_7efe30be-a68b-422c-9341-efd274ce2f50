{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n// Load session from localStorage\nconst loadSessionFromStorage = () => {\n  try {\n    const savedSession = localStorage.getItem('paper-agent-session');\n    if (savedSession) {\n      const parsed = JSON.parse(savedSession);\n      return {\n        currentSessionId: parsed.currentSessionId,\n        messages: parsed.messages || []\n      };\n    }\n  } catch (error) {\n    console.warn('Failed to load session from localStorage:', error);\n  }\n  return {};\n};\nconst initialState = {\n  messages: [],\n  isStreaming: false,\n  currentSessionId: null,\n  isLoading: false,\n  error: null,\n  ...loadSessionFromStorage()\n};\nconst chatSlice = createSlice({\n  name: 'chat',\n  initialState,\n  reducers: {\n    addMessage: (state, action) => {\n      state.messages.push(action.payload);\n    },\n    updateMessage: (state, action) => {\n      const message = state.messages.find(m => m.id === action.payload.id);\n      if (message) {\n        message.content = action.payload.content;\n      }\n    },\n    setStreaming: (state, action) => {\n      state.isStreaming = action.payload;\n    },\n    setSessionId: (state, action) => {\n      state.currentSessionId = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearMessages: state => {\n      state.messages = [];\n    },\n    addIntermediateStep: (state, action) => {\n      const message = state.messages.find(m => m.id === action.payload.messageId);\n      if (message) {\n        if (!message.metadata) message.metadata = {};\n        if (!message.metadata.intermediateSteps) message.metadata.intermediateSteps = [];\n        message.metadata.intermediateSteps.push(action.payload.step);\n      }\n    }\n  }\n});\nexport const {\n  addMessage,\n  updateMessage,\n  setStreaming,\n  setSessionId,\n  setLoading,\n  setError,\n  clearMessages,\n  addIntermediateStep\n} = chatSlice.actions;\nexport default chatSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "loadSessionFromStorage", "savedSession", "localStorage", "getItem", "parsed", "JSON", "parse", "currentSessionId", "messages", "error", "console", "warn", "initialState", "isStreaming", "isLoading", "chatSlice", "name", "reducers", "addMessage", "state", "action", "push", "payload", "updateMessage", "message", "find", "m", "id", "content", "setStreaming", "setSessionId", "setLoading", "setError", "clearMessages", "addIntermediateStep", "messageId", "metadata", "intermediateSteps", "step", "actions", "reducer"], "sources": ["/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: string;\n  metadata?: {\n    toolCalls?: any[];\n    intermediateSteps?: any[];\n  };\n}\n\nexport interface ChatState {\n  messages: ChatMessage[];\n  isStreaming: boolean;\n  currentSessionId: string | null;\n  isLoading: boolean;\n  error: string | null;\n}\n\n// Load session from localStorage\nconst loadSessionFromStorage = (): Partial<ChatState> => {\n  try {\n    const savedSession = localStorage.getItem('paper-agent-session');\n    if (savedSession) {\n      const parsed = JSON.parse(savedSession);\n      return {\n        currentSessionId: parsed.currentSessionId,\n        messages: parsed.messages || [],\n      };\n    }\n  } catch (error) {\n    console.warn('Failed to load session from localStorage:', error);\n  }\n  return {};\n};\n\nconst initialState: ChatState = {\n  messages: [],\n  isStreaming: false,\n  currentSessionId: null,\n  isLoading: false,\n  error: null,\n  ...loadSessionFromStorage(),\n};\n\nconst chatSlice = createSlice({\n  name: 'chat',\n  initialState,\n  reducers: {\n    addMessage: (state, action: PayloadAction<ChatMessage>) => {\n      state.messages.push(action.payload);\n    },\n    updateMessage: (state, action: PayloadAction<{ id: string; content: string }>) => {\n      const message = state.messages.find(m => m.id === action.payload.id);\n      if (message) {\n        message.content = action.payload.content;\n      }\n    },\n    setStreaming: (state, action: PayloadAction<boolean>) => {\n      state.isStreaming = action.payload;\n    },\n    setSessionId: (state, action: PayloadAction<string>) => {\n      state.currentSessionId = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearMessages: (state) => {\n      state.messages = [];\n    },\n    addIntermediateStep: (state, action: PayloadAction<{ messageId: string; step: any }>) => {\n      const message = state.messages.find(m => m.id === action.payload.messageId);\n      if (message) {\n        if (!message.metadata) message.metadata = {};\n        if (!message.metadata.intermediateSteps) message.metadata.intermediateSteps = [];\n        message.metadata.intermediateSteps.push(action.payload.step);\n      }\n    },\n  },\n});\n\nexport const {\n  addMessage,\n  updateMessage,\n  setStreaming,\n  setSessionId,\n  setLoading,\n  setError,\n  clearMessages,\n  addIntermediateStep,\n} = chatSlice.actions;\n\nexport default chatSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAqB7D;AACA,MAAMC,sBAAsB,GAAGA,CAAA,KAA0B;EACvD,IAAI;IACF,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;IAChE,IAAIF,YAAY,EAAE;MAChB,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC;MACvC,OAAO;QACLM,gBAAgB,EAAEH,MAAM,CAACG,gBAAgB;QACzCC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ,IAAI;MAC/B,CAAC;IACH;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEF,KAAK,CAAC;EAClE;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AAED,MAAMG,YAAuB,GAAG;EAC9BJ,QAAQ,EAAE,EAAE;EACZK,WAAW,EAAE,KAAK;EAClBN,gBAAgB,EAAE,IAAI;EACtBO,SAAS,EAAE,KAAK;EAChBL,KAAK,EAAE,IAAI;EACX,GAAGT,sBAAsB,CAAC;AAC5B,CAAC;AAED,MAAMe,SAAS,GAAGhB,WAAW,CAAC;EAC5BiB,IAAI,EAAE,MAAM;EACZJ,YAAY;EACZK,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAkC,KAAK;MACzDD,KAAK,CAACX,QAAQ,CAACa,IAAI,CAACD,MAAM,CAACE,OAAO,CAAC;IACrC,CAAC;IACDC,aAAa,EAAEA,CAACJ,KAAK,EAAEC,MAAsD,KAAK;MAChF,MAAMI,OAAO,GAAGL,KAAK,CAACX,QAAQ,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,MAAM,CAACE,OAAO,CAACK,EAAE,CAAC;MACpE,IAAIH,OAAO,EAAE;QACXA,OAAO,CAACI,OAAO,GAAGR,MAAM,CAACE,OAAO,CAACM,OAAO;MAC1C;IACF,CAAC;IACDC,YAAY,EAAEA,CAACV,KAAK,EAAEC,MAA8B,KAAK;MACvDD,KAAK,CAACN,WAAW,GAAGO,MAAM,CAACE,OAAO;IACpC,CAAC;IACDQ,YAAY,EAAEA,CAACX,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAACZ,gBAAgB,GAAGa,MAAM,CAACE,OAAO;IACzC,CAAC;IACDS,UAAU,EAAEA,CAACZ,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACL,SAAS,GAAGM,MAAM,CAACE,OAAO;IAClC,CAAC;IACDU,QAAQ,EAAEA,CAACb,KAAK,EAAEC,MAAoC,KAAK;MACzDD,KAAK,CAACV,KAAK,GAAGW,MAAM,CAACE,OAAO;IAC9B,CAAC;IACDW,aAAa,EAAGd,KAAK,IAAK;MACxBA,KAAK,CAACX,QAAQ,GAAG,EAAE;IACrB,CAAC;IACD0B,mBAAmB,EAAEA,CAACf,KAAK,EAAEC,MAAuD,KAAK;MACvF,MAAMI,OAAO,GAAGL,KAAK,CAACX,QAAQ,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,MAAM,CAACE,OAAO,CAACa,SAAS,CAAC;MAC3E,IAAIX,OAAO,EAAE;QACX,IAAI,CAACA,OAAO,CAACY,QAAQ,EAAEZ,OAAO,CAACY,QAAQ,GAAG,CAAC,CAAC;QAC5C,IAAI,CAACZ,OAAO,CAACY,QAAQ,CAACC,iBAAiB,EAAEb,OAAO,CAACY,QAAQ,CAACC,iBAAiB,GAAG,EAAE;QAChFb,OAAO,CAACY,QAAQ,CAACC,iBAAiB,CAAChB,IAAI,CAACD,MAAM,CAACE,OAAO,CAACgB,IAAI,CAAC;MAC9D;IACF;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXpB,UAAU;EACVK,aAAa;EACbM,YAAY;EACZC,YAAY;EACZC,UAAU;EACVC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,GAAGnB,SAAS,CAACwB,OAAO;AAErB,eAAexB,SAAS,CAACyB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}