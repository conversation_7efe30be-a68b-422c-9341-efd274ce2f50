{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport api from '../../services/api';\nimport './PaperEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaperEditor = ({\n  paper\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n  const handleContentChange = useCallback(value => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n  const handleTextareaChange = e => {\n    handleContentChange(e.target.value);\n  };\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleExport = () => {\n    const blob = new Blob([content], {\n      type: 'text/markdown'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n  const renderPreview = () => {\n    const html = marked.parse(content, {\n      async: false\n    });\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: sanitizedHtml\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 12\n    }, this);\n  };\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paper-editor\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: paper.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"word-count\",\n            children: [getWordCount(), \" words\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-indicator\",\n            children: hasUnsavedChanges ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unsaved\",\n              children: \"\\u25CF Unsaved changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"saved\",\n              children: \"\\u2713 Saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsPreviewMode(!isPreviewMode),\n          className: \"editor-button\",\n          title: isPreviewMode ? 'Edit mode' : 'Preview mode',\n          children: [isPreviewMode ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 30\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 53\n          }, this), isPreviewMode ? 'Edit' : 'Preview']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: !hasUnsavedChanges || isSaving,\n          className: \"editor-button save-button\",\n          title: \"Save changes\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), isSaving ? 'Saving...' : 'Save']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          className: \"editor-button\",\n          title: \"Export as Markdown\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: isPreviewMode ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-pane\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-content\",\n          children: renderPreview()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"edit-pane\",\n        children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: content,\n          onChange: handleContentChange,\n          className: \"editor-textarea\",\n          placeholder: \"Start writing your paper here...\",\n          spellCheck: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Characters: \", content.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Words: \", getWordCount()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Lines: \", content.split('\\n').length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-help\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Markdown supported \\u2022 Ctrl+S to save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperEditor, \"WAkBjZO5V4jpS43YXiev0kO/Y1E=\", false, function () {\n  return [useDispatch];\n});\n_c = PaperEditor;\nexport default PaperEditor;\nvar _c;\n$RefreshReg$(_c, \"PaperEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "updateCurrentPaperContent", "addNotification", "Save", "Eye", "Eye<PERSON>ff", "Download", "marked", "DOMPurify", "api", "jsxDEV", "_jsxDEV", "PaperEditor", "paper", "_s", "dispatch", "content", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewMode", "setIsPreviewMode", "isSplitView", "setIsSplitView", "isSaving", "setIsSaving", "hasUnsavedChanges", "setHasUnsavedChanges", "isFullscreen", "setIsFullscreen", "paper_id", "handleContentChange", "value", "newContent", "handleTextareaChange", "e", "target", "handleSave", "result", "papers", "update", "title", "data", "type", "message", "duration", "Error", "error", "handleExport", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "toLowerCase", "click", "revokeObjectURL", "renderPreview", "html", "parse", "async", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWordCount", "trim", "split", "filter", "word", "length", "to<PERSON><PERSON><PERSON><PERSON>", "toggleFullscreen", "timer", "setTimeout", "clearTimeout", "handleKeyDown", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "addEventListener", "removeEventListener", "className", "children", "onClick", "size", "disabled", "onChange", "placeholder", "spell<PERSON>heck", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Paper, updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\n\ninterface PaperEditorProps {\n  paper: Paper;\n}\n\nconst PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  const handleContentChange = useCallback((value: string | undefined) => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n\n  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    handleContentChange(e.target.value);\n  };\n\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const renderPreview = () => {\n    const html = marked.parse(content, { async: false }) as string;\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n  };\n\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, []);\n\n  return (\n    <div className=\"paper-editor\">\n      <div className=\"editor-header\">\n        <div className=\"editor-title\">\n          <h2>{paper.title}</h2>\n          <div className=\"editor-meta\">\n            <span className=\"word-count\">{getWordCount()} words</span>\n            <span className=\"status-indicator\">\n              {hasUnsavedChanges ? (\n                <span className=\"unsaved\">● Unsaved changes</span>\n              ) : (\n                <span className=\"saved\">✓ Saved</span>\n              )}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"editor-actions\">\n          <button\n            onClick={() => setIsPreviewMode(!isPreviewMode)}\n            className=\"editor-button\"\n            title={isPreviewMode ? 'Edit mode' : 'Preview mode'}\n          >\n            {isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}\n            {isPreviewMode ? 'Edit' : 'Preview'}\n          </button>\n\n          <button\n            onClick={handleSave}\n            disabled={!hasUnsavedChanges || isSaving}\n            className=\"editor-button save-button\"\n            title=\"Save changes\"\n          >\n            <Save size={16} />\n            {isSaving ? 'Saving...' : 'Save'}\n          </button>\n\n          <button\n            onClick={handleExport}\n            className=\"editor-button\"\n            title=\"Export as Markdown\"\n          >\n            <Download size={16} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      <div className=\"editor-content\">\n        {isPreviewMode ? (\n          <div className=\"preview-pane\">\n            <div className=\"markdown-content\">\n              {renderPreview()}\n            </div>\n          </div>\n        ) : (\n          <div className=\"edit-pane\">\n            <textarea\n              value={content}\n              onChange={handleContentChange}\n              className=\"editor-textarea\"\n              placeholder=\"Start writing your paper here...\"\n              spellCheck={true}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"editor-footer\">\n        <div className=\"editor-stats\">\n          <span>Characters: {content.length}</span>\n          <span>Words: {getWordCount()}</span>\n          <span>Lines: {content.split('\\n').length}</span>\n        </div>\n\n        <div className=\"editor-help\">\n          <span>Markdown supported • Ctrl+S to save</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaperEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAAgBC,yBAAyB,QAAQ,gCAAgC;AACjF,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAA0B,cAAc;AAC5E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AAEjC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAACgB,KAAK,CAACG,OAAO,CAAC;EACrD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdmB,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC;IACzBS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACZ,KAAK,CAACe,QAAQ,EAAEf,KAAK,CAACG,OAAO,CAAC,CAAC;EAEnClB,SAAS,CAAC,MAAM;IACd2B,oBAAoB,CAACT,OAAO,KAAKH,KAAK,CAACG,OAAO,CAAC;EACjD,CAAC,EAAE,CAACA,OAAO,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC;EAE5B,MAAMa,mBAAmB,GAAG9B,WAAW,CAAE+B,KAAyB,IAAK;IACrE,MAAMC,UAAU,GAAGD,KAAK,IAAI,EAAE;IAC9Bb,UAAU,CAACc,UAAU,CAAC;IACtBhB,QAAQ,CAACd,yBAAyB,CAAC8B,UAAU,CAAC,CAAC;EACjD,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMiB,oBAAoB,GAAIC,CAAyC,IAAK;IAC1EJ,mBAAmB,CAACI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EACrC,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACX,iBAAiB,EAAE;IAExBD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMa,MAAM,GAAG,MAAM3B,GAAG,CAAC4B,MAAM,CAACC,MAAM,CAACzB,KAAK,CAACe,QAAQ,EAAEf,KAAK,CAAC0B,KAAK,EAAEvB,OAAO,CAAC;MAE5E,IAAIoB,MAAM,CAACI,IAAI,EAAE;QACff,oBAAoB,CAAC,KAAK,CAAC;QAC3BV,QAAQ,CAACb,eAAe,CAAC;UACvBuC,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,aAAa;UACpBG,OAAO,EAAE,4CAA4C;UACrDC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,MAAM,CAACS,KAAK,IAAI,gBAAgB,CAAC;MACnD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd9B,QAAQ,CAACb,eAAe,CAAC;QACvBuC,IAAI,EAAE,OAAO;QACbF,KAAK,EAAE,aAAa;QACpBG,OAAO,EAAE,gDAAgD;QACzDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRpB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAChC,OAAO,CAAC,EAAE;MAAEyB,IAAI,EAAE;IAAgB,CAAC,CAAC;IAC3D,MAAMQ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAG3C,KAAK,CAAC0B,KAAK,CAACkB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK;IAC7EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZT,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGvD,MAAM,CAACwD,KAAK,CAAC/C,OAAO,EAAE;MAAEgD,KAAK,EAAE;IAAM,CAAC,CAAW;IAC9D,MAAMC,aAAa,GAAGzD,SAAS,CAAC0D,QAAQ,CAACJ,IAAI,CAAC;IAC9C,oBAAOnD,OAAA;MAAKwD,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAc;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOzD,OAAO,CAAC0D,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC3E,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI3D,WAAW,EAAE;MACfD,gBAAgB,CAAC,CAACD,aAAa,CAAC;MAChCG,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACLA,cAAc,CAAC,IAAI,CAAC;MACpBF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM6D,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrD,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd,IAAI0B,iBAAiB,EAAE;MACrB,MAAMyD,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B/C,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMgD,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACjE,OAAO,EAAEQ,iBAAiB,CAAC,CAAC;;EAEhC;EACA1B,SAAS,CAAC,MAAM;IACd,MAAMsF,aAAa,GAAInD,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACoD,OAAO,IAAIpD,CAAC,CAACqD,OAAO,KAAKrD,CAAC,CAACsD,GAAG,KAAK,GAAG,EAAE;QAC7CtD,CAAC,CAACuD,cAAc,CAAC,CAAC;QAClBrD,UAAU,CAAC,CAAC;MACd;MACA,IAAI,CAACF,CAAC,CAACoD,OAAO,IAAIpD,CAAC,CAACqD,OAAO,KAAKrD,CAAC,CAACsD,GAAG,KAAK,GAAG,EAAE;QAC7CtD,CAAC,CAACuD,cAAc,CAAC,CAAC;QAClBT,UAAU,CAAC,CAAC;MACd;IACF,CAAC;IAED1B,QAAQ,CAACoC,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAM/B,QAAQ,CAACqC,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzE,OAAA;IAAKgF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BjF,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BjF,OAAA;QAAKgF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjF,OAAA;UAAAiF,QAAA,EAAK/E,KAAK,CAAC0B;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtB7D,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAMgF,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEnB,YAAY,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1D7D,OAAA;YAAMgF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/BpE,iBAAiB,gBAChBb,OAAA;cAAMgF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAElD7D,OAAA;cAAMgF,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACtC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7D,OAAA;QAAKgF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BjF,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAM1E,gBAAgB,CAAC,CAACD,aAAa,CAAE;UAChDyE,SAAS,EAAC,eAAe;UACzBpD,KAAK,EAAErB,aAAa,GAAG,WAAW,GAAG,cAAe;UAAA0E,QAAA,GAEnD1E,aAAa,gBAAGP,OAAA,CAACN,MAAM;YAACyF,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACP,GAAG;YAAC0F,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxDtD,aAAa,GAAG,MAAM,GAAG,SAAS;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAET7D,OAAA;UACEkF,OAAO,EAAE1D,UAAW;UACpB4D,QAAQ,EAAE,CAACvE,iBAAiB,IAAIF,QAAS;UACzCqE,SAAS,EAAC,2BAA2B;UACrCpD,KAAK,EAAC,cAAc;UAAAqD,QAAA,gBAEpBjF,OAAA,CAACR,IAAI;YAAC2F,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjBlD,QAAQ,GAAG,WAAW,GAAG,MAAM;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAET7D,OAAA;UACEkF,OAAO,EAAE/C,YAAa;UACtB6C,SAAS,EAAC,eAAe;UACzBpD,KAAK,EAAC,oBAAoB;UAAAqD,QAAA,gBAE1BjF,OAAA,CAACL,QAAQ;YAACwF,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7D,OAAA;MAAKgF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5B1E,aAAa,gBACZP,OAAA;QAAKgF,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BjF,OAAA;UAAKgF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9B/B,aAAa,CAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN7D,OAAA;QAAKgF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBjF,OAAA;UACEmB,KAAK,EAAEd,OAAQ;UACfgF,QAAQ,EAAEnE,mBAAoB;UAC9B8D,SAAS,EAAC,iBAAiB;UAC3BM,WAAW,EAAC,kCAAkC;UAC9CC,UAAU,EAAE;QAAK;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN7D,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BjF,OAAA;QAAKgF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjF,OAAA;UAAAiF,QAAA,GAAM,cAAY,EAAC5E,OAAO,CAAC8D,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzC7D,OAAA;UAAAiF,QAAA,GAAM,SAAO,EAACnB,YAAY,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpC7D,OAAA;UAAAiF,QAAA,GAAM,SAAO,EAAC5E,OAAO,CAAC2D,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEN7D,OAAA;QAAKgF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BjF,OAAA;UAAAiF,QAAA,EAAM;QAAmC;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAzMIF,WAAuC;EAAA,QAC1BZ,WAAW;AAAA;AAAAmG,EAAA,GADxBvF,WAAuC;AA2M7C,eAAeA,WAAW;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}