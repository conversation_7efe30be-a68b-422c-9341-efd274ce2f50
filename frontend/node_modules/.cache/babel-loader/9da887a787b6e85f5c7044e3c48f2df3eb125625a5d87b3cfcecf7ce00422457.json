{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"kqv944\"\n}], [\"path\", {\n  d: \"m7.9 7.9 2.7 2.7\",\n  key: \"hpeyl3\"\n}], [\"circle\", {\n  cx: \"16.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"w0ekpg\"\n}], [\"path\", {\n  d: \"m13.4 10.6 2.7-2.7\",\n  key: \"264c1n\"\n}], [\"circle\", {\n  cx: \"7.5\",\n  cy: \"16.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"nkw3mc\"\n}], [\"path\", {\n  d: \"m7.9 16.1 2.7-2.7\",\n  key: \"p81g5e\"\n}], [\"circle\", {\n  cx: \"16.5\",\n  cy: \"16.5\",\n  r: \".5\",\n  fill: \"currentColor\",\n  key: \"fubopw\"\n}], [\"path\", {\n  d: \"m13.4 13.4 2.7 2.7\",\n  key: \"abhel3\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}]];\nconst Vault = createLucideIcon(\"vault\", __iconNode);\nexport { __iconNode, Vault as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "cx", "cy", "r", "fill", "d", "<PERSON><PERSON>", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/vault.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['circle', { cx: '7.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'kqv944' }],\n  ['path', { d: 'm7.9 7.9 2.7 2.7', key: 'hpeyl3' }],\n  ['circle', { cx: '16.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'w0ekpg' }],\n  ['path', { d: 'm13.4 10.6 2.7-2.7', key: '264c1n' }],\n  ['circle', { cx: '7.5', cy: '16.5', r: '.5', fill: 'currentColor', key: 'nkw3mc' }],\n  ['path', { d: 'm7.9 16.1 2.7-2.7', key: 'p81g5e' }],\n  ['circle', { cx: '16.5', cy: '16.5', r: '.5', fill: 'currentColor', key: 'fubopw' }],\n  ['path', { d: 'm13.4 13.4 2.7 2.7', key: 'abhel3' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Vault\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KICA8cGF0aCBkPSJtNy45IDcuOSAyLjcgMi43IiAvPgogIDxjaXJjbGUgY3g9IjE2LjUiIGN5PSI3LjUiIHI9Ii41IiBmaWxsPSJjdXJyZW50Q29sb3IiIC8+CiAgPHBhdGggZD0ibTEzLjQgMTAuNiAyLjctMi43IiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjE2LjUiIHI9Ii41IiBmaWxsPSJjdXJyZW50Q29sb3IiIC8+CiAgPHBhdGggZD0ibTcuOSAxNi4xIDIuNy0yLjciIC8+CiAgPGNpcmNsZSBjeD0iMTYuNSIgY3k9IjE2LjUiIHI9Ii41IiBmaWxsPSJjdXJyZW50Q29sb3IiIC8+CiAgPHBhdGggZD0ibTEzLjQgMTMuNCAyLjcgMi43IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/vault\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Vault = createLucideIcon('vault', __iconNode);\n\nexport default Vault;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,UAAU;EAAEC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,KAAO;EAAAC,CAAA,EAAG,IAAM;EAAAC,IAAA,EAAM,cAAgB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjF,CAAC,MAAQ;EAAEK,CAAA,EAAG,kBAAoB;EAAAL,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,UAAU;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,KAAO;EAAAC,CAAA,EAAG,IAAM;EAAAC,IAAA,EAAM,cAAgB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,MAAQ;EAAEK,CAAA,EAAG,oBAAsB;EAAAL,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,UAAU;EAAEC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,CAAA,EAAG,IAAM;EAAAC,IAAA,EAAM,cAAgB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,MAAQ;EAAEK,CAAA,EAAG,mBAAqB;EAAAL,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,UAAU;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,CAAA,EAAG,IAAM;EAAAC,IAAA,EAAM,cAAgB;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACnF,CAAC,MAAQ;EAAEK,CAAA,EAAG,oBAAsB;EAAAL,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAM,KAAA,GAAQC,gBAAiB,UAASb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}