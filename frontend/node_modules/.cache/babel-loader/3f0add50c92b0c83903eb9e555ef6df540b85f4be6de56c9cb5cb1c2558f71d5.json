{"ast": null, "code": "var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\nexport default compose;", "map": {"version": 3, "names": ["compose", "_len", "arguments", "length", "fns", "Array", "_key", "x", "reduceRight", "y", "f"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/@monaco-editor/loader/lib/es/utils/compose.js"], "sourcesContent": ["var compose = function compose() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (x) {\n    return fns.reduceRight(function (y, f) {\n      return f(y);\n    }, x);\n  };\n};\n\nexport default compose;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;EAC/B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,GAAG,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACtFF,GAAG,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC7B;EAEA,OAAO,UAAUC,CAAC,EAAE;IAClB,OAAOH,GAAG,CAACI,WAAW,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACrC,OAAOA,CAAC,CAACD,CAAC,CAAC;IACb,CAAC,EAAEF,CAAC,CAAC;EACP,CAAC;AACH,CAAC;AAED,eAAeP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}