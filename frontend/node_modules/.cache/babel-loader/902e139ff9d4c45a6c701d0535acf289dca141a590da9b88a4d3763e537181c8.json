{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setPapers, setCurrentPaper, setSearchQuery } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Search, Plus, FileText } from 'lucide-react';\nimport api from '../../services/api';\nimport './PaperList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PaperList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    papers,\n    filteredPapers,\n    currentPaper,\n    searchQuery,\n    isLoading\n  } = useSelector(state => state.papers);\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);\n  const loadPapers = useCallback(async () => {\n    console.log('🔄 Step 1: Starting loadPapers function');\n\n    // Test basic functionality first\n    console.log('🔄 Step 2: Testing dispatch');\n    dispatch(setPapers([{\n      paper_id: 'test1',\n      title: 'Test Paper 1',\n      status: 'draft',\n      content: 'Test content 1',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }, {\n      paper_id: 'test2',\n      title: 'Test Paper 2',\n      status: 'draft',\n      content: 'Test content 2',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }]));\n    console.log('✅ Step 3: Dispatch completed successfully');\n\n    // Now try the API call\n    console.log('🔄 Step 4: About to test API call');\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/papers');\n      console.log('📡 Step 5: Fetch response:', response);\n      const data = await response.json();\n      console.log('📄 Step 6: Response data:', data);\n    } catch (error) {\n      console.error('💥 Step 7: API call failed:', error);\n    }\n  }, [dispatch]);\n  useEffect(() => {\n    // Load papers on component mount\n    loadPapers();\n  }, [loadPapers]);\n  const handleSearchChange = e => {\n    const query = e.target.value;\n    setLocalSearchQuery(query);\n    dispatch(setSearchQuery(query));\n  };\n  const handlePaperSelect = async paper => {\n    try {\n      // First set the paper with basic info\n      dispatch(setCurrentPaper(paper));\n\n      // Then fetch the full paper content\n      const result = await api.papers.get(paper.paper_id);\n      if (result.data) {\n        // Update with full content\n        dispatch(setCurrentPaper(result.data));\n      } else {\n        throw new Error(result.error || 'Failed to load paper content');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to load paper content',\n        duration: 5000\n      }));\n    }\n  };\n  const handleCreatePaper = async () => {\n    try {\n      const title = `New Paper ${new Date().toLocaleDateString()}`;\n      const content = `# ${title}\\n\\n## Introduction\\n\\nStart writing your paper here...`;\n      const result = await api.papers.create(title, content);\n      if (result.data) {\n        loadPapers(); // Reload papers list\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Created',\n          message: `Created \"${title}\"`,\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create paper');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to create paper',\n        duration: 5000\n      }));\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paper-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paper-list-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"paper-list-title\",\n        children: \"Papers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paper-search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 16,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search papers...\",\n            value: localSearchQuery,\n            onChange: handleSearchChange,\n            className: \"paper-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreatePaper,\n        className: \"create-paper-button\",\n        title: \"Create new paper\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"New Paper\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paper-list-content\",\n      children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading papers...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this) : filteredPapers.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: papers.length === 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            size: 48,\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"No Papers Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Create your first research paper to get started.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreatePaper,\n            className: \"create-first-paper\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 19\n            }, this), \"Create First Paper\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 48,\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"No Results Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No papers match your search query.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"papers-grid\",\n        children: filteredPapers.map(paper => {\n          var _paper$metadata;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `paper-item ${(currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id) === paper.paper_id ? 'active' : ''}`,\n            onClick: () => handlePaperSelect(paper),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"paper-item-title\",\n                children: paper.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `paper-item-status ${paper.status}`,\n                children: paper.status.replace('_', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-content\",\n              children: paper.content && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"paper-item-preview\",\n                children: [paper.content.replace(/[#*`]/g, '').substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"paper-item-date\",\n                children: [\"Updated \", formatDate(paper.updated_at)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), ((_paper$metadata = paper.metadata) === null || _paper$metadata === void 0 ? void 0 : _paper$metadata.word_count) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"paper-item-words\",\n                children: [paper.metadata.word_count, \" words\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, paper.paper_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperList, \"udYG1k5LcobjNYABHmt/6Wa4ZZc=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = PaperList;\nexport default PaperList;\nvar _c;\n$RefreshReg$(_c, \"PaperList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "useSelector", "setPapers", "setCurrentPaper", "setSearch<PERSON>uery", "addNotification", "Search", "Plus", "FileText", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PaperList", "_s", "dispatch", "papers", "filteredPapers", "currentPaper", "searchQuery", "isLoading", "state", "localSearchQuery", "setLocalSearchQuery", "loadPapers", "console", "log", "paper_id", "title", "status", "content", "created_at", "Date", "toISOString", "updated_at", "response", "fetch", "data", "json", "error", "handleSearchChange", "e", "query", "target", "value", "handlePaperSelect", "paper", "result", "get", "Error", "type", "message", "duration", "handleCreatePaper", "toLocaleDateString", "create", "formatDate", "dateString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "placeholder", "onChange", "onClick", "length", "map", "_paper$metadata", "replace", "substring", "metadata", "word_count", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { setPapers, setCurrentPaper, setSearchQuery } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Search, Plus, FileText } from 'lucide-react';\nimport api from '../../services/api';\nimport './PaperList.css';\n\nconst PaperList: React.FC = () => {\n  const dispatch = useDispatch();\n  const { papers, filteredPapers, currentPaper, searchQuery, isLoading } = useSelector((state: RootState) => state.papers);\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);\n\n  const loadPapers = useCallback(async () => {\n    console.log('🔄 Step 1: Starting loadPapers function');\n\n    // Test basic functionality first\n    console.log('🔄 Step 2: Testing dispatch');\n    dispatch(setPapers([\n      {\n        paper_id: 'test1',\n        title: 'Test Paper 1',\n        status: 'draft',\n        content: 'Test content 1',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        paper_id: 'test2',\n        title: 'Test Paper 2',\n        status: 'draft',\n        content: 'Test content 2',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ]));\n    console.log('✅ Step 3: Dispatch completed successfully');\n\n    // Now try the API call\n    console.log('🔄 Step 4: About to test API call');\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/papers');\n      console.log('📡 Step 5: Fetch response:', response);\n      const data = await response.json();\n      console.log('📄 Step 6: Response data:', data);\n    } catch (error) {\n      console.error('💥 Step 7: API call failed:', error);\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Load papers on component mount\n    loadPapers();\n  }, [loadPapers]);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setLocalSearchQuery(query);\n    dispatch(setSearchQuery(query));\n  };\n\n  const handlePaperSelect = async (paper: any) => {\n    try {\n      // First set the paper with basic info\n      dispatch(setCurrentPaper(paper));\n\n      // Then fetch the full paper content\n      const result = await api.papers.get(paper.paper_id);\n      if (result.data) {\n        // Update with full content\n        dispatch(setCurrentPaper(result.data));\n      } else {\n        throw new Error(result.error || 'Failed to load paper content');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to load paper content',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleCreatePaper = async () => {\n    try {\n      const title = `New Paper ${new Date().toLocaleDateString()}`;\n      const content = `# ${title}\\n\\n## Introduction\\n\\nStart writing your paper here...`;\n\n      const result = await api.papers.create(title, content);\n\n      if (result.data) {\n        loadPapers(); // Reload papers list\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Created',\n          message: `Created \"${title}\"`,\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create paper');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to create paper',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <div className=\"paper-list\">\n      <div className=\"paper-list-header\">\n        <h3 className=\"paper-list-title\">Papers</h3>\n\n        <div className=\"paper-search-container\">\n          <div className=\"search-input-wrapper\">\n            <Search size={16} className=\"search-icon\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search papers...\"\n              value={localSearchQuery}\n              onChange={handleSearchChange}\n              className=\"paper-search\"\n            />\n          </div>\n        </div>\n\n        <button\n          onClick={handleCreatePaper}\n          className=\"create-paper-button\"\n          title=\"Create new paper\"\n        >\n          <Plus size={16} />\n          <span>New Paper</span>\n        </button>\n      </div>\n\n      <div className=\"paper-list-content\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <span>Loading papers...</span>\n          </div>\n        ) : filteredPapers.length === 0 ? (\n          <div className=\"empty-state\">\n            {papers.length === 0 ? (\n              <>\n                <FileText size={48} className=\"empty-icon\" />\n                <h4>No Papers Yet</h4>\n                <p>Create your first research paper to get started.</p>\n                <button onClick={handleCreatePaper} className=\"create-first-paper\">\n                  <Plus size={16} />\n                  Create First Paper\n                </button>\n              </>\n            ) : (\n              <>\n                <Search size={48} className=\"empty-icon\" />\n                <h4>No Results Found</h4>\n                <p>No papers match your search query.</p>\n              </>\n            )}\n          </div>\n        ) : (\n          <div className=\"papers-grid\">\n            {filteredPapers.map((paper) => (\n              <div\n                key={paper.paper_id}\n                className={`paper-item ${currentPaper?.paper_id === paper.paper_id ? 'active' : ''}`}\n                onClick={() => handlePaperSelect(paper)}\n              >\n                <div className=\"paper-item-header\">\n                  <h4 className=\"paper-item-title\">{paper.title}</h4>\n                  <span className={`paper-item-status ${paper.status}`}>\n                    {paper.status.replace('_', ' ')}\n                  </span>\n                </div>\n\n                <div className=\"paper-item-content\">\n                  {paper.content && (\n                    <p className=\"paper-item-preview\">\n                      {paper.content.replace(/[#*`]/g, '').substring(0, 100)}...\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"paper-item-meta\">\n                  <span className=\"paper-item-date\">\n                    Updated {formatDate(paper.updated_at)}\n                  </span>\n                  {paper.metadata?.word_count && (\n                    <span className=\"paper-item-words\">\n                      {paper.metadata.word_count} words\n                    </span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PaperList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,SAAS,EAAEC,eAAe,EAAEC,cAAc,QAAQ,gCAAgC;AAC3F,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACrD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,MAAM;IAAEC,cAAc;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGpB,WAAW,CAAEqB,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAAC;EACxH,MAAM,CAACM,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAACuB,WAAW,CAAC;EAErE,MAAMK,UAAU,GAAG1B,WAAW,CAAC,YAAY;IACzC2B,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;;IAEtD;IACAD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CX,QAAQ,CAACd,SAAS,CAAC,CACjB;MACE0B,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,cAAc;MACrBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC,EACD;MACEN,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,cAAc;MACrBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC,CACF,CAAC,CAAC;IACHR,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;IAExD;IACAD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,CAAC;MACnEX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAES,QAAQ,CAAC;MACnD,MAAME,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEW,IAAI,CAAC;IAChD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;EAEdlB,SAAS,CAAC,MAAM;IACd;IACA2B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMgB,kBAAkB,GAAIC,CAAsC,IAAK;IACrE,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAC5BrB,mBAAmB,CAACmB,KAAK,CAAC;IAC1B3B,QAAQ,CAACZ,cAAc,CAACuC,KAAK,CAAC,CAAC;EACjC,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOC,KAAU,IAAK;IAC9C,IAAI;MACF;MACA/B,QAAQ,CAACb,eAAe,CAAC4C,KAAK,CAAC,CAAC;;MAEhC;MACA,MAAMC,MAAM,GAAG,MAAMvC,GAAG,CAACQ,MAAM,CAACgC,GAAG,CAACF,KAAK,CAACnB,QAAQ,CAAC;MACnD,IAAIoB,MAAM,CAACV,IAAI,EAAE;QACf;QACAtB,QAAQ,CAACb,eAAe,CAAC6C,MAAM,CAACV,IAAI,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,MAAM,IAAIY,KAAK,CAACF,MAAM,CAACR,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdxB,QAAQ,CAACX,eAAe,CAAC;QACvB8C,IAAI,EAAE,OAAO;QACbtB,KAAK,EAAE,OAAO;QACduB,OAAO,EAAE,8BAA8B;QACvCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMzB,KAAK,GAAG,aAAa,IAAII,IAAI,CAAC,CAAC,CAACsB,kBAAkB,CAAC,CAAC,EAAE;MAC5D,MAAMxB,OAAO,GAAG,KAAKF,KAAK,yDAAyD;MAEnF,MAAMmB,MAAM,GAAG,MAAMvC,GAAG,CAACQ,MAAM,CAACuC,MAAM,CAAC3B,KAAK,EAAEE,OAAO,CAAC;MAEtD,IAAIiB,MAAM,CAACV,IAAI,EAAE;QACfb,UAAU,CAAC,CAAC,CAAC,CAAC;QACdT,QAAQ,CAACX,eAAe,CAAC;UACvB8C,IAAI,EAAE,SAAS;UACftB,KAAK,EAAE,eAAe;UACtBuB,OAAO,EAAE,YAAYvB,KAAK,GAAG;UAC7BwB,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIH,KAAK,CAACF,MAAM,CAACR,KAAK,IAAI,wBAAwB,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdxB,QAAQ,CAACX,eAAe,CAAC;QACvB8C,IAAI,EAAE,OAAO;QACbtB,KAAK,EAAE,OAAO;QACduB,OAAO,EAAE,wBAAwB;QACjCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIzB,IAAI,CAACyB,UAAU,CAAC,CAACH,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACE5C,OAAA;IAAKgD,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBjD,OAAA;MAAKgD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCjD,OAAA;QAAIgD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5CrD,OAAA;QAAKgD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCjD,OAAA;UAAKgD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnCjD,OAAA,CAACL,MAAM;YAAC2D,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CrD,OAAA;YACEwC,IAAI,EAAC,MAAM;YACXe,WAAW,EAAC,kBAAkB;YAC9BrB,KAAK,EAAEtB,gBAAiB;YACxB4C,QAAQ,EAAE1B,kBAAmB;YAC7BkB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrD,OAAA;QACEyD,OAAO,EAAEd,iBAAkB;QAC3BK,SAAS,EAAC,qBAAqB;QAC/B9B,KAAK,EAAC,kBAAkB;QAAA+B,QAAA,gBAExBjD,OAAA,CAACJ,IAAI;UAAC0D,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClBrD,OAAA;UAAAiD,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENrD,OAAA;MAAKgD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCvC,SAAS,gBACRV,OAAA;QAAKgD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjD,OAAA;UAAKgD,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCrD,OAAA;UAAAiD,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,GACJ9C,cAAc,CAACmD,MAAM,KAAK,CAAC,gBAC7B1D,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB3C,MAAM,CAACoD,MAAM,KAAK,CAAC,gBAClB1D,OAAA,CAAAE,SAAA;UAAA+C,QAAA,gBACEjD,OAAA,CAACH,QAAQ;YAACyD,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CrD,OAAA;YAAAiD,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBrD,OAAA;YAAAiD,QAAA,EAAG;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvDrD,OAAA;YAAQyD,OAAO,EAAEd,iBAAkB;YAACK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAChEjD,OAAA,CAACJ,IAAI;cAAC0D,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;UAAA+C,QAAA,gBACEjD,OAAA,CAACL,MAAM;YAAC2D,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CrD,OAAA;YAAAiD,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBrD,OAAA;YAAAiD,QAAA,EAAG;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA,eACzC;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENrD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB1C,cAAc,CAACoD,GAAG,CAAEvB,KAAK;UAAA,IAAAwB,eAAA;UAAA,oBACxB5D,OAAA;YAEEgD,SAAS,EAAE,cAAc,CAAAxC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAAQ,MAAKmB,KAAK,CAACnB,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrFwC,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAACC,KAAK,CAAE;YAAAa,QAAA,gBAExCjD,OAAA;cAAKgD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjD,OAAA;gBAAIgD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEb,KAAK,CAAClB;cAAK;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnDrD,OAAA;gBAAMgD,SAAS,EAAE,qBAAqBZ,KAAK,CAACjB,MAAM,EAAG;gBAAA8B,QAAA,EAClDb,KAAK,CAACjB,MAAM,CAAC0C,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAChCb,KAAK,CAAChB,OAAO,iBACZpB,OAAA;gBAAGgD,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAC9Bb,KAAK,CAAChB,OAAO,CAACyC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzD;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACJ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENrD,OAAA;cAAKgD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjD,OAAA;gBAAMgD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,UACxB,EAACH,UAAU,CAACV,KAAK,CAACZ,UAAU,CAAC;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,EACN,EAAAO,eAAA,GAAAxB,KAAK,CAAC2B,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,UAAU,kBACzBhE,OAAA;gBAAMgD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAC/Bb,KAAK,CAAC2B,QAAQ,CAACC,UAAU,EAAC,QAC7B;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GA5BDjB,KAAK,CAACnB,QAAQ;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BhB,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA1MID,SAAmB;EAAA,QACNd,WAAW,EAC6CC,WAAW;AAAA;AAAA2E,EAAA,GAFhF9D,SAAmB;AA4MzB,eAAeA,SAAS;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}