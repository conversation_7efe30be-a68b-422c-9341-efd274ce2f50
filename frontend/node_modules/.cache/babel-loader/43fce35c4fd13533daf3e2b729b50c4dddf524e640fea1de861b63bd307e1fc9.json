{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { addChart } from '../../store/slices/toolsSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatPanel = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    messages,\n    isStreaming,\n    isLoading,\n    currentSessionId\n  } = useSelector(state => state.chat);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper,\n    cursorPosition\n  } = useSelector(state => state.papers);\n  const [inputValue, setInputValue] = useState('');\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n    console.log('Sending message:', inputValue.trim());\n    console.log('Current session ID:', currentSessionId);\n    console.log('Current paper ID:', currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id);\n    const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    const userMessage = {\n      id: userMessageId,\n      role: 'user',\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString()\n    };\n    dispatch(addMessage(userMessage));\n    const messageContent = inputValue.trim(); // Store before clearing\n    setInputValue('');\n    dispatch(setLoading(true));\n    try {\n      var _response$body;\n      // Create assistant message placeholder\n      const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString()\n      }));\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(messageContent, currentSessionId || 'default-session', currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id, cursorPosition);\n      console.log('Response status:', response.status, response.ok);\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = (_response$body = response.body) === null || _response$body === void 0 ? void 0 : _response$body.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n      console.log('Starting to read streaming response...');\n      if (reader) {\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) {\n            console.log('Streaming completed');\n            break;\n          }\n          const chunk = decoder.decode(value);\n          console.log('Received chunk:', chunk);\n          const lines = chunk.split('\\n');\n          let currentEvent = '';\n          for (const line of lines) {\n            if (line.startsWith('event: ')) {\n              currentEvent = line.slice(7).trim();\n              console.log('Event type:', currentEvent);\n            } else if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n                console.log('Event data:', currentEvent, data);\n                switch (currentEvent) {\n                  case 'start':\n                    console.log('Chat started');\n                    break;\n                  case 'step':\n                    if (data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.tool_name}...`,\n                        duration: 3000\n                      }));\n                    } else if (data.type === 'tool_result') {\n                      var _data$result;\n                      // Handle tool results, especially chart creation\n                      if (data.tool_name && data.tool_name.includes('chart') && (_data$result = data.result) !== null && _data$result !== void 0 && _data$result.success) {\n                        const result = data.result;\n                        dispatch(addChart({\n                          type: result.chart_type || 'line',\n                          title: result.title || 'Chart',\n                          url: result.chart_url,\n                          data: result.data || {}\n                        }));\n                        dispatch(addNotification({\n                          type: 'success',\n                          title: 'Chart Created',\n                          message: `${result.title || 'Chart'} has been created successfully!`,\n                          duration: 5000\n                        }));\n                      }\n                    } else if (data.type === 'agent_response' && data.content) {\n                      // This is the actual response content\n                      assistantContent = data.content;\n                      console.log('Updating message with content from step:', assistantContent);\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent\n                      }));\n                    }\n                    break;\n                  case 'response':\n                    // This event contains metadata, the actual content is in the step event\n                    console.log('Response event received:', data);\n                    break;\n                  case 'complete':\n                    console.log('Chat completed');\n                    dispatch(setStreaming(false));\n\n                    // Check if paper was updated and refresh if needed\n                    if (data.paper_updated && data.updated_paper_id && (currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id) === data.updated_paper_id) {\n                      console.log('Paper was updated, refreshing editor...');\n                      // Trigger paper refresh by dispatching an action\n                      window.dispatchEvent(new CustomEvent('paperUpdated', {\n                        detail: {\n                          paperId: data.updated_paper_id\n                        }\n                      }));\n                    }\n                    return;\n                  case 'error':\n                    console.error('Chat error:', data);\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.error || 'An error occurred',\n                      duration: 5000\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n                console.warn('Failed to parse SSE data:', line, e);\n              }\n            }\n          }\n        }\n      }\n      dispatch(setStreaming(false));\n    } catch (error) {\n      console.error('Chat error:', error);\n      dispatch(setStreaming(false));\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Chat Error',\n        message: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000\n      }));\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const testConnection = async () => {\n    try {\n      console.log('Testing backend connection...');\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      const data = await response.json();\n      console.log('Backend health check:', data);\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Test',\n        message: `Backend is ${data.status}`,\n        duration: 3000\n      }));\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Connection Test',\n        message: 'Failed to connect to backend',\n        duration: 5000\n      }));\n    }\n  };\n  if (sidebarCollapsed) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-panel-collapsed\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapsed-icon\",\n        children: /*#__PURE__*/_jsxDEV(Send, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-panel-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: testConnection,\n        style: {\n          fontSize: '12px',\n          padding: '4px 8px'\n        },\n        children: \"Test Connection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), currentPaper && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-paper-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC4 \", currentPaper.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-body\",\n      children: [/*#__PURE__*/_jsxDEV(MessageList, {\n        messages: messages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToolStatusList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-footer\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        value: inputValue,\n        onChange: setInputValue,\n        onSend: handleSendMessage,\n        onKeyPress: handleKeyPress,\n        disabled: isStreaming || isLoading,\n        placeholder: \"Ask me anything about research papers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatPanel, \"zzZiqSSneNMKn98OwTscWNYsGHY=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = ChatPanel;\nexport default ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "addMessage", "updateMessage", "setStreaming", "setLoading", "addNotification", "add<PERSON><PERSON>", "MessageList", "MessageInput", "ToolStatusList", "Send", "api", "jsxDEV", "_jsxDEV", "ChatPanel", "_s", "dispatch", "messages", "isStreaming", "isLoading", "currentSessionId", "state", "chat", "sidebarCollapsed", "ui", "currentPaper", "cursorPosition", "papers", "inputValue", "setInputValue", "handleSendMessage", "trim", "console", "log", "paper_id", "userMessageId", "Date", "now", "Math", "random", "toString", "substr", "userMessage", "id", "role", "content", "timestamp", "toISOString", "messageContent", "_response$body", "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "sendMessage", "status", "ok", "Error", "reader", "body", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "chunk", "decode", "lines", "split", "currentEvent", "line", "startsWith", "slice", "data", "JSON", "parse", "type", "title", "message", "tool_name", "duration", "_data$result", "includes", "result", "success", "chart_type", "url", "chart_url", "paper_updated", "updated_paper_id", "window", "dispatchEvent", "CustomEvent", "detail", "paperId", "error", "e", "warn", "handleKeyPress", "key", "shift<PERSON>ey", "preventDefault", "testConnection", "fetch", "json", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "fontSize", "padding", "onChange", "onSend", "onKeyPress", "disabled", "placeholder", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { addChart } from '../../store/slices/toolsSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\n\nconst ChatPanel: React.FC = () => {\n  const dispatch = useDispatch();\n  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentPaper, cursorPosition } = useSelector((state: RootState) => state.papers);\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n\n    console.log('Sending message:', inputValue.trim());\n    console.log('Current session ID:', currentSessionId);\n    console.log('Current paper ID:', currentPaper?.paper_id);\n\n    const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    const userMessage = {\n      id: userMessageId,\n      role: 'user' as const,\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString(),\n    };\n\n    dispatch(addMessage(userMessage));\n    const messageContent = inputValue.trim(); // Store before clearing\n    setInputValue('');\n    dispatch(setLoading(true));\n\n    try {\n      // Create assistant message placeholder\n      const assistantMessageId = `assistant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString(),\n      }));\n\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(\n        messageContent,\n        currentSessionId || 'default-session',\n        currentPaper?.paper_id,\n        cursorPosition\n      );\n\n      console.log('Response status:', response.status, response.ok);\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = response.body?.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n\n      console.log('Starting to read streaming response...');\n\n      if (reader) {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) {\n            console.log('Streaming completed');\n            break;\n          }\n\n          const chunk = decoder.decode(value);\n          console.log('Received chunk:', chunk);\n          const lines = chunk.split('\\n');\n\n          let currentEvent = '';\n\n          for (const line of lines) {\n            if (line.startsWith('event: ')) {\n              currentEvent = line.slice(7).trim();\n              console.log('Event type:', currentEvent);\n            } else if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n                console.log('Event data:', currentEvent, data);\n\n                switch (currentEvent) {\n                  case 'start':\n                    console.log('Chat started');\n                    break;\n\n                  case 'step':\n                    if (data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.tool_name}...`,\n                        duration: 3000,\n                      }));\n                    } else if (data.type === 'tool_result') {\n                      // Handle tool results, especially chart creation\n                      if (data.tool_name && data.tool_name.includes('chart') && data.result?.success) {\n                        const result = data.result;\n                        dispatch(addChart({\n                          type: result.chart_type || 'line',\n                          title: result.title || 'Chart',\n                          url: result.chart_url,\n                          data: result.data || {},\n                        }));\n\n                        dispatch(addNotification({\n                          type: 'success',\n                          title: 'Chart Created',\n                          message: `${result.title || 'Chart'} has been created successfully!`,\n                          duration: 5000,\n                        }));\n                      }\n                    } else if (data.type === 'agent_response' && data.content) {\n                      // This is the actual response content\n                      assistantContent = data.content;\n                      console.log('Updating message with content from step:', assistantContent);\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent,\n                      }));\n                    }\n                    break;\n\n                  case 'response':\n                    // This event contains metadata, the actual content is in the step event\n                    console.log('Response event received:', data);\n                    break;\n\n                  case 'complete':\n                    console.log('Chat completed');\n                    dispatch(setStreaming(false));\n\n                    // Check if paper was updated and refresh if needed\n                    if (data.paper_updated && data.updated_paper_id && currentPaper?.paper_id === data.updated_paper_id) {\n                      console.log('Paper was updated, refreshing editor...');\n                      // Trigger paper refresh by dispatching an action\n                      window.dispatchEvent(new CustomEvent('paperUpdated', {\n                        detail: { paperId: data.updated_paper_id }\n                      }));\n                    }\n                    return;\n\n                  case 'error':\n                    console.error('Chat error:', data);\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.error || 'An error occurred',\n                      duration: 5000,\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n                console.warn('Failed to parse SSE data:', line, e);\n              }\n            }\n          }\n        }\n      }\n\n      dispatch(setStreaming(false));\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      dispatch(setStreaming(false));\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Chat Error',\n        message: `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const testConnection = async () => {\n    try {\n      console.log('Testing backend connection...');\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      const data = await response.json();\n      console.log('Backend health check:', data);\n\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Test',\n        message: `Backend is ${data.status}`,\n        duration: 3000,\n      }));\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Connection Test',\n        message: 'Failed to connect to backend',\n        duration: 5000,\n      }));\n    }\n  };\n\n\n\n  if (sidebarCollapsed) {\n    return (\n      <div className=\"chat-panel-collapsed\">\n        <div className=\"collapsed-icon\">\n          <Send size={20} />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"chat-panel-content\">\n      <div className=\"chat-header\">\n        <h2>Chat</h2>\n        <button onClick={testConnection} style={{ fontSize: '12px', padding: '4px 8px' }}>\n          Test Connection\n        </button>\n        {currentPaper && (\n          <div className=\"current-paper-indicator\">\n            <span>📄 {currentPaper.title}</span>\n          </div>\n        )}\n      </div>\n\n      <div className=\"chat-body\">\n        <MessageList messages={messages} />\n        <ToolStatusList />\n      </div>\n\n      <div className=\"chat-footer\">\n        <MessageInput\n          value={inputValue}\n          onChange={setInputValue}\n          onSend={handleSendMessage}\n          onKeyPress={handleKeyPress}\n          disabled={isStreaming || isLoading}\n          placeholder=\"Ask me anything about research papers...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ChatPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,UAAU,EAAEC,aAAa,EAAEC,YAAY,EAAEC,UAAU,QAAQ,8BAA8B;AAClG,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,QAAQ,QAAQ,+BAA+B;AACxD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,QAAQ;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAiB,CAAC,GAAGpB,WAAW,CAAEqB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC5G,MAAM;IAAEC;EAAiB,CAAC,GAAGvB,WAAW,CAAEqB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EACxE,MAAM;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAG1B,WAAW,CAAEqB,KAAgB,IAAKA,KAAK,CAACM,MAAM,CAAC;EACxF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMgC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,CAAC,IAAIb,WAAW,IAAIC,SAAS,EAAE;IAEpDa,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEL,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,gBAAgB,CAAC;IACpDY,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAER,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAAQ,CAAC;IAExD,MAAMC,aAAa,GAAG,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACrF,MAAMC,WAAW,GAAG;MAClBC,EAAE,EAAER,aAAa;MACjBS,IAAI,EAAE,MAAe;MACrBC,OAAO,EAAEjB,UAAU,CAACG,IAAI,CAAC,CAAC;MAC1Be,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;IACpC,CAAC;IAED/B,QAAQ,CAACf,UAAU,CAACyC,WAAW,CAAC,CAAC;IACjC,MAAMM,cAAc,GAAGpB,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1CF,aAAa,CAAC,EAAE,CAAC;IACjBb,QAAQ,CAACZ,UAAU,CAAC,IAAI,CAAC,CAAC;IAE1B,IAAI;MAAA,IAAA6C,cAAA;MACF;MACA,MAAMC,kBAAkB,GAAG,aAAad,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC/FzB,QAAQ,CAACf,UAAU,CAAC;QAClB0C,EAAE,EAAEO,kBAAkB;QACtBN,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACW,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC;MAEH/B,QAAQ,CAACZ,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BY,QAAQ,CAACb,YAAY,CAAC,IAAI,CAAC,CAAC;;MAE5B;MACA,MAAMgD,QAAQ,GAAG,MAAMxC,GAAG,CAACW,IAAI,CAAC8B,WAAW,CACzCJ,cAAc,EACd5B,gBAAgB,IAAI,iBAAiB,EACrCK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAAQ,EACtBR,cACF,CAAC;MAEDM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkB,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,EAAE,CAAC;MAE7D,IAAI,CAACH,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;;MAEA;MACA,MAAMC,MAAM,IAAAP,cAAA,GAAGE,QAAQ,CAACM,IAAI,cAAAR,cAAA,uBAAbA,cAAA,CAAeS,SAAS,CAAC,CAAC;MACzC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MACjC,IAAIC,gBAAgB,GAAG,EAAE;MAEzB7B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD,IAAIuB,MAAM,EAAE;QACV,OAAO,IAAI,EAAE;UACX,MAAM;YAAEM,IAAI;YAAEC;UAAM,CAAC,GAAG,MAAMP,MAAM,CAACQ,IAAI,CAAC,CAAC;UAC3C,IAAIF,IAAI,EAAE;YACR9B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;YAClC;UACF;UAEA,MAAMgC,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACH,KAAK,CAAC;UACnC/B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgC,KAAK,CAAC;UACrC,MAAME,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;UAE/B,IAAIC,YAAY,GAAG,EAAE;UAErB,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;YACxB,IAAIG,IAAI,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE;cAC9BF,YAAY,GAAGC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAACzC,IAAI,CAAC,CAAC;cACnCC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoC,YAAY,CAAC;YAC1C,CAAC,MAAM,IAAIC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;cACpC,IAAI;gBACF,MAAME,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtCxC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoC,YAAY,EAAEI,IAAI,CAAC;gBAE9C,QAAQJ,YAAY;kBAClB,KAAK,OAAO;oBACVrC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;oBAC3B;kBAEF,KAAK,MAAM;oBACT,IAAIwC,IAAI,CAACG,IAAI,KAAK,WAAW,EAAE;sBAC7B5D,QAAQ,CAACX,eAAe,CAAC;wBACvBuE,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,gBAAgB;wBACvBC,OAAO,EAAE,aAAaL,IAAI,CAACM,SAAS,KAAK;wBACzCC,QAAQ,EAAE;sBACZ,CAAC,CAAC,CAAC;oBACL,CAAC,MAAM,IAAIP,IAAI,CAACG,IAAI,KAAK,aAAa,EAAE;sBAAA,IAAAK,YAAA;sBACtC;sBACA,IAAIR,IAAI,CAACM,SAAS,IAAIN,IAAI,CAACM,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC,KAAAD,YAAA,GAAIR,IAAI,CAACU,MAAM,cAAAF,YAAA,eAAXA,YAAA,CAAaG,OAAO,EAAE;wBAC9E,MAAMD,MAAM,GAAGV,IAAI,CAACU,MAAM;wBAC1BnE,QAAQ,CAACV,QAAQ,CAAC;0BAChBsE,IAAI,EAAEO,MAAM,CAACE,UAAU,IAAI,MAAM;0BACjCR,KAAK,EAAEM,MAAM,CAACN,KAAK,IAAI,OAAO;0BAC9BS,GAAG,EAAEH,MAAM,CAACI,SAAS;0BACrBd,IAAI,EAAEU,MAAM,CAACV,IAAI,IAAI,CAAC;wBACxB,CAAC,CAAC,CAAC;wBAEHzD,QAAQ,CAACX,eAAe,CAAC;0BACvBuE,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,eAAe;0BACtBC,OAAO,EAAE,GAAGK,MAAM,CAACN,KAAK,IAAI,OAAO,iCAAiC;0BACpEG,QAAQ,EAAE;wBACZ,CAAC,CAAC,CAAC;sBACL;oBACF,CAAC,MAAM,IAAIP,IAAI,CAACG,IAAI,KAAK,gBAAgB,IAAIH,IAAI,CAAC5B,OAAO,EAAE;sBACzD;sBACAgB,gBAAgB,GAAGY,IAAI,CAAC5B,OAAO;sBAC/Bb,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE4B,gBAAgB,CAAC;sBACzE7C,QAAQ,CAACd,aAAa,CAAC;wBACrByC,EAAE,EAAEO,kBAAkB;wBACtBL,OAAO,EAAEgB;sBACX,CAAC,CAAC,CAAC;oBACL;oBACA;kBAEF,KAAK,UAAU;oBACb;oBACA7B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwC,IAAI,CAAC;oBAC7C;kBAEF,KAAK,UAAU;oBACbzC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;oBAC7BjB,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAC;;oBAE7B;oBACA,IAAIsE,IAAI,CAACe,aAAa,IAAIf,IAAI,CAACgB,gBAAgB,IAAI,CAAAhE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAES,QAAQ,MAAKuC,IAAI,CAACgB,gBAAgB,EAAE;sBACnGzD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;sBACtD;sBACAyD,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,cAAc,EAAE;wBACnDC,MAAM,EAAE;0BAAEC,OAAO,EAAErB,IAAI,CAACgB;wBAAiB;sBAC3C,CAAC,CAAC,CAAC;oBACL;oBACA;kBAEF,KAAK,OAAO;oBACVzD,OAAO,CAAC+D,KAAK,CAAC,aAAa,EAAEtB,IAAI,CAAC;oBAClCzD,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7Ba,QAAQ,CAACX,eAAe,CAAC;sBACvBuE,IAAI,EAAE,OAAO;sBACbC,KAAK,EAAE,YAAY;sBACnBC,OAAO,EAAEL,IAAI,CAACsB,KAAK,IAAI,mBAAmB;sBAC1Cf,QAAQ,EAAE;oBACZ,CAAC,CAAC,CAAC;oBACH;gBACJ;cACF,CAAC,CAAC,OAAOgB,CAAC,EAAE;gBACV;gBACAhE,OAAO,CAACiE,IAAI,CAAC,2BAA2B,EAAE3B,IAAI,EAAE0B,CAAC,CAAC;cACpD;YACF;UACF;QACF;MACF;MAEAhF,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAC;IAE/B,CAAC,CAAC,OAAO4F,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC/E,QAAQ,CAACb,YAAY,CAAC,KAAK,CAAC,CAAC;MAC7Ba,QAAQ,CAACZ,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BY,QAAQ,CAACX,eAAe,CAAC;QACvBuE,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,YAAY;QACnBC,OAAO,EAAE,2BAA2BiB,KAAK,YAAYxC,KAAK,GAAGwC,KAAK,CAACjB,OAAO,GAAG,eAAe,EAAE;QAC9FE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMkB,cAAc,GAAIF,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI,CAACH,CAAC,CAACI,QAAQ,EAAE;MACpCJ,CAAC,CAACK,cAAc,CAAC,CAAC;MAClBvE,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMwE,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFtE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMkB,QAAQ,GAAG,MAAMoD,KAAK,CAAC,qCAAqC,CAAC;MACnE,MAAM9B,IAAI,GAAG,MAAMtB,QAAQ,CAACqD,IAAI,CAAC,CAAC;MAClCxE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwC,IAAI,CAAC;MAE1CzD,QAAQ,CAACX,eAAe,CAAC;QACvBuE,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,cAAcL,IAAI,CAACpB,MAAM,EAAE;QACpC2B,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd/D,OAAO,CAAC+D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C/E,QAAQ,CAACX,eAAe,CAAC;QACvBuE,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,8BAA8B;QACvCE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAID,IAAIzD,gBAAgB,EAAE;IACpB,oBACEV,OAAA;MAAK4F,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnC7F,OAAA;QAAK4F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B7F,OAAA,CAACH,IAAI;UAACiG,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElG,OAAA;IAAK4F,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjC7F,OAAA;MAAK4F,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B7F,OAAA;QAAA6F,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACblG,OAAA;QAAQmG,OAAO,EAAEV,cAAe;QAACW,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAU,CAAE;QAAAT,QAAA,EAAC;MAElF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRtF,YAAY,iBACXZ,OAAA;QAAK4F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtC7F,OAAA;UAAA6F,QAAA,GAAM,eAAG,EAACjF,YAAY,CAACoD,KAAK;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENlG,OAAA;MAAK4F,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB7F,OAAA,CAACN,WAAW;QAACU,QAAQ,EAAEA;MAAS;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnClG,OAAA,CAACJ,cAAc;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENlG,OAAA;MAAK4F,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B7F,OAAA,CAACL,YAAY;QACXuD,KAAK,EAAEnC,UAAW;QAClBwF,QAAQ,EAAEvF,aAAc;QACxBwF,MAAM,EAAEvF,iBAAkB;QAC1BwF,UAAU,EAAEpB,cAAe;QAC3BqB,QAAQ,EAAErG,WAAW,IAAIC,SAAU;QACnCqG,WAAW,EAAC;MAA0C;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChG,EAAA,CA9PID,SAAmB;EAAA,QACNf,WAAW,EACmCC,WAAW,EAC7CA,WAAW,EACCA,WAAW;AAAA;AAAAyH,EAAA,GAJhD3G,SAAmB;AAgQzB,eAAeA,SAAS;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}