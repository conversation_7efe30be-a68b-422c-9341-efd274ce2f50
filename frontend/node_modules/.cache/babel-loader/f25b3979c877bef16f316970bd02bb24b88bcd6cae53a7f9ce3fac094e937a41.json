{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector } from 'react-redux';\nimport PaperEditor from '../Editor/PaperEditor';\nimport ChartViewer from '../Charts/ChartViewer';\nimport SearchResults from '../Search/SearchResults';\nimport WelcomeScreen from './WelcomeScreen';\nimport './MainWorkArea.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainWorkArea = () => {\n  _s();\n  const {\n    currentView\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper\n  } = useSelector(state => state.papers);\n  const {\n    charts\n  } = useSelector(state => state.tools);\n  const {\n    searchResults\n  } = useSelector(state => state.tools);\n  const renderContent = () => {\n    switch (currentView) {\n      case 'editor':\n        return currentPaper ? /*#__PURE__*/_jsxDEV(PaperEditor, {\n          paper: currentPaper\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-paper-selected\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Paper Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Select a paper from the context panel or create a new one to start editing.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this);\n      case 'charts':\n        return charts.length > 0 ? /*#__PURE__*/_jsxDEV(ChartViewer, {\n          charts: charts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-charts\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Charts Generated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ask the AI to create visualizations from your research data.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this);\n      case 'papers':\n        return searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(SearchResults, {\n          results: searchResults\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-search-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Search Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Use the chat to search for research papers and view results here.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this);\n      case 'chat':\n      default:\n        return /*#__PURE__*/_jsxDEV(WelcomeScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"main-work-area\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"work-area-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"view-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'chat' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Welcome\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'editor' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Paper Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'charts' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: [\"Charts (\", charts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'papers' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"work-area-content\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(MainWorkArea, \"LmjNd2I8oKMMN1h3BnAYdYAHmJo=\", false, function () {\n  return [useSelector, useSelector, useSelector, useSelector];\n});\n_c = MainWorkArea;\nexport default MainWorkArea;\nvar _c;\n$RefreshReg$(_c, \"MainWorkArea\");", "map": {"version": 3, "names": ["React", "useSelector", "PaperEditor", "ChartViewer", "SearchResults", "WelcomeScreen", "jsxDEV", "_jsxDEV", "MainWorkArea", "_s", "current<PERSON>iew", "state", "ui", "currentPaper", "papers", "charts", "tools", "searchResults", "renderContent", "paper", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "length", "results", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { setCurrentView } from '../../store/slices/uiSlice';\nimport PaperEditor from '../Editor/PaperEditor';\nimport ChartViewer from '../Charts/ChartViewer';\nimport SearchResults from '../Search/SearchResults';\nimport WelcomeScreen from './WelcomeScreen';\nimport './MainWorkArea.css';\n\nconst MainWorkArea: React.FC = () => {\n  const { currentView } = useSelector((state: RootState) => state.ui);\n  const { currentPaper } = useSelector((state: RootState) => state.papers);\n  const { charts } = useSelector((state: RootState) => state.tools);\n  const { searchResults } = useSelector((state: RootState) => state.tools);\n\n  const renderContent = () => {\n    switch (currentView) {\n      case 'editor':\n        return currentPaper ? (\n          <PaperEditor paper={currentPaper} />\n        ) : (\n          <div className=\"no-paper-selected\">\n            <h3>No Paper Selected</h3>\n            <p>Select a paper from the context panel or create a new one to start editing.</p>\n          </div>\n        );\n\n      case 'charts':\n        return charts.length > 0 ? (\n          <ChartViewer charts={charts} />\n        ) : (\n          <div className=\"no-charts\">\n            <h3>No Charts Generated</h3>\n            <p>Ask the AI to create visualizations from your research data.</p>\n          </div>\n        );\n\n      case 'papers':\n        return searchResults.length > 0 ? (\n          <SearchResults results={searchResults} />\n        ) : (\n          <div className=\"no-search-results\">\n            <h3>No Search Results</h3>\n            <p>Use the chat to search for research papers and view results here.</p>\n          </div>\n        );\n\n      case 'chat':\n      default:\n        return <WelcomeScreen />;\n    }\n  };\n\n  return (\n    <div className=\"main-work-area\">\n      <div className=\"work-area-header\">\n        <div className=\"view-tabs\">\n          <button\n            className={`tab ${currentView === 'chat' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Welcome\n          </button>\n          <button\n            className={`tab ${currentView === 'editor' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Paper Editor\n          </button>\n          <button\n            className={`tab ${currentView === 'charts' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Charts ({charts.length})\n          </button>\n          <button\n            className={`tab ${currentView === 'papers' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Search Results\n          </button>\n        </div>\n      </div>\n\n      <div className=\"work-area-content\">\n        {renderContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default MainWorkArea;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAsBC,WAAW,QAAQ,aAAa;AAGtD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAY,CAAC,GAAGT,WAAW,CAAEU,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EACnE,MAAM;IAAEC;EAAa,CAAC,GAAGZ,WAAW,CAAEU,KAAgB,IAAKA,KAAK,CAACG,MAAM,CAAC;EACxE,MAAM;IAAEC;EAAO,CAAC,GAAGd,WAAW,CAAEU,KAAgB,IAAKA,KAAK,CAACK,KAAK,CAAC;EACjE,MAAM;IAAEC;EAAc,CAAC,GAAGhB,WAAW,CAAEU,KAAgB,IAAKA,KAAK,CAACK,KAAK,CAAC;EAExE,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQR,WAAW;MACjB,KAAK,QAAQ;QACX,OAAOG,YAAY,gBACjBN,OAAA,CAACL,WAAW;UAACiB,KAAK,EAAEN;QAAa;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpChB,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClB,OAAA;YAAAkB,QAAA,EAAI;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BhB,OAAA;YAAAkB,QAAA,EAAG;UAA2E;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CACN;MAEH,KAAK,QAAQ;QACX,OAAOR,MAAM,CAACW,MAAM,GAAG,CAAC,gBACtBnB,OAAA,CAACJ,WAAW;UAACY,MAAM,EAAEA;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/BhB,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlB,OAAA;YAAAkB,QAAA,EAAI;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BhB,OAAA;YAAAkB,QAAA,EAAG;UAA4D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;MAEH,KAAK,QAAQ;QACX,OAAON,aAAa,CAACS,MAAM,GAAG,CAAC,gBAC7BnB,OAAA,CAACH,aAAa;UAACuB,OAAO,EAAEV;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzChB,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClB,OAAA;YAAAkB,QAAA,EAAI;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BhB,OAAA;YAAAkB,QAAA,EAAG;UAAiE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CACN;MAEH,KAAK,MAAM;MACX;QACE,oBAAOhB,OAAA,CAACF,aAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5B;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BlB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BlB,OAAA;QAAKiB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBlB,OAAA;UACEiB,SAAS,EAAE,OAAOd,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3DkB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThB,OAAA;UACEiB,SAAS,EAAE,OAAOd,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DkB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThB,OAAA;UACEiB,SAAS,EAAE,OAAOd,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DkB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,GACxD,UACS,EAACV,MAAM,CAACW,MAAM,EAAC,GACzB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThB,OAAA;UACEiB,SAAS,EAAE,OAAOd,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DkB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKiB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/BP,aAAa,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAhFID,YAAsB;EAAA,QACFP,WAAW,EACVA,WAAW,EACjBA,WAAW,EACJA,WAAW;AAAA;AAAA4B,EAAA,GAJjCrB,YAAsB;AAkF5B,eAAeA,YAAY;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}