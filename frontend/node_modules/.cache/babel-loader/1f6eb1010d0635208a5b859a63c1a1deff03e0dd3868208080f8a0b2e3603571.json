{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BarChart3, TrendingUp, PieChart, Circle, Download, ExternalLink } from 'lucide-react';\nimport './ChartViewer.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChartViewer = ({\n  charts\n}) => {\n  _s();\n  const [selectedChart, setSelectedChart] = useState(charts[0] || null);\n  const getChartIcon = type => {\n    switch (type) {\n      case 'line':\n        return /*#__PURE__*/_jsxDEV(TrendingUp, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 16\n        }, this);\n      case 'bar':\n        return /*#__PURE__*/_jsxDEV(BarChart3, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 16\n        }, this);\n      case 'pie':\n        return /*#__PURE__*/_jsxDEV(PieChart, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 16\n        }, this);\n      case 'doughnut':\n        return /*#__PURE__*/_jsxDEV(Circle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(BarChart3, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleDownload = chart => {\n    const link = document.createElement('a');\n    link.href = chart.url;\n    link.download = `${chart.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;\n    link.click();\n  };\n  const handleOpenInNewTab = chart => {\n    window.open(chart.url, '_blank');\n  };\n  if (charts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-viewer-empty\",\n      children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n        size: 64,\n        className: \"empty-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No Charts Available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Ask the AI to create visualizations from your research data.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chart-viewer\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"sidebar-title\",\n        children: [\"Charts (\", charts.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chart-list\",\n        children: charts.map(chart => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `chart-list-item ${(selectedChart === null || selectedChart === void 0 ? void 0 : selectedChart.id) === chart.id ? 'active' : ''}`,\n          onClick: () => setSelectedChart(chart),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-list-icon\",\n            children: getChartIcon(chart.type)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-list-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chart-list-title\",\n              children: chart.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chart-list-type\",\n              children: [chart.type, \" chart\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, chart.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-main\",\n      children: selectedChart ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"chart-title\",\n              children: selectedChart.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chart-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"chart-type\",\n                children: [getChartIcon(selectedChart.type), selectedChart.type, \" chart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"chart-date\",\n                children: [\"Created \", new Date(selectedChart.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleOpenInNewTab(selectedChart),\n              className: \"chart-action-button\",\n              title: \"Open in new tab\",\n              children: [/*#__PURE__*/_jsxDEV(ExternalLink, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this), \"Open\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDownload(selectedChart),\n              className: \"chart-action-button\",\n              title: \"Download chart\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), \"Download\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedChart.url,\n            alt: selectedChart.title,\n            className: \"chart-image\",\n            onError: e => {\n              var _target$nextElementSi;\n              const target = e.target;\n              target.style.display = 'none';\n              (_target$nextElementSi = target.nextElementSibling) === null || _target$nextElementSi === void 0 ? void 0 : _target$nextElementSi.classList.remove('hidden');\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"chart-error hidden\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              size: 48\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Failed to Load Chart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"The chart image could not be loaded.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleOpenInNewTab(selectedChart),\n              className: \"retry-button\",\n              children: \"Try Opening in New Tab\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), selectedChart.data && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-data\",\n          children: /*#__PURE__*/_jsxDEV(\"details\", {\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              children: \"Chart Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"data-preview\",\n              children: JSON.stringify(selectedChart.data, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-chart-selected\",\n        children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n          size: 64,\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Select a Chart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Choose a chart from the sidebar to view it here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(ChartViewer, \"dSOAxtE9HGO3dWryq2xt4vo5A1Q=\");\n_c = ChartViewer;\nexport default ChartViewer;\nvar _c;\n$RefreshReg$(_c, \"ChartViewer\");", "map": {"version": 3, "names": ["React", "useState", "BarChart3", "TrendingUp", "<PERSON><PERSON><PERSON>", "Circle", "Download", "ExternalLink", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChartViewer", "charts", "_s", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "getChartIcon", "type", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleDownload", "chart", "link", "document", "createElement", "href", "url", "download", "title", "replace", "toLowerCase", "click", "handleOpenInNewTab", "window", "open", "length", "className", "children", "map", "id", "onClick", "Date", "created_at", "toLocaleDateString", "src", "alt", "onError", "e", "_target$nextElementSi", "target", "style", "display", "nextElement<PERSON><PERSON>ling", "classList", "remove", "data", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Chart } from '../../store/slices/toolsSlice';\nimport { BarChart3, TrendingUp, <PERSON><PERSON><PERSON>, Circle, Download, ExternalLink } from 'lucide-react';\nimport './ChartViewer.css';\n\ninterface ChartViewerProps {\n  charts: Chart[];\n}\n\nconst ChartViewer: React.FC<ChartViewerProps> = ({ charts }) => {\n  const [selectedChart, setSelectedChart] = useState<Chart | null>(charts[0] || null);\n\n  const getChartIcon = (type: string) => {\n    switch (type) {\n      case 'line':\n        return <TrendingUp size={20} />;\n      case 'bar':\n        return <BarChart3 size={20} />;\n      case 'pie':\n        return <PieChart size={20} />;\n      case 'doughnut':\n        return <Circle size={20} />;\n      default:\n        return <BarChart3 size={20} />;\n    }\n  };\n\n  const handleDownload = (chart: Chart) => {\n    const link = document.createElement('a');\n    link.href = chart.url;\n    link.download = `${chart.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.png`;\n    link.click();\n  };\n\n  const handleOpenInNewTab = (chart: Chart) => {\n    window.open(chart.url, '_blank');\n  };\n\n  if (charts.length === 0) {\n    return (\n      <div className=\"chart-viewer-empty\">\n        <BarChart3 size={64} className=\"empty-icon\" />\n        <h3>No Charts Available</h3>\n        <p>Ask the AI to create visualizations from your research data.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"chart-viewer\">\n      <div className=\"chart-sidebar\">\n        <h3 className=\"sidebar-title\">Charts ({charts.length})</h3>\n        <div className=\"chart-list\">\n          {charts.map((chart) => (\n            <div\n              key={chart.id}\n              className={`chart-list-item ${selectedChart?.id === chart.id ? 'active' : ''}`}\n              onClick={() => setSelectedChart(chart)}\n            >\n              <div className=\"chart-list-icon\">\n                {getChartIcon(chart.type)}\n              </div>\n              <div className=\"chart-list-content\">\n                <div className=\"chart-list-title\">{chart.title}</div>\n                <div className=\"chart-list-type\">{chart.type} chart</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"chart-main\">\n        {selectedChart ? (\n          <>\n            <div className=\"chart-header\">\n              <div className=\"chart-info\">\n                <h2 className=\"chart-title\">{selectedChart.title}</h2>\n                <div className=\"chart-meta\">\n                  <span className=\"chart-type\">\n                    {getChartIcon(selectedChart.type)}\n                    {selectedChart.type} chart\n                  </span>\n                  <span className=\"chart-date\">\n                    Created {new Date(selectedChart.created_at).toLocaleDateString()}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"chart-actions\">\n                <button\n                  onClick={() => handleOpenInNewTab(selectedChart)}\n                  className=\"chart-action-button\"\n                  title=\"Open in new tab\"\n                >\n                  <ExternalLink size={16} />\n                  Open\n                </button>\n                <button\n                  onClick={() => handleDownload(selectedChart)}\n                  className=\"chart-action-button\"\n                  title=\"Download chart\"\n                >\n                  <Download size={16} />\n                  Download\n                </button>\n              </div>\n            </div>\n\n            <div className=\"chart-display\">\n              <img\n                src={selectedChart.url}\n                alt={selectedChart.title}\n                className=\"chart-image\"\n                onError={(e) => {\n                  const target = e.target as HTMLImageElement;\n                  target.style.display = 'none';\n                  target.nextElementSibling?.classList.remove('hidden');\n                }}\n              />\n              <div className=\"chart-error hidden\">\n                <BarChart3 size={48} />\n                <h4>Failed to Load Chart</h4>\n                <p>The chart image could not be loaded.</p>\n                <button\n                  onClick={() => handleOpenInNewTab(selectedChart)}\n                  className=\"retry-button\"\n                >\n                  Try Opening in New Tab\n                </button>\n              </div>\n            </div>\n\n            {selectedChart.data && (\n              <div className=\"chart-data\">\n                <details>\n                  <summary>Chart Data</summary>\n                  <pre className=\"data-preview\">\n                    {JSON.stringify(selectedChart.data, null, 2)}\n                  </pre>\n                </details>\n              </div>\n            )}\n          </>\n        ) : (\n          <div className=\"no-chart-selected\">\n            <BarChart3 size={64} className=\"empty-icon\" />\n            <h3>Select a Chart</h3>\n            <p>Choose a chart from the sidebar to view it here.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ChartViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,SAASC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,cAAc;AAC9F,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAeY,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;EAEnF,MAAMI,YAAY,GAAIC,IAAY,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,MAAM;QACT,oBAAOT,OAAA,CAACN,UAAU;UAACgB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,KAAK;QACR,oBAAOd,OAAA,CAACP,SAAS;UAACiB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChC,KAAK,KAAK;QACR,oBAAOd,OAAA,CAACL,QAAQ;UAACe,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,UAAU;QACb,oBAAOd,OAAA,CAACJ,MAAM;UAACc,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B;QACE,oBAAOd,OAAA,CAACP,SAAS;UAACiB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClC;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,KAAY,IAAK;IACvC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGJ,KAAK,CAACK,GAAG;IACrBJ,IAAI,CAACK,QAAQ,GAAG,GAAGN,KAAK,CAACO,KAAK,CAACC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,MAAM;IAC9ER,IAAI,CAACS,KAAK,CAAC,CAAC;EACd,CAAC;EAED,MAAMC,kBAAkB,GAAIX,KAAY,IAAK;IAC3CY,MAAM,CAACC,IAAI,CAACb,KAAK,CAACK,GAAG,EAAE,QAAQ,CAAC;EAClC,CAAC;EAED,IAAIjB,MAAM,CAAC0B,MAAM,KAAK,CAAC,EAAE;IACvB,oBACE9B,OAAA;MAAK+B,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjChC,OAAA,CAACP,SAAS;QAACiB,IAAI,EAAE,EAAG;QAACqB,SAAS,EAAC;MAAY;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Cd,OAAA;QAAAgC,QAAA,EAAI;MAAmB;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5Bd,OAAA;QAAAgC,QAAA,EAAG;MAA4D;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAEV;EAEA,oBACEd,OAAA;IAAK+B,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BhC,OAAA;MAAK+B,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BhC,OAAA;QAAI+B,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,UAAQ,EAAC5B,MAAM,CAAC0B,MAAM,EAAC,GAAC;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3Dd,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxB5B,MAAM,CAAC6B,GAAG,CAAEjB,KAAK,iBAChBhB,OAAA;UAEE+B,SAAS,EAAE,mBAAmB,CAAAzB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4B,EAAE,MAAKlB,KAAK,CAACkB,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC/EC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACS,KAAK,CAAE;UAAAgB,QAAA,gBAEvChC,OAAA;YAAK+B,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7BxB,YAAY,CAACQ,KAAK,CAACP,IAAI;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNd,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChC,OAAA;cAAK+B,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAEhB,KAAK,CAACO;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrDd,OAAA;cAAK+B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAEhB,KAAK,CAACP,IAAI,EAAC,QAAM;YAAA;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA,GAVDE,KAAK,CAACkB,EAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENd,OAAA;MAAK+B,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxB1B,aAAa,gBACZN,OAAA,CAAAE,SAAA;QAAA8B,QAAA,gBACEhC,OAAA;UAAK+B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BhC,OAAA;YAAK+B,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBhC,OAAA;cAAI+B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE1B,aAAa,CAACiB;YAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDd,OAAA;cAAK+B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBhC,OAAA;gBAAM+B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GACzBxB,YAAY,CAACF,aAAa,CAACG,IAAI,CAAC,EAChCH,aAAa,CAACG,IAAI,EAAC,QACtB;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPd,OAAA;gBAAM+B,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,UACnB,EAAC,IAAII,IAAI,CAAC9B,aAAa,CAAC+B,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENd,OAAA;YAAK+B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BhC,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMR,kBAAkB,CAACrB,aAAa,CAAE;cACjDyB,SAAS,EAAC,qBAAqB;cAC/BR,KAAK,EAAC,iBAAiB;cAAAS,QAAA,gBAEvBhC,OAAA,CAACF,YAAY;gBAACY,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAE5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTd,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACT,aAAa,CAAE;cAC7CyB,SAAS,EAAC,qBAAqB;cAC/BR,KAAK,EAAC,gBAAgB;cAAAS,QAAA,gBAEtBhC,OAAA,CAACH,QAAQ;gBAACa,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAK+B,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BhC,OAAA;YACEuC,GAAG,EAAEjC,aAAa,CAACe,GAAI;YACvBmB,GAAG,EAAElC,aAAa,CAACiB,KAAM;YACzBQ,SAAS,EAAC,aAAa;YACvBU,OAAO,EAAGC,CAAC,IAAK;cAAA,IAAAC,qBAAA;cACd,MAAMC,MAAM,GAAGF,CAAC,CAACE,MAA0B;cAC3CA,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;cAC7B,CAAAH,qBAAA,GAAAC,MAAM,CAACG,kBAAkB,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BK,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;YACvD;UAAE;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFd,OAAA;YAAK+B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjChC,OAAA,CAACP,SAAS;cAACiB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBd,OAAA;cAAAgC,QAAA,EAAI;YAAoB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7Bd,OAAA;cAAAgC,QAAA,EAAG;YAAoC;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC3Cd,OAAA;cACEmC,OAAO,EAAEA,CAAA,KAAMR,kBAAkB,CAACrB,aAAa,CAAE;cACjDyB,SAAS,EAAC,cAAc;cAAAC,QAAA,EACzB;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELR,aAAa,CAAC4C,IAAI,iBACjBlD,OAAA;UAAK+B,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBhC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAAgC,QAAA,EAAS;YAAU;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC7Bd,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BmB,IAAI,CAACC,SAAS,CAAC9C,aAAa,CAAC4C,IAAI,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CACN;MAAA,eACD,CAAC,gBAEHd,OAAA;QAAK+B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChChC,OAAA,CAACP,SAAS;UAACiB,IAAI,EAAE,EAAG;UAACqB,SAAS,EAAC;QAAY;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9Cd,OAAA;UAAAgC,QAAA,EAAI;QAAc;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBd,OAAA;UAAAgC,QAAA,EAAG;QAAgD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CAhJIF,WAAuC;AAAAkD,EAAA,GAAvClD,WAAuC;AAkJ7C,eAAeA,WAAW;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}