{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { updateSessionActivity, setConnected, setConnectionError } from '../store/slices/sessionSlice';\nimport { addNotification } from '../store/slices/uiSlice';\nexport const useRealTimeSync = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    currentSession\n  } = useSelector(state => state.session);\n  const updateActivity = useCallback(() => {\n    if (currentSession) {\n      dispatch(updateSessionActivity());\n    }\n  }, [dispatch, currentSession]);\n  const checkConnection = useCallback(async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      if (response.ok) {\n        dispatch(setConnected(true));\n      } else {\n        throw new Error('Health check failed');\n      }\n    } catch (error) {\n      dispatch(setConnected(false));\n      dispatch(setConnectionError('Connection lost'));\n    }\n  }, [dispatch]);\n\n  // Update activity on user interactions\n  useEffect(() => {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\n    const handleActivity = () => {\n      updateActivity();\n    };\n    events.forEach(event => {\n      document.addEventListener(event, handleActivity, true);\n    });\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleActivity, true);\n      });\n    };\n  }, [updateActivity]);\n\n  // Periodic connection check\n  useEffect(() => {\n    checkConnection(); // Initial check\n\n    const interval = setInterval(checkConnection, 30000); // Check every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [checkConnection]);\n\n  // Handle online/offline events\n  useEffect(() => {\n    const handleOnline = () => {\n      dispatch(setConnected(true));\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Restored',\n        message: 'You are back online',\n        duration: 3000\n      }));\n      checkConnection();\n    };\n    const handleOffline = () => {\n      dispatch(setConnected(false));\n      dispatch(setConnectionError('No internet connection'));\n      dispatch(addNotification({\n        type: 'warning',\n        title: 'Connection Lost',\n        message: 'You are offline. Changes will be saved when connection is restored.',\n        duration: 5000\n      }));\n    };\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, [dispatch, checkConnection]);\n  return {\n    updateActivity,\n    checkConnection\n  };\n};\n_s(useRealTimeSync, \"Uk8r1KlWwU4LJ4RGHs2M9LvghS4=\", false, function () {\n  return [useDispatch, useSelector];\n});\nexport default useRealTimeSync;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useDispatch", "useSelector", "updateSessionActivity", "setConnected", "setConnectionError", "addNotification", "useRealTimeSync", "_s", "dispatch", "currentSession", "state", "session", "updateActivity", "checkConnection", "response", "fetch", "ok", "Error", "error", "events", "handleActivity", "for<PERSON>ach", "event", "document", "addEventListener", "removeEventListener", "interval", "setInterval", "clearInterval", "handleOnline", "type", "title", "message", "duration", "handleOffline", "window"], "sources": ["/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts"], "sourcesContent": ["import { useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../store/store';\nimport { updateSessionActivity, setConnected, setConnectionError } from '../store/slices/sessionSlice';\nimport { addNotification } from '../store/slices/uiSlice';\n\nexport const useRealTimeSync = () => {\n  const dispatch = useDispatch();\n  const { currentSession } = useSelector((state: RootState) => state.session);\n\n  const updateActivity = useCallback(() => {\n    if (currentSession) {\n      dispatch(updateSessionActivity());\n    }\n  }, [dispatch, currentSession]);\n\n  const checkConnection = useCallback(async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/health');\n      if (response.ok) {\n        dispatch(setConnected(true));\n      } else {\n        throw new Error('Health check failed');\n      }\n    } catch (error) {\n      dispatch(setConnected(false));\n      dispatch(setConnectionError('Connection lost'));\n    }\n  }, [dispatch]);\n\n  // Update activity on user interactions\n  useEffect(() => {\n    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];\n    \n    const handleActivity = () => {\n      updateActivity();\n    };\n\n    events.forEach(event => {\n      document.addEventListener(event, handleActivity, true);\n    });\n\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleActivity, true);\n      });\n    };\n  }, [updateActivity]);\n\n  // Periodic connection check\n  useEffect(() => {\n    checkConnection(); // Initial check\n    \n    const interval = setInterval(checkConnection, 30000); // Check every 30 seconds\n    \n    return () => clearInterval(interval);\n  }, [checkConnection]);\n\n  // Handle online/offline events\n  useEffect(() => {\n    const handleOnline = () => {\n      dispatch(setConnected(true));\n      dispatch(addNotification({\n        type: 'success',\n        title: 'Connection Restored',\n        message: 'You are back online',\n        duration: 3000,\n      }));\n      checkConnection();\n    };\n\n    const handleOffline = () => {\n      dispatch(setConnected(false));\n      dispatch(setConnectionError('No internet connection'));\n      dispatch(addNotification({\n        type: 'warning',\n        title: 'Connection Lost',\n        message: 'You are offline. Changes will be saved when connection is restored.',\n        duration: 5000,\n      }));\n    };\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, [dispatch, checkConnection]);\n\n  return {\n    updateActivity,\n    checkConnection,\n  };\n};\n\nexport default useRealTimeSync;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,qBAAqB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,8BAA8B;AACtG,SAASC,eAAe,QAAQ,yBAAyB;AAEzD,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAe,CAAC,GAAGR,WAAW,CAAES,KAAgB,IAAKA,KAAK,CAACC,OAAO,CAAC;EAE3E,MAAMC,cAAc,GAAGb,WAAW,CAAC,MAAM;IACvC,IAAIU,cAAc,EAAE;MAClBD,QAAQ,CAACN,qBAAqB,CAAC,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACM,QAAQ,EAAEC,cAAc,CAAC,CAAC;EAE9B,MAAMI,eAAe,GAAGd,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,CAAC;MACnE,IAAID,QAAQ,CAACE,EAAE,EAAE;QACfR,QAAQ,CAACL,YAAY,CAAC,IAAI,CAAC,CAAC;MAC9B,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAAC,qBAAqB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdV,QAAQ,CAACL,YAAY,CAAC,KAAK,CAAC,CAAC;MAC7BK,QAAQ,CAACJ,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAACI,QAAQ,CAAC,CAAC;;EAEd;EACAV,SAAS,CAAC,MAAM;IACd,MAAMqB,MAAM,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;IAE7E,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3BR,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDO,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;MACtBC,QAAQ,CAACC,gBAAgB,CAACF,KAAK,EAAEF,cAAc,EAAE,IAAI,CAAC;IACxD,CAAC,CAAC;IAEF,OAAO,MAAM;MACXD,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;QACtBC,QAAQ,CAACE,mBAAmB,CAACH,KAAK,EAAEF,cAAc,EAAE,IAAI,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACR,cAAc,CAAC,CAAC;;EAEpB;EACAd,SAAS,CAAC,MAAM;IACde,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEnB,MAAMa,QAAQ,GAAGC,WAAW,CAACd,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEtD,OAAO,MAAMe,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACb,eAAe,CAAC,CAAC;;EAErB;EACAf,SAAS,CAAC,MAAM;IACd,MAAM+B,YAAY,GAAGA,CAAA,KAAM;MACzBrB,QAAQ,CAACL,YAAY,CAAC,IAAI,CAAC,CAAC;MAC5BK,QAAQ,CAACH,eAAe,CAAC;QACvByB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,qBAAqB;QAC5BC,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;MACHpB,eAAe,CAAC,CAAC;IACnB,CAAC;IAED,MAAMqB,aAAa,GAAGA,CAAA,KAAM;MAC1B1B,QAAQ,CAACL,YAAY,CAAC,KAAK,CAAC,CAAC;MAC7BK,QAAQ,CAACJ,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;MACtDI,QAAQ,CAACH,eAAe,CAAC;QACvByB,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,iBAAiB;QACxBC,OAAO,EAAE,qEAAqE;QAC9EC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC;IAEDE,MAAM,CAACX,gBAAgB,CAAC,QAAQ,EAAEK,YAAY,CAAC;IAC/CM,MAAM,CAACX,gBAAgB,CAAC,SAAS,EAAEU,aAAa,CAAC;IAEjD,OAAO,MAAM;MACXC,MAAM,CAACV,mBAAmB,CAAC,QAAQ,EAAEI,YAAY,CAAC;MAClDM,MAAM,CAACV,mBAAmB,CAAC,SAAS,EAAES,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAAC1B,QAAQ,EAAEK,eAAe,CAAC,CAAC;EAE/B,OAAO;IACLD,cAAc;IACdC;EACF,CAAC;AACH,CAAC;AAACN,EAAA,CAzFWD,eAAe;EAAA,QACTN,WAAW,EACDC,WAAW;AAAA;AAyFxC,eAAeK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}