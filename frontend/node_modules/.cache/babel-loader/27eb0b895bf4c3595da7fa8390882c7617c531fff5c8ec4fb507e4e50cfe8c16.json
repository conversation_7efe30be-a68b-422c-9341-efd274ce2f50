{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\n// Load charts from localStorage\nconst loadChartsFromStorage = () => {\n  try {\n    const savedCharts = localStorage.getItem('paper-agent-charts');\n    if (savedCharts) {\n      return JSON.parse(savedCharts);\n    }\n  } catch (error) {\n    console.warn('Failed to load charts from localStorage:', error);\n  }\n  return [];\n};\n\n// Save charts to localStorage\nconst saveChartsToStorage = charts => {\n  try {\n    localStorage.setItem('paper-agent-charts', JSON.stringify(charts));\n  } catch (error) {\n    console.warn('Failed to save charts to localStorage:', error);\n  }\n};\nconst initialState = {\n  executions: [],\n  charts: loadChartsFromStorage(),\n  searchResults: [],\n  isExecuting: false\n};\nconst toolsSlice = createSlice({\n  name: 'tools',\n  initialState,\n  reducers: {\n    startToolExecution: (state, action) => {\n      const execution = {\n        ...action.payload,\n        id: Date.now().toString(),\n        status: 'running',\n        startTime: new Date().toISOString()\n      };\n      state.executions.push(execution);\n      state.isExecuting = true;\n    },\n    updateToolExecution: (state, action) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        Object.assign(execution, action.payload.updates);\n        if (action.payload.updates.status === 'completed' || action.payload.updates.status === 'failed') {\n          execution.endTime = new Date().toISOString();\n        }\n      }\n\n      // Check if any tools are still running\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    completeToolExecution: (state, action) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        execution.status = 'completed';\n        execution.output = action.payload.output;\n        execution.endTime = new Date().toISOString();\n      }\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    failToolExecution: (state, action) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        execution.status = 'failed';\n        execution.error = action.payload.error;\n        execution.endTime = new Date().toISOString();\n      }\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    addChart: (state, action) => {\n      const chart = {\n        ...action.payload,\n        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString()\n      };\n      state.charts.push(chart);\n      saveChartsToStorage(state.charts);\n    },\n    removeChart: (state, action) => {\n      state.charts = state.charts.filter(c => c.id !== action.payload);\n      saveChartsToStorage(state.charts);\n    },\n    addSearchResult: (state, action) => {\n      const searchResult = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString()\n      };\n      state.searchResults.push(searchResult);\n\n      // Keep only the last 10 search results\n      if (state.searchResults.length > 10) {\n        state.searchResults = state.searchResults.slice(-10);\n      }\n    },\n    clearExecutions: state => {\n      state.executions = [];\n      state.isExecuting = false;\n    }\n  }\n});\nexport const {\n  startToolExecution,\n  updateToolExecution,\n  completeToolExecution,\n  failToolExecution,\n  addChart,\n  removeChart,\n  addSearchResult,\n  clearExecutions\n} = toolsSlice.actions;\nexport default toolsSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "loadChartsFromStorage", "<PERSON><PERSON><PERSON><PERSON>", "localStorage", "getItem", "JSON", "parse", "error", "console", "warn", "saveChartsToStorage", "charts", "setItem", "stringify", "initialState", "executions", "searchResults", "isExecuting", "toolsSlice", "name", "reducers", "startToolExecution", "state", "action", "execution", "payload", "id", "Date", "now", "toString", "status", "startTime", "toISOString", "push", "updateToolExecution", "find", "e", "Object", "assign", "updates", "endTime", "some", "completeToolExecution", "output", "failToolExecution", "add<PERSON><PERSON>", "chart", "Math", "random", "substr", "created_at", "<PERSON><PERSON><PERSON>", "filter", "c", "addSearchResult", "searchResult", "timestamp", "length", "slice", "clearExecutions", "actions", "reducer"], "sources": ["/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface ToolExecution {\n  id: string;\n  toolName: string;\n  status: 'pending' | 'running' | 'completed' | 'failed';\n  progress?: number;\n  startTime: string;\n  endTime?: string;\n  input: any;\n  output?: any;\n  error?: string;\n}\n\nexport interface Chart {\n  id: string;\n  type: 'line' | 'bar' | 'pie' | 'polar' | 'doughnut';\n  title: string;\n  url: string;\n  data: any;\n  created_at: string;\n}\n\nexport interface SearchResult {\n  id: string;\n  query: string;\n  results: any[];\n  count: number;\n  timestamp: string;\n  source: 'firecrawl' | 'deep_research';\n}\n\nexport interface ToolsState {\n  executions: ToolExecution[];\n  charts: Chart[];\n  searchResults: SearchResult[];\n  isExecuting: boolean;\n}\n\n// Load charts from localStorage\nconst loadChartsFromStorage = (): Chart[] => {\n  try {\n    const savedCharts = localStorage.getItem('paper-agent-charts');\n    if (savedCharts) {\n      return JSON.parse(savedCharts);\n    }\n  } catch (error) {\n    console.warn('Failed to load charts from localStorage:', error);\n  }\n  return [];\n};\n\n// Save charts to localStorage\nconst saveChartsToStorage = (charts: Chart[]) => {\n  try {\n    localStorage.setItem('paper-agent-charts', JSON.stringify(charts));\n  } catch (error) {\n    console.warn('Failed to save charts to localStorage:', error);\n  }\n};\n\nconst initialState: ToolsState = {\n  executions: [],\n  charts: loadChartsFromStorage(),\n  searchResults: [],\n  isExecuting: false,\n};\n\nconst toolsSlice = createSlice({\n  name: 'tools',\n  initialState,\n  reducers: {\n    startToolExecution: (state, action: PayloadAction<Omit<ToolExecution, 'id' | 'startTime' | 'status'>>) => {\n      const execution: ToolExecution = {\n        ...action.payload,\n        id: Date.now().toString(),\n        status: 'running',\n        startTime: new Date().toISOString(),\n      };\n      state.executions.push(execution);\n      state.isExecuting = true;\n    },\n    updateToolExecution: (state, action: PayloadAction<{ id: string; updates: Partial<ToolExecution> }>) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        Object.assign(execution, action.payload.updates);\n        if (action.payload.updates.status === 'completed' || action.payload.updates.status === 'failed') {\n          execution.endTime = new Date().toISOString();\n        }\n      }\n\n      // Check if any tools are still running\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    completeToolExecution: (state, action: PayloadAction<{ id: string; output: any }>) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        execution.status = 'completed';\n        execution.output = action.payload.output;\n        execution.endTime = new Date().toISOString();\n      }\n\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    failToolExecution: (state, action: PayloadAction<{ id: string; error: string }>) => {\n      const execution = state.executions.find(e => e.id === action.payload.id);\n      if (execution) {\n        execution.status = 'failed';\n        execution.error = action.payload.error;\n        execution.endTime = new Date().toISOString();\n      }\n\n      state.isExecuting = state.executions.some(e => e.status === 'running' || e.status === 'pending');\n    },\n    addChart: (state, action: PayloadAction<Omit<Chart, 'id' | 'created_at'>>) => {\n      const chart: Chart = {\n        ...action.payload,\n        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        created_at: new Date().toISOString(),\n      };\n      state.charts.push(chart);\n      saveChartsToStorage(state.charts);\n    },\n    removeChart: (state, action: PayloadAction<string>) => {\n      state.charts = state.charts.filter(c => c.id !== action.payload);\n      saveChartsToStorage(state.charts);\n    },\n    addSearchResult: (state, action: PayloadAction<Omit<SearchResult, 'id' | 'timestamp'>>) => {\n      const searchResult: SearchResult = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n      };\n      state.searchResults.push(searchResult);\n\n      // Keep only the last 10 search results\n      if (state.searchResults.length > 10) {\n        state.searchResults = state.searchResults.slice(-10);\n      }\n    },\n    clearExecutions: (state) => {\n      state.executions = [];\n      state.isExecuting = false;\n    },\n  },\n});\n\nexport const {\n  startToolExecution,\n  updateToolExecution,\n  completeToolExecution,\n  failToolExecution,\n  addChart,\n  removeChart,\n  addSearchResult,\n  clearExecutions,\n} = toolsSlice.actions;\n\nexport default toolsSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAuC7D;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAe;EAC3C,IAAI;IACF,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IAC9D,IAAIF,WAAW,EAAE;MACf,OAAOG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC;IAChC;EACF,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;EACjE;EACA,OAAO,EAAE;AACX,CAAC;;AAED;AACA,MAAMG,mBAAmB,GAAIC,MAAe,IAAK;EAC/C,IAAI;IACFR,YAAY,CAACS,OAAO,CAAC,oBAAoB,EAAEP,IAAI,CAACQ,SAAS,CAACF,MAAM,CAAC,CAAC;EACpE,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAEF,KAAK,CAAC;EAC/D;AACF,CAAC;AAED,MAAMO,YAAwB,GAAG;EAC/BC,UAAU,EAAE,EAAE;EACdJ,MAAM,EAAEV,qBAAqB,CAAC,CAAC;EAC/Be,aAAa,EAAE,EAAE;EACjBC,WAAW,EAAE;AACf,CAAC;AAED,MAAMC,UAAU,GAAGlB,WAAW,CAAC;EAC7BmB,IAAI,EAAE,OAAO;EACbL,YAAY;EACZM,QAAQ,EAAE;IACRC,kBAAkB,EAAEA,CAACC,KAAK,EAAEC,MAAyE,KAAK;MACxG,MAAMC,SAAwB,GAAG;QAC/B,GAAGD,MAAM,CAACE,OAAO;QACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACpC,CAAC;MACDV,KAAK,CAACP,UAAU,CAACkB,IAAI,CAACT,SAAS,CAAC;MAChCF,KAAK,CAACL,WAAW,GAAG,IAAI;IAC1B,CAAC;IACDiB,mBAAmB,EAAEA,CAACZ,KAAK,EAAEC,MAAsE,KAAK;MACtG,MAAMC,SAAS,GAAGF,KAAK,CAACP,UAAU,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,CAAC;MACxE,IAAIF,SAAS,EAAE;QACba,MAAM,CAACC,MAAM,CAACd,SAAS,EAAED,MAAM,CAACE,OAAO,CAACc,OAAO,CAAC;QAChD,IAAIhB,MAAM,CAACE,OAAO,CAACc,OAAO,CAACT,MAAM,KAAK,WAAW,IAAIP,MAAM,CAACE,OAAO,CAACc,OAAO,CAACT,MAAM,KAAK,QAAQ,EAAE;UAC/FN,SAAS,CAACgB,OAAO,GAAG,IAAIb,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QAC9C;MACF;;MAEA;MACAV,KAAK,CAACL,WAAW,GAAGK,KAAK,CAACP,UAAU,CAAC0B,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACN,MAAM,KAAK,SAAS,IAAIM,CAAC,CAACN,MAAM,KAAK,SAAS,CAAC;IAClG,CAAC;IACDY,qBAAqB,EAAEA,CAACpB,KAAK,EAAEC,MAAkD,KAAK;MACpF,MAAMC,SAAS,GAAGF,KAAK,CAACP,UAAU,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,CAAC;MACxE,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACM,MAAM,GAAG,WAAW;QAC9BN,SAAS,CAACmB,MAAM,GAAGpB,MAAM,CAACE,OAAO,CAACkB,MAAM;QACxCnB,SAAS,CAACgB,OAAO,GAAG,IAAIb,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MAC9C;MAEAV,KAAK,CAACL,WAAW,GAAGK,KAAK,CAACP,UAAU,CAAC0B,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACN,MAAM,KAAK,SAAS,IAAIM,CAAC,CAACN,MAAM,KAAK,SAAS,CAAC;IAClG,CAAC;IACDc,iBAAiB,EAAEA,CAACtB,KAAK,EAAEC,MAAoD,KAAK;MAClF,MAAMC,SAAS,GAAGF,KAAK,CAACP,UAAU,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,EAAE,KAAKH,MAAM,CAACE,OAAO,CAACC,EAAE,CAAC;MACxE,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACM,MAAM,GAAG,QAAQ;QAC3BN,SAAS,CAACjB,KAAK,GAAGgB,MAAM,CAACE,OAAO,CAAClB,KAAK;QACtCiB,SAAS,CAACgB,OAAO,GAAG,IAAIb,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MAC9C;MAEAV,KAAK,CAACL,WAAW,GAAGK,KAAK,CAACP,UAAU,CAAC0B,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACN,MAAM,KAAK,SAAS,IAAIM,CAAC,CAACN,MAAM,KAAK,SAAS,CAAC;IAClG,CAAC;IACDe,QAAQ,EAAEA,CAACvB,KAAK,EAAEC,MAAuD,KAAK;MAC5E,MAAMuB,KAAY,GAAG;QACnB,GAAGvB,MAAM,CAACE,OAAO;QACjBC,EAAE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAImB,IAAI,CAACC,MAAM,CAAC,CAAC,CAACnB,QAAQ,CAAC,EAAE,CAAC,CAACoB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DC,UAAU,EAAE,IAAIvB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACrC,CAAC;MACDV,KAAK,CAACX,MAAM,CAACsB,IAAI,CAACa,KAAK,CAAC;MACxBpC,mBAAmB,CAACY,KAAK,CAACX,MAAM,CAAC;IACnC,CAAC;IACDwC,WAAW,EAAEA,CAAC7B,KAAK,EAAEC,MAA6B,KAAK;MACrDD,KAAK,CAACX,MAAM,GAAGW,KAAK,CAACX,MAAM,CAACyC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3B,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;MAChEf,mBAAmB,CAACY,KAAK,CAACX,MAAM,CAAC;IACnC,CAAC;IACD2C,eAAe,EAAEA,CAAChC,KAAK,EAAEC,MAA6D,KAAK;MACzF,MAAMgC,YAA0B,GAAG;QACjC,GAAGhC,MAAM,CAACE,OAAO;QACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzB2B,SAAS,EAAE,IAAI7B,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACpC,CAAC;MACDV,KAAK,CAACN,aAAa,CAACiB,IAAI,CAACsB,YAAY,CAAC;;MAEtC;MACA,IAAIjC,KAAK,CAACN,aAAa,CAACyC,MAAM,GAAG,EAAE,EAAE;QACnCnC,KAAK,CAACN,aAAa,GAAGM,KAAK,CAACN,aAAa,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC;MACtD;IACF,CAAC;IACDC,eAAe,EAAGrC,KAAK,IAAK;MAC1BA,KAAK,CAACP,UAAU,GAAG,EAAE;MACrBO,KAAK,CAACL,WAAW,GAAG,KAAK;IAC3B;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXI,kBAAkB;EAClBa,mBAAmB;EACnBQ,qBAAqB;EACrBE,iBAAiB;EACjBC,QAAQ;EACRM,WAAW;EACXG,eAAe;EACfK;AACF,CAAC,GAAGzC,UAAU,CAAC0C,OAAO;AAEtB,eAAe1C,UAAU,CAAC2C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}