{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaperEditor = ({\n  paper\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n  const handleContentChange = useCallback(value => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleExport = () => {\n    const blob = new Blob([content], {\n      type: 'text/markdown'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n  const renderPreview = () => {\n    const html = marked.parse(content, {\n      async: false\n    });\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: sanitizedHtml\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 12\n    }, this);\n  };\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `paper-editor ${isFullscreen ? 'fullscreen' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: paper.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"word-count\",\n            children: [getWordCount(), \" words\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-indicator\",\n            children: hasUnsavedChanges ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unsaved\",\n              children: \"\\u25CF Unsaved changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"saved\",\n              children: \"\\u2713 Saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleView,\n          className: \"editor-button\",\n          title: isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode',\n          children: [isSplitView ? /*#__PURE__*/_jsxDEV(Split, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 28\n          }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 66\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 89\n          }, this), isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleFullscreen,\n          className: \"editor-button\",\n          title: isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen',\n          children: [/*#__PURE__*/_jsxDEV(Maximize2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), isFullscreen ? 'Exit' : 'Fullscreen']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: !hasUnsavedChanges || isSaving,\n          className: \"editor-button save-button\",\n          title: \"Save changes (Ctrl+S)\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), isSaving ? 'Saving...' : 'Save']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          className: \"editor-button\",\n          title: \"Export as Markdown\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: isSplitView ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"split-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-pane\",\n          children: /*#__PURE__*/_jsxDEV(Editor, {\n            height: \"100%\",\n            defaultLanguage: \"markdown\",\n            value: content,\n            onChange: handleContentChange,\n            theme: \"vs-light\",\n            options: {\n              minimap: {\n                enabled: false\n              },\n              wordWrap: 'on',\n              lineNumbers: 'on',\n              scrollBeyondLastLine: false,\n              automaticLayout: true,\n              fontSize: 14,\n              fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n              padding: {\n                top: 16,\n                bottom: 16\n              },\n              suggest: {\n                showKeywords: false,\n                showSnippets: false\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-pane\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"markdown-content\",\n            children: renderPreview()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-pane full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-content\",\n          children: renderPreview()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"edit-pane full\",\n        children: /*#__PURE__*/_jsxDEV(Editor, {\n          height: \"100%\",\n          defaultLanguage: \"markdown\",\n          value: content,\n          onChange: handleContentChange,\n          theme: \"vs-light\",\n          options: {\n            minimap: {\n              enabled: true\n            },\n            wordWrap: 'on',\n            lineNumbers: 'on',\n            scrollBeyondLastLine: false,\n            automaticLayout: true,\n            fontSize: 16,\n            fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n            padding: {\n              top: 20,\n              bottom: 20\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Characters: \", content.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Words: \", getWordCount()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Lines: \", content.split('\\n').length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-help\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Markdown supported \\u2022 Ctrl+S to save \\u2022 Ctrl+P to toggle view\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperEditor, \"WAkBjZO5V4jpS43YXiev0kO/Y1E=\", false, function () {\n  return [useDispatch];\n});\n_c = PaperEditor;\nexport default PaperEditor;\nvar _c;\n$RefreshReg$(_c, \"PaperEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "updateCurrentPaperContent", "addNotification", "Save", "Eye", "Eye<PERSON>ff", "Download", "Split", "Maximize2", "marked", "DOMPurify", "Editor", "api", "jsxDEV", "_jsxDEV", "PaperEditor", "paper", "_s", "dispatch", "content", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewMode", "setIsPreviewMode", "isSplitView", "setIsSplitView", "isSaving", "setIsSaving", "hasUnsavedChanges", "setHasUnsavedChanges", "isFullscreen", "setIsFullscreen", "paper_id", "handleContentChange", "value", "newContent", "handleSave", "result", "papers", "update", "title", "data", "type", "message", "duration", "Error", "error", "handleExport", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "toLowerCase", "click", "revokeObjectURL", "renderPreview", "html", "parse", "async", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWordCount", "trim", "split", "filter", "word", "length", "to<PERSON><PERSON><PERSON><PERSON>", "toggleFullscreen", "timer", "setTimeout", "clearTimeout", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "addEventListener", "removeEventListener", "className", "children", "onClick", "size", "disabled", "height", "defaultLanguage", "onChange", "theme", "options", "minimap", "enabled", "wordWrap", "lineNumbers", "scrollBeyondLastLine", "automaticLayout", "fontSize", "fontFamily", "padding", "top", "bottom", "suggest", "showKeywords", "showSnippets", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Paper, updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\n\ninterface PaperEditorProps {\n  paper: Paper;\n}\n\nconst PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  const handleContentChange = useCallback((value: string | undefined) => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n\n\n\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const renderPreview = () => {\n    const html = marked.parse(content, { async: false }) as string;\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n  };\n\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, []);\n\n  return (\n    <div className={`paper-editor ${isFullscreen ? 'fullscreen' : ''}`}>\n      <div className=\"editor-header\">\n        <div className=\"editor-title\">\n          <h2>{paper.title}</h2>\n          <div className=\"editor-meta\">\n            <span className=\"word-count\">{getWordCount()} words</span>\n            <span className=\"status-indicator\">\n              {hasUnsavedChanges ? (\n                <span className=\"unsaved\">● Unsaved changes</span>\n              ) : (\n                <span className=\"saved\">✓ Saved</span>\n              )}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"editor-actions\">\n          <button\n            onClick={toggleView}\n            className=\"editor-button\"\n            title={isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode'}\n          >\n            {isSplitView ? <Split size={16} /> : isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}\n            {isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview'}\n          </button>\n\n          <button\n            onClick={toggleFullscreen}\n            className=\"editor-button\"\n            title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}\n          >\n            <Maximize2 size={16} />\n            {isFullscreen ? 'Exit' : 'Fullscreen'}\n          </button>\n\n          <button\n            onClick={handleSave}\n            disabled={!hasUnsavedChanges || isSaving}\n            className=\"editor-button save-button\"\n            title=\"Save changes (Ctrl+S)\"\n          >\n            <Save size={16} />\n            {isSaving ? 'Saving...' : 'Save'}\n          </button>\n\n          <button\n            onClick={handleExport}\n            className=\"editor-button\"\n            title=\"Export as Markdown\"\n          >\n            <Download size={16} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      <div className=\"editor-content\">\n        {isSplitView ? (\n          <div className=\"split-view\">\n            <div className=\"edit-pane\">\n              <Editor\n                height=\"100%\"\n                defaultLanguage=\"markdown\"\n                value={content}\n                onChange={handleContentChange}\n                theme=\"vs-light\"\n                options={{\n                  minimap: { enabled: false },\n                  wordWrap: 'on',\n                  lineNumbers: 'on',\n                  scrollBeyondLastLine: false,\n                  automaticLayout: true,\n                  fontSize: 14,\n                  fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                  padding: { top: 16, bottom: 16 },\n                  suggest: {\n                    showKeywords: false,\n                    showSnippets: false,\n                  },\n                }}\n              />\n            </div>\n            <div className=\"preview-pane\">\n              <div className=\"markdown-content\">\n                {renderPreview()}\n              </div>\n            </div>\n          </div>\n        ) : isPreviewMode ? (\n          <div className=\"preview-pane full\">\n            <div className=\"markdown-content\">\n              {renderPreview()}\n            </div>\n          </div>\n        ) : (\n          <div className=\"edit-pane full\">\n            <Editor\n              height=\"100%\"\n              defaultLanguage=\"markdown\"\n              value={content}\n              onChange={handleContentChange}\n              theme=\"vs-light\"\n              options={{\n                minimap: { enabled: true },\n                wordWrap: 'on',\n                lineNumbers: 'on',\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n                fontSize: 16,\n                fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                padding: { top: 20, bottom: 20 },\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"editor-footer\">\n        <div className=\"editor-stats\">\n          <span>Characters: {content.length}</span>\n          <span>Words: {getWordCount()}</span>\n          <span>Lines: {content.split('\\n').length}</span>\n        </div>\n\n        <div className=\"editor-help\">\n          <span>Markdown supported • Ctrl+S to save • Ctrl+P to toggle view</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaperEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAAgBC,yBAAyB,QAAQ,gCAAgC;AACjF,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,cAAc;AAC5E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAACmB,KAAK,CAACG,OAAO,CAAC;EACrD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdsB,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC;IACzBS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACZ,KAAK,CAACe,QAAQ,EAAEf,KAAK,CAACG,OAAO,CAAC,CAAC;EAEnCrB,SAAS,CAAC,MAAM;IACd8B,oBAAoB,CAACT,OAAO,KAAKH,KAAK,CAACG,OAAO,CAAC;EACjD,CAAC,EAAE,CAACA,OAAO,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC;EAE5B,MAAMa,mBAAmB,GAAGjC,WAAW,CAAEkC,KAAyB,IAAK;IACrE,MAAMC,UAAU,GAAGD,KAAK,IAAI,EAAE;IAC9Bb,UAAU,CAACc,UAAU,CAAC;IACtBhB,QAAQ,CAACjB,yBAAyB,CAACiC,UAAU,CAAC,CAAC;EACjD,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAId,MAAMiB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACR,iBAAiB,EAAE;IAExBD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMU,MAAM,GAAG,MAAMxB,GAAG,CAACyB,MAAM,CAACC,MAAM,CAACtB,KAAK,CAACe,QAAQ,EAAEf,KAAK,CAACuB,KAAK,EAAEpB,OAAO,CAAC;MAE5E,IAAIiB,MAAM,CAACI,IAAI,EAAE;QACfZ,oBAAoB,CAAC,KAAK,CAAC;QAC3BV,QAAQ,CAAChB,eAAe,CAAC;UACvBuC,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,aAAa;UACpBG,OAAO,EAAE,4CAA4C;UACrDC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,MAAM,CAACS,KAAK,IAAI,gBAAgB,CAAC;MACnD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd3B,QAAQ,CAAChB,eAAe,CAAC;QACvBuC,IAAI,EAAE,OAAO;QACbF,KAAK,EAAE,aAAa;QACpBG,OAAO,EAAE,gDAAgD;QACzDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRjB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC7B,OAAO,CAAC,EAAE;MAAEsB,IAAI,EAAE;IAAgB,CAAC,CAAC;IAC3D,MAAMQ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAGxC,KAAK,CAACuB,KAAK,CAACkB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK;IAC7EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZT,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGrD,MAAM,CAACsD,KAAK,CAAC5C,OAAO,EAAE;MAAE6C,KAAK,EAAE;IAAM,CAAC,CAAW;IAC9D,MAAMC,aAAa,GAAGvD,SAAS,CAACwD,QAAQ,CAACJ,IAAI,CAAC;IAC9C,oBAAOhD,OAAA;MAAKqD,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAc;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAOtD,OAAO,CAACuD,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC3E,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxD,WAAW,EAAE;MACfD,gBAAgB,CAAC,CAACD,aAAa,CAAC;MAChCG,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACLA,cAAc,CAAC,IAAI,CAAC;MACpBF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM0D,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlD,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI6B,iBAAiB,EAAE;MACrB,MAAMsD,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B/C,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMgD,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAAC9D,OAAO,EAAEQ,iBAAiB,EAAEQ,UAAU,CAAC,CAAC;;EAE5C;EACArC,SAAS,CAAC,MAAM;IACd,MAAMsF,aAAa,GAAIC,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBtD,UAAU,CAAC,CAAC;MACd;MACA,IAAI,CAACkD,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBV,UAAU,CAAC,CAAC;MACd;IACF,CAAC;IAED1B,QAAQ,CAACqC,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACnD,OAAO,MAAM/B,QAAQ,CAACsC,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtE,OAAA;IAAK8E,SAAS,EAAE,gBAAgB/D,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAAgE,QAAA,gBACjE/E,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/E,OAAA;QAAK8E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/E,OAAA;UAAA+E,QAAA,EAAK7E,KAAK,CAACuB;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtB1D,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/E,OAAA;YAAM8E,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEpB,YAAY,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1D1D,OAAA;YAAM8E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/BlE,iBAAiB,gBAChBb,OAAA;cAAM8E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAElD1D,OAAA;cAAM8E,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACtC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1D,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UACEgF,OAAO,EAAEf,UAAW;UACpBa,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAEhB,WAAW,GAAG,oBAAoB,GAAGF,aAAa,GAAG,WAAW,GAAG,cAAe;UAAAwE,QAAA,GAExFtE,WAAW,gBAAGT,OAAA,CAACP,KAAK;YAACwF,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAGnD,aAAa,gBAAGP,OAAA,CAACT,MAAM;YAAC0F,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACV,GAAG;YAAC2F,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC5FjD,WAAW,GAAG,OAAO,GAAGF,aAAa,GAAG,MAAM,GAAG,SAAS;QAAA;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAET1D,OAAA;UACEgF,OAAO,EAAEd,gBAAiB;UAC1BY,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAEV,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;UAAAgE,QAAA,gBAE7D/E,OAAA,CAACN,SAAS;YAACuF,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtB3C,YAAY,GAAG,MAAM,GAAG,YAAY;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAET1D,OAAA;UACEgF,OAAO,EAAE3D,UAAW;UACpB6D,QAAQ,EAAE,CAACrE,iBAAiB,IAAIF,QAAS;UACzCmE,SAAS,EAAC,2BAA2B;UACrCrD,KAAK,EAAC,uBAAuB;UAAAsD,QAAA,gBAE7B/E,OAAA,CAACX,IAAI;YAAC4F,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjB/C,QAAQ,GAAG,WAAW,GAAG,MAAM;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAET1D,OAAA;UACEgF,OAAO,EAAEhD,YAAa;UACtB8C,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAC,oBAAoB;UAAAsD,QAAA,gBAE1B/E,OAAA,CAACR,QAAQ;YAACyF,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1D,OAAA;MAAK8E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BtE,WAAW,gBACVT,OAAA;QAAK8E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB/E,OAAA;UAAK8E,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB/E,OAAA,CAACH,MAAM;YACLsF,MAAM,EAAC,MAAM;YACbC,eAAe,EAAC,UAAU;YAC1BjE,KAAK,EAAEd,OAAQ;YACfgF,QAAQ,EAAEnE,mBAAoB;YAC9BoE,KAAK,EAAC,UAAU;YAChBC,OAAO,EAAE;cACPC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAC;cAC3BC,QAAQ,EAAE,IAAI;cACdC,WAAW,EAAE,IAAI;cACjBC,oBAAoB,EAAE,KAAK;cAC3BC,eAAe,EAAE,IAAI;cACrBC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,yCAAyC;cACrDC,OAAO,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAG,CAAC;cAChCC,OAAO,EAAE;gBACPC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB;YACF;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN1D,OAAA;UAAK8E,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B/E,OAAA;YAAK8E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BhC,aAAa,CAAC;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJnD,aAAa,gBACfP,OAAA;QAAK8E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChC/E,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BhC,aAAa,CAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN1D,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B/E,OAAA,CAACH,MAAM;UACLsF,MAAM,EAAC,MAAM;UACbC,eAAe,EAAC,UAAU;UAC1BjE,KAAK,EAAEd,OAAQ;UACfgF,QAAQ,EAAEnE,mBAAoB;UAC9BoE,KAAK,EAAC,UAAU;UAChBC,OAAO,EAAE;YACPC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAC;YAC1BC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE,IAAI;YACjBC,oBAAoB,EAAE,KAAK;YAC3BC,eAAe,EAAE,IAAI;YACrBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,yCAAyC;YACrDC,OAAO,EAAE;cAAEC,GAAG,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG;UACjC;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN1D,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B/E,OAAA;QAAK8E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B/E,OAAA;UAAA+E,QAAA,GAAM,cAAY,EAAC1E,OAAO,CAAC2D,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzC1D,OAAA;UAAA+E,QAAA,GAAM,SAAO,EAACpB,YAAY,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpC1D,OAAA;UAAA+E,QAAA,GAAM,SAAO,EAAC1E,OAAO,CAACwD,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEN1D,OAAA;QAAK8E,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B/E,OAAA;UAAA+E,QAAA,EAAM;QAA2D;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CAzPIF,WAAuC;EAAA,QAC1Bf,WAAW;AAAA;AAAAoH,EAAA,GADxBrG,WAAuC;AA2P7C,eAAeA,WAAW;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}