{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 21h1\",\n  key: \"v9vybs\"\n}], [\"path\", {\n  d: \"M21 14v1\",\n  key: \"169vum\"\n}], [\"path\", {\n  d: \"M21 19a2 2 0 0 1-2 2\",\n  key: \"1j7049\"\n}], [\"path\", {\n  d: \"M21 9v1\",\n  key: \"mxsmne\"\n}], [\"path\", {\n  d: \"M3 14v1\",\n  key: \"vnatye\"\n}], [\"path\", {\n  d: \"M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2\",\n  key: \"89voep\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}], [\"path\", {\n  d: \"M5 21a2 2 0 0 1-2-2\",\n  key: \"sbafld\"\n}], [\"path\", {\n  d: \"M9 21h1\",\n  key: \"15o7lz\"\n}]];\nconst SquareDashedTopSolid = createLucideIcon(\"square-dashed-top-solid\", __iconNode);\nexport { __iconNode, SquareDashedTopSolid as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SquareDashedTopSolid", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/square-dashed-top-solid.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 21h1', key: 'v9vybs' }],\n  ['path', { d: 'M21 14v1', key: '169vum' }],\n  ['path', { d: 'M21 19a2 2 0 0 1-2 2', key: '1j7049' }],\n  ['path', { d: 'M21 9v1', key: 'mxsmne' }],\n  ['path', { d: 'M3 14v1', key: 'vnatye' }],\n  ['path', { d: 'M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2', key: '89voep' }],\n  ['path', { d: 'M3 9v1', key: '1r0deq' }],\n  ['path', { d: 'M5 21a2 2 0 0 1-2-2', key: 'sbafld' }],\n  ['path', { d: 'M9 21h1', key: '15o7lz' }],\n];\n\n/**\n * @component @name SquareDashedTopSolid\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMjFoMSIgLz4KICA8cGF0aCBkPSJNMjEgMTR2MSIgLz4KICA8cGF0aCBkPSJNMjEgMTlhMiAyIDAgMCAxLTIgMiIgLz4KICA8cGF0aCBkPSJNMjEgOXYxIiAvPgogIDxwYXRoIGQ9Ik0zIDE0djEiIC8+CiAgPHBhdGggZD0iTTMgNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDIiIC8+CiAgPHBhdGggZD0iTTMgOXYxIiAvPgogIDxwYXRoIGQ9Ik01IDIxYTIgMiAwIDAgMS0yLTIiIC8+CiAgPHBhdGggZD0iTTkgMjFoMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/square-dashed-top-solid\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquareDashedTopSolid = createLucideIcon('square-dashed-top-solid', __iconNode);\n\nexport default SquareDashedTopSolid;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,oBAAA,GAAuBC,gBAAiB,4BAA2BJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}