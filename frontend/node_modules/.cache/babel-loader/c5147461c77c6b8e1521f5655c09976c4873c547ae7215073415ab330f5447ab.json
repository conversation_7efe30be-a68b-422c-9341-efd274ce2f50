{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toggleSidebar, toggleRightPanel, setTheme } from '../../store/slices/uiSlice';\nimport { FileText, Settings, Download, Sun, Moon, Menu, PanelLeftClose, PanelRightClose, Wifi, WifiOff } from 'lucide-react';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    theme,\n    sidebarCollapsed,\n    rightPanelCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentSession,\n    isConnected\n  } = useSelector(state => state.session);\n  const {\n    isStreaming\n  } = useSelector(state => state.chat);\n  const handleToggleSidebar = () => {\n    dispatch(toggleSidebar());\n  };\n  const handleToggleRightPanel = () => {\n    dispatch(toggleRightPanel());\n  };\n  const handleToggleTheme = () => {\n    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));\n  };\n  const handleExport = () => {\n    // TODO: Implement export functionality\n    console.log('Export functionality to be implemented');\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `header ${theme}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-button\",\n        onClick: handleToggleSidebar,\n        title: sidebarCollapsed ? 'Show chat panel' : 'Hide chat panel',\n        children: sidebarCollapsed ? /*#__PURE__*/_jsxDEV(Menu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(PanelLeftClose, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 52\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-logo\",\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          size: 24,\n          className: \"logo-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"logo-text\",\n          children: \"Paper Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-center\",\n      children: currentSession && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"session-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"session-status\",\n          children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n            size: 16,\n            className: \"status-icon connected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n            size: 16,\n            className: \"status-icon disconnected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"session-id\",\n            children: [\"Session: \", currentSession.session_id.slice(0, 8), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), isStreaming && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"streaming-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"streaming-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"AI is thinking...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-right\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-button\",\n        onClick: handleToggleTheme,\n        title: `Switch to ${theme === 'light' ? 'dark' : 'light'} theme`,\n        children: theme === 'light' ? /*#__PURE__*/_jsxDEV(Moon, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(Sun, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 53\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-button\",\n        onClick: handleExport,\n        title: \"Export data\",\n        children: /*#__PURE__*/_jsxDEV(Download, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-button\",\n        title: \"Settings\",\n        children: /*#__PURE__*/_jsxDEV(Settings, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"header-button\",\n        onClick: handleToggleRightPanel,\n        title: rightPanelCollapsed ? 'Show context panel' : 'Hide context panel',\n        children: rightPanelCollapsed ? /*#__PURE__*/_jsxDEV(Menu, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 34\n        }, this) : /*#__PURE__*/_jsxDEV(PanelRightClose, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"VQh3AIuDJ09cFmIapvKUHVz+kL4=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "toggleSidebar", "toggleRightPanel", "setTheme", "FileText", "Settings", "Download", "Sun", "Moon", "<PERSON><PERSON>", "PanelLeftClose", "PanelRightClose", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Header", "_s", "dispatch", "theme", "sidebarCollapsed", "rightPanelCollapsed", "state", "ui", "currentSession", "isConnected", "session", "isStreaming", "chat", "handleToggleSidebar", "handleToggleRightPanel", "handleToggleTheme", "handleExport", "console", "log", "className", "children", "onClick", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "session_id", "slice", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { toggleSidebar, toggleRightPanel, setTheme } from '../../store/slices/uiSlice';\nimport {\n  FileText,\n  Settings,\n  Download,\n  Sun,\n  Moon,\n  Menu,\n  PanelLeftClose,\n  PanelRightClose,\n  Wifi,\n  WifiOff\n} from 'lucide-react';\nimport './Header.css';\n\nconst Header: React.FC = () => {\n  const dispatch = useDispatch();\n  const { theme, sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentSession, isConnected } = useSelector((state: RootState) => state.session);\n  const { isStreaming } = useSelector((state: RootState) => state.chat);\n\n  const handleToggleSidebar = () => {\n    dispatch(toggleSidebar());\n  };\n\n  const handleToggleRightPanel = () => {\n    dispatch(toggleRightPanel());\n  };\n\n  const handleToggleTheme = () => {\n    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));\n  };\n\n  const handleExport = () => {\n    // TODO: Implement export functionality\n    console.log('Export functionality to be implemented');\n  };\n\n  return (\n    <header className={`header ${theme}`}>\n      <div className=\"header-left\">\n        <button\n          className=\"header-button\"\n          onClick={handleToggleSidebar}\n          title={sidebarCollapsed ? 'Show chat panel' : 'Hide chat panel'}\n        >\n          {sidebarCollapsed ? <Menu size={20} /> : <PanelLeftClose size={20} />}\n        </button>\n\n        <div className=\"header-logo\">\n          <FileText size={24} className=\"logo-icon\" />\n          <h1 className=\"logo-text\">Paper Agent</h1>\n        </div>\n      </div>\n\n      <div className=\"header-center\">\n        {currentSession && (\n          <div className=\"session-info\">\n            <div className=\"session-status\">\n              {isConnected ? (\n                <Wifi size={16} className=\"status-icon connected\" />\n              ) : (\n                <WifiOff size={16} className=\"status-icon disconnected\" />\n              )}\n              <span className=\"session-id\">\n                Session: {currentSession.session_id.slice(0, 8)}...\n              </span>\n            </div>\n\n            {isStreaming && (\n              <div className=\"streaming-indicator\">\n                <div className=\"streaming-dot\"></div>\n                <span>AI is thinking...</span>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      <div className=\"header-right\">\n        <button\n          className=\"header-button\"\n          onClick={handleToggleTheme}\n          title={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}\n        >\n          {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}\n        </button>\n\n        <button\n          className=\"header-button\"\n          onClick={handleExport}\n          title=\"Export data\"\n        >\n          <Download size={20} />\n        </button>\n\n        <button\n          className=\"header-button\"\n          title=\"Settings\"\n        >\n          <Settings size={20} />\n        </button>\n\n        <button\n          className=\"header-button\"\n          onClick={handleToggleRightPanel}\n          title={rightPanelCollapsed ? 'Show context panel' : 'Hide context panel'}\n        >\n          {rightPanelCollapsed ? <Menu size={20} /> : <PanelRightClose size={20} />}\n        </button>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,4BAA4B;AACtF,SACEC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,cAAc,EACdC,eAAe,EACfC,IAAI,EACJC,OAAO,QACF,cAAc;AACrB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoB,KAAK;IAAEC,gBAAgB;IAAEC;EAAoB,CAAC,GAAGrB,WAAW,CAAEsB,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EACpG,MAAM;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGzB,WAAW,CAAEsB,KAAgB,IAAKA,KAAK,CAACI,OAAO,CAAC;EACxF,MAAM;IAAEC;EAAY,CAAC,GAAG3B,WAAW,CAAEsB,KAAgB,IAAKA,KAAK,CAACM,IAAI,CAAC;EAErE,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCX,QAAQ,CAACjB,aAAa,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAM6B,sBAAsB,GAAGA,CAAA,KAAM;IACnCZ,QAAQ,CAAChB,gBAAgB,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,QAAQ,CAACf,QAAQ,CAACgB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,oBACEnB,OAAA;IAAQoB,SAAS,EAAE,UAAUhB,KAAK,EAAG;IAAAiB,QAAA,gBACnCrB,OAAA;MAAKoB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrB,OAAA;QACEoB,SAAS,EAAC,eAAe;QACzBE,OAAO,EAAER,mBAAoB;QAC7BS,KAAK,EAAElB,gBAAgB,GAAG,iBAAiB,GAAG,iBAAkB;QAAAgB,QAAA,EAE/DhB,gBAAgB,gBAAGL,OAAA,CAACN,IAAI;UAAC8B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACL,cAAc;UAAC6B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAET5B,OAAA;QAAKoB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrB,OAAA,CAACX,QAAQ;UAACmC,IAAI,EAAE,EAAG;UAACJ,SAAS,EAAC;QAAW;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5C5B,OAAA;UAAIoB,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5B,OAAA;MAAKoB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BZ,cAAc,iBACbT,OAAA;QAAKoB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrB,OAAA;UAAKoB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC5BX,WAAW,gBACVV,OAAA,CAACH,IAAI;YAAC2B,IAAI,EAAE,EAAG;YAACJ,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpD5B,OAAA,CAACF,OAAO;YAAC0B,IAAI,EAAE,EAAG;YAACJ,SAAS,EAAC;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAC1D,eACD5B,OAAA;YAAMoB,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAC,WAClB,EAACZ,cAAc,CAACoB,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KAClD;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELhB,WAAW,iBACVZ,OAAA;UAAKoB,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCrB,OAAA;YAAKoB,SAAS,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrC5B,OAAA;YAAAqB,QAAA,EAAM;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN5B,OAAA;MAAKoB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrB,OAAA;QACEoB,SAAS,EAAC,eAAe;QACzBE,OAAO,EAAEN,iBAAkB;QAC3BO,KAAK,EAAE,aAAanB,KAAK,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,QAAS;QAAAiB,QAAA,EAEhEjB,KAAK,KAAK,OAAO,gBAAGJ,OAAA,CAACP,IAAI;UAAC+B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACR,GAAG;UAACgC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAET5B,OAAA;QACEoB,SAAS,EAAC,eAAe;QACzBE,OAAO,EAAEL,YAAa;QACtBM,KAAK,EAAC,aAAa;QAAAF,QAAA,eAEnBrB,OAAA,CAACT,QAAQ;UAACiC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAET5B,OAAA;QACEoB,SAAS,EAAC,eAAe;QACzBG,KAAK,EAAC,UAAU;QAAAF,QAAA,eAEhBrB,OAAA,CAACV,QAAQ;UAACkC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAET5B,OAAA;QACEoB,SAAS,EAAC,eAAe;QACzBE,OAAO,EAAEP,sBAAuB;QAChCQ,KAAK,EAAEjB,mBAAmB,GAAG,oBAAoB,GAAG,oBAAqB;QAAAe,QAAA,EAExEf,mBAAmB,gBAAGN,OAAA,CAACN,IAAI;UAAC8B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACJ,eAAe;UAAC4B,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC1B,EAAA,CAlGID,MAAgB;EAAA,QACHjB,WAAW,EAC6BC,WAAW,EAC5BA,WAAW,EAC3BA,WAAW;AAAA;AAAA8C,EAAA,GAJ/B9B,MAAgB;AAoGtB,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}