{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx\";\nimport React from 'react';\nimport { User, Bot, Info } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MessageBubble = ({\n  message\n}) => {\n  var _message$metadata;\n  const formatTimestamp = timestamp => {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const renderContent = () => {\n    if (message.role === 'assistant') {\n      // Render markdown for assistant messages\n      const html = marked.parse(message.content);\n      const sanitizedHtml = DOMPurify.sanitize(html);\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        dangerouslySetInnerHTML: {\n          __html: sanitizedHtml\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: message.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  };\n  const getIcon = () => {\n    switch (message.role) {\n      case 'user':\n        return /*#__PURE__*/_jsxDEV(User, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 16\n        }, this);\n      case 'assistant':\n        return /*#__PURE__*/_jsxDEV(Bot, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 16\n        }, this);\n      case 'system':\n        return /*#__PURE__*/_jsxDEV(Info, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `message-bubble ${message.role}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-icon\",\n        children: getIcon()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-role\",\n        children: message.role === 'user' ? 'You' : message.role === 'assistant' ? 'AI Assistant' : 'System'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-content\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), ((_message$metadata = message.metadata) === null || _message$metadata === void 0 ? void 0 : _message$metadata.intermediateSteps) && message.metadata.intermediateSteps.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-steps\",\n      children: /*#__PURE__*/_jsxDEV(\"details\", {\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          children: [\"Tool Executions (\", message.metadata.intermediateSteps.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"steps-list\",\n          children: message.metadata.intermediateSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-type\",\n                children: step.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 21\n              }, this), step.tool_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"step-tool\",\n                children: step.tool_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this), step.result && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-result\",\n              children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: JSON.stringify(step.result, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-metadata\",\n      children: formatTimestamp(message.timestamp)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c = MessageBubble;\nexport default MessageBubble;\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");", "map": {"version": 3, "names": ["React", "User", "Bot", "Info", "marked", "DOMPurify", "jsxDEV", "_jsxDEV", "MessageBubble", "message", "_message$metadata", "formatTimestamp", "timestamp", "date", "Date", "toLocaleTimeString", "hour", "minute", "renderContent", "role", "html", "parse", "content", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "getIcon", "size", "className", "metadata", "intermediateSteps", "length", "map", "step", "index", "type", "tool_name", "result", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx"], "sourcesContent": ["import React from 'react';\nimport { ChatMessage } from '../../store/slices/chatSlice';\nimport { User, Bot, Info } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\n\ninterface MessageBubbleProps {\n  message: ChatMessage;\n}\n\nconst MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const renderContent = () => {\n    if (message.role === 'assistant') {\n      // Render markdown for assistant messages\n      const html = marked.parse(message.content);\n      const sanitizedHtml = DOMPurify.sanitize(html);\n      return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n    }\n    return <div>{message.content}</div>;\n  };\n\n  const getIcon = () => {\n    switch (message.role) {\n      case 'user':\n        return <User size={16} />;\n      case 'assistant':\n        return <Bot size={16} />;\n      case 'system':\n        return <Info size={16} />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className={`message-bubble ${message.role}`}>\n      <div className=\"message-header\">\n        <div className=\"message-icon\">\n          {getIcon()}\n        </div>\n        <div className=\"message-role\">\n          {message.role === 'user' ? 'You' : message.role === 'assistant' ? 'AI Assistant' : 'System'}\n        </div>\n      </div>\n\n      <div className=\"message-content\">\n        {renderContent()}\n      </div>\n\n      {message.metadata?.intermediateSteps && message.metadata.intermediateSteps.length > 0 && (\n        <div className=\"message-steps\">\n          <details>\n            <summary>Tool Executions ({message.metadata.intermediateSteps.length})</summary>\n            <div className=\"steps-list\">\n              {message.metadata.intermediateSteps.map((step, index) => (\n                <div key={index} className=\"step-item\">\n                  <div className=\"step-header\">\n                    <span className=\"step-type\">{step.type}</span>\n                    {step.tool_name && <span className=\"step-tool\">{step.tool_name}</span>}\n                  </div>\n                  {step.result && (\n                    <div className=\"step-result\">\n                      <pre>{JSON.stringify(step.result, null, 2)}</pre>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </details>\n        </div>\n      )}\n\n      <div className=\"message-metadata\">\n        {formatTimestamp(message.timestamp)}\n      </div>\n    </div>\n  );\n};\n\nexport default MessageBubble;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,IAAI,EAAEC,GAAG,EAAEC,IAAI,QAAQ,cAAc;AAC9C,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMlC,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAA,IAAAC,iBAAA;EACnE,MAAMC,eAAe,GAAIC,SAAiB,IAAK;IAC7C,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EAC5E,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIT,OAAO,CAACU,IAAI,KAAK,WAAW,EAAE;MAChC;MACA,MAAMC,IAAI,GAAGhB,MAAM,CAACiB,KAAK,CAACZ,OAAO,CAACa,OAAO,CAAC;MAC1C,MAAMC,aAAa,GAAGlB,SAAS,CAACmB,QAAQ,CAACJ,IAAI,CAAC;MAC9C,oBAAOb,OAAA;QAAKkB,uBAAuB,EAAE;UAAEC,MAAM,EAAEH;QAAc;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpE;IACA,oBAAOvB,OAAA;MAAAwB,QAAA,EAAMtB,OAAO,CAACa;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACrC,CAAC;EAED,MAAME,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQvB,OAAO,CAACU,IAAI;MAClB,KAAK,MAAM;QACT,oBAAOZ,OAAA,CAACN,IAAI;UAACgC,IAAI,EAAE;QAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,WAAW;QACd,oBAAOvB,OAAA,CAACL,GAAG;UAAC+B,IAAI,EAAE;QAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1B,KAAK,QAAQ;QACX,oBAAOvB,OAAA,CAACJ,IAAI;UAAC8B,IAAI,EAAE;QAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEvB,OAAA;IAAK2B,SAAS,EAAE,kBAAkBzB,OAAO,CAACU,IAAI,EAAG;IAAAY,QAAA,gBAC/CxB,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAH,QAAA,gBAC7BxB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAH,QAAA,EAC1BC,OAAO,CAAC;MAAC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACNvB,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAH,QAAA,EAC1BtB,OAAO,CAACU,IAAI,KAAK,MAAM,GAAG,KAAK,GAAGV,OAAO,CAACU,IAAI,KAAK,WAAW,GAAG,cAAc,GAAG;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAK2B,SAAS,EAAC,iBAAiB;MAAAH,QAAA,EAC7Bb,aAAa,CAAC;IAAC;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,EAEL,EAAApB,iBAAA,GAAAD,OAAO,CAAC0B,QAAQ,cAAAzB,iBAAA,uBAAhBA,iBAAA,CAAkB0B,iBAAiB,KAAI3B,OAAO,CAAC0B,QAAQ,CAACC,iBAAiB,CAACC,MAAM,GAAG,CAAC,iBACnF9B,OAAA;MAAK2B,SAAS,EAAC,eAAe;MAAAH,QAAA,eAC5BxB,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAAwB,QAAA,GAAS,mBAAiB,EAACtB,OAAO,CAAC0B,QAAQ,CAACC,iBAAiB,CAACC,MAAM,EAAC,GAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAChFvB,OAAA;UAAK2B,SAAS,EAAC,YAAY;UAAAH,QAAA,EACxBtB,OAAO,CAAC0B,QAAQ,CAACC,iBAAiB,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAClDjC,OAAA;YAAiB2B,SAAS,EAAC,WAAW;YAAAH,QAAA,gBACpCxB,OAAA;cAAK2B,SAAS,EAAC,aAAa;cAAAH,QAAA,gBAC1BxB,OAAA;gBAAM2B,SAAS,EAAC,WAAW;gBAAAH,QAAA,EAAEQ,IAAI,CAACE;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC7CS,IAAI,CAACG,SAAS,iBAAInC,OAAA;gBAAM2B,SAAS,EAAC,WAAW;gBAAAH,QAAA,EAAEQ,IAAI,CAACG;cAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EACLS,IAAI,CAACI,MAAM,iBACVpC,OAAA;cAAK2B,SAAS,EAAC,aAAa;cAAAH,QAAA,eAC1BxB,OAAA;gBAAAwB,QAAA,EAAMa,IAAI,CAACC,SAAS,CAACN,IAAI,CAACI,MAAM,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CACN;UAAA,GATOU,KAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACN,eAEDvB,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAH,QAAA,EAC9BpB,eAAe,CAACF,OAAO,CAACG,SAAS;IAAC;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACgB,EAAA,GAxEItC,aAA2C;AA0EjD,eAAeA,aAAa;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}