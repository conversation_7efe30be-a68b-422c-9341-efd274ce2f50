{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 6a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6Z\",\n  key: \"fpq118\"\n}], [\"path\", {\n  d: \"M5 10h14\",\n  key: \"elsbfy\"\n}], [\"path\", {\n  d: \"M15 7v6\",\n  key: \"1nx30x\"\n}]];\nconst Refrigerator = createLucideIcon(\"refrigerator\", __iconNode);\nexport { __iconNode, Refrigerator as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Refrigerator", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/refrigerator.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M5 6a4 4 0 0 1 4-4h6a4 4 0 0 1 4 4v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6Z', key: 'fpq118' },\n  ],\n  ['path', { d: 'M5 10h14', key: 'elsbfy' }],\n  ['path', { d: 'M15 7v6', key: '1nx30x' }],\n];\n\n/**\n * @component @name Refrigerator\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA2YTQgNCAwIDAgMSA0LTRoNmE0IDQgMCAwIDEgNCA0djE0YTIgMiAwIDAgMS0yIDJIN2EyIDIgMCAwIDEtMi0yVjZaIiAvPgogIDxwYXRoIGQ9Ik01IDEwaDE0IiAvPgogIDxwYXRoIGQ9Ik0xNSA3djYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refrigerator\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Refrigerator = createLucideIcon('refrigerator', __iconNode);\n\nexport default Refrigerator;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,YAAA,GAAeC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}