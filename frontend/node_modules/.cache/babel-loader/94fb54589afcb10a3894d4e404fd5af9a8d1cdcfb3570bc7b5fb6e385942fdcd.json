{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setCurrentView } from '../../store/slices/uiSlice';\nimport PaperEditor from '../Editor/PaperEditor';\nimport ChartViewer from '../Charts/ChartViewer';\nimport SearchResults from '../Search/SearchResults';\nimport WelcomeScreen from './WelcomeScreen';\nimport './MainWorkArea.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainWorkArea = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    currentView\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper\n  } = useSelector(state => state.papers);\n  const {\n    charts\n  } = useSelector(state => state.tools);\n  const {\n    searchResults\n  } = useSelector(state => state.tools);\n  const handleViewChange = view => {\n    dispatch(setCurrentView(view));\n  };\n  const renderContent = () => {\n    switch (currentView) {\n      case 'editor':\n        return currentPaper ? /*#__PURE__*/_jsxDEV(PaperEditor, {\n          paper: currentPaper\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-paper-selected\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Paper Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Select a paper from the context panel or create a new one to start editing.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this);\n      case 'charts':\n        return charts.length > 0 ? /*#__PURE__*/_jsxDEV(ChartViewer, {\n          charts: charts\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-charts\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Charts Generated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Ask the AI to create visualizations from your research data.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this);\n      case 'papers':\n        return searchResults.length > 0 ? /*#__PURE__*/_jsxDEV(SearchResults, {\n          results: searchResults\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-search-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Search Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Use the chat to search for research papers and view results here.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this);\n      case 'chat':\n      default:\n        return /*#__PURE__*/_jsxDEV(WelcomeScreen, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"main-work-area\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"work-area-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"view-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'chat' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Welcome\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'editor' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Paper Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'charts' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: [\"Charts (\", charts.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab ${currentView === 'papers' ? 'active' : ''}`,\n          onClick: () => {/* TODO: Implement view switching */},\n          children: \"Search Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"work-area-content\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(MainWorkArea, \"GClh+M+jlS9GKkM1vqFKShnbnoQ=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector, useSelector];\n});\n_c = MainWorkArea;\nexport default MainWorkArea;\nvar _c;\n$RefreshReg$(_c, \"MainWorkArea\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "set<PERSON><PERSON><PERSON>View", "PaperEditor", "ChartViewer", "SearchResults", "WelcomeScreen", "jsxDEV", "_jsxDEV", "MainWorkArea", "_s", "dispatch", "current<PERSON>iew", "state", "ui", "currentPaper", "papers", "charts", "tools", "searchResults", "handleViewChange", "view", "renderContent", "paper", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "length", "results", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx"], "sourcesContent": ["import React from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { setCurrentView } from '../../store/slices/uiSlice';\nimport PaperEditor from '../Editor/PaperEditor';\nimport ChartViewer from '../Charts/ChartViewer';\nimport SearchResults from '../Search/SearchResults';\nimport WelcomeScreen from './WelcomeScreen';\nimport './MainWorkArea.css';\n\nconst MainWorkArea: React.FC = () => {\n  const dispatch = useDispatch();\n  const { currentView } = useSelector((state: RootState) => state.ui);\n  const { currentPaper } = useSelector((state: RootState) => state.papers);\n  const { charts } = useSelector((state: RootState) => state.tools);\n  const { searchResults } = useSelector((state: RootState) => state.tools);\n\n  const handleViewChange = (view: typeof currentView) => {\n    dispatch(setCurrentView(view));\n  };\n\n  const renderContent = () => {\n    switch (currentView) {\n      case 'editor':\n        return currentPaper ? (\n          <PaperEditor paper={currentPaper} />\n        ) : (\n          <div className=\"no-paper-selected\">\n            <h3>No Paper Selected</h3>\n            <p>Select a paper from the context panel or create a new one to start editing.</p>\n          </div>\n        );\n\n      case 'charts':\n        return charts.length > 0 ? (\n          <ChartViewer charts={charts} />\n        ) : (\n          <div className=\"no-charts\">\n            <h3>No Charts Generated</h3>\n            <p>Ask the AI to create visualizations from your research data.</p>\n          </div>\n        );\n\n      case 'papers':\n        return searchResults.length > 0 ? (\n          <SearchResults results={searchResults} />\n        ) : (\n          <div className=\"no-search-results\">\n            <h3>No Search Results</h3>\n            <p>Use the chat to search for research papers and view results here.</p>\n          </div>\n        );\n\n      case 'chat':\n      default:\n        return <WelcomeScreen />;\n    }\n  };\n\n  return (\n    <div className=\"main-work-area\">\n      <div className=\"work-area-header\">\n        <div className=\"view-tabs\">\n          <button\n            className={`tab ${currentView === 'chat' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Welcome\n          </button>\n          <button\n            className={`tab ${currentView === 'editor' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Paper Editor\n          </button>\n          <button\n            className={`tab ${currentView === 'charts' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Charts ({charts.length})\n          </button>\n          <button\n            className={`tab ${currentView === 'papers' ? 'active' : ''}`}\n            onClick={() => {/* TODO: Implement view switching */ }}\n          >\n            Search Results\n          </button>\n        </div>\n      </div>\n\n      <div className=\"work-area-content\">\n        {renderContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default MainWorkArea;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAY,CAAC,GAAGX,WAAW,CAAEY,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EACnE,MAAM;IAAEC;EAAa,CAAC,GAAGd,WAAW,CAAEY,KAAgB,IAAKA,KAAK,CAACG,MAAM,CAAC;EACxE,MAAM;IAAEC;EAAO,CAAC,GAAGhB,WAAW,CAAEY,KAAgB,IAAKA,KAAK,CAACK,KAAK,CAAC;EACjE,MAAM;IAAEC;EAAc,CAAC,GAAGlB,WAAW,CAAEY,KAAgB,IAAKA,KAAK,CAACK,KAAK,CAAC;EAExE,MAAME,gBAAgB,GAAIC,IAAwB,IAAK;IACrDV,QAAQ,CAACT,cAAc,CAACmB,IAAI,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQV,WAAW;MACjB,KAAK,QAAQ;QACX,OAAOG,YAAY,gBACjBP,OAAA,CAACL,WAAW;UAACoB,KAAK,EAAER;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpCnB,OAAA;UAAKoB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrB,OAAA;YAAAqB,QAAA,EAAI;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BnB,OAAA;YAAAqB,QAAA,EAAG;UAA2E;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CACN;MAEH,KAAK,QAAQ;QACX,OAAOV,MAAM,CAACa,MAAM,GAAG,CAAC,gBACtBtB,OAAA,CAACJ,WAAW;UAACa,MAAM,EAAEA;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/BnB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrB,OAAA;YAAAqB,QAAA,EAAI;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BnB,OAAA;YAAAqB,QAAA,EAAG;UAA4D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACN;MAEH,KAAK,QAAQ;QACX,OAAOR,aAAa,CAACW,MAAM,GAAG,CAAC,gBAC7BtB,OAAA,CAACH,aAAa;UAAC0B,OAAO,EAAEZ;QAAc;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzCnB,OAAA;UAAKoB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrB,OAAA;YAAAqB,QAAA,EAAI;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BnB,OAAA;YAAAqB,QAAA,EAAG;UAAiE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CACN;MAEH,KAAK,MAAM;MACX;QACE,oBAAOnB,OAAA,CAACF,aAAa;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5B;EACF,CAAC;EAED,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BrB,OAAA;MAAKoB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BrB,OAAA;QAAKoB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBrB,OAAA;UACEoB,SAAS,EAAE,OAAOhB,WAAW,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC3DoB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UACEoB,SAAS,EAAE,OAAOhB,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DoB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UACEoB,SAAS,EAAE,OAAOhB,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DoB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,GACxD,UACS,EAACZ,MAAM,CAACa,MAAM,EAAC,GACzB;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnB,OAAA;UACEoB,SAAS,EAAE,OAAOhB,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC7DoB,OAAO,EAAEA,CAAA,KAAM,CAAC,qCAAuC;UAAAH,QAAA,EACxD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnB,OAAA;MAAKoB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/BP,aAAa,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CArFID,YAAsB;EAAA,QACTT,WAAW,EACJC,WAAW,EACVA,WAAW,EACjBA,WAAW,EACJA,WAAW;AAAA;AAAAgC,EAAA,GALjCxB,YAAsB;AAuF5B,eAAeA,YAAY;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}