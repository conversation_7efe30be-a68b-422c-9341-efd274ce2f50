{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M19.13 5.09C15.22 9.14 10 10.44 2.25 10.94\",\n  key: \"hpej1\"\n}], [\"path\", {\n  d: \"M21.75 12.84c-6.62-1.41-12.14 1-16.38 6.32\",\n  key: \"1tr44o\"\n}], [\"path\", {\n  d: \"M8.56 2.75c4.37 6 6 9.42 8 17.72\",\n  key: \"kbh691\"\n}]];\nconst Dribbble = createLucideIcon(\"dribbble\", __iconNode);\nexport { __iconNode, Dribbble as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/dribbble.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M19.13 5.09C15.22 9.14 10 10.44 2.25 10.94', key: 'hpej1' }],\n  ['path', { d: 'M21.75 12.84c-6.62-1.41-12.14 1-16.38 6.32', key: '1tr44o' }],\n  ['path', { d: 'M8.56 2.75c4.37 6 6 9.42 8 17.72', key: 'kbh691' }],\n];\n\n/**\n * @component @name Dribbble\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTkuMTMgNS4wOUMxNS4yMiA5LjE0IDEwIDEwLjQ0IDIuMjUgMTAuOTQiIC8+CiAgPHBhdGggZD0iTTIxLjc1IDEyLjg0Yy02LjYyLTEuNDEtMTIuMTQgMS0xNi4zOCA2LjMyIiAvPgogIDxwYXRoIGQ9Ik04LjU2IDIuNzVjNC4zNyA2IDYgOS40MiA4IDE3LjcyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dribbble\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=dribbble instead. This icon will be removed in v1.0\n */\nconst Dribbble = createLucideIcon('dribbble', __iconNode);\n\nexport default Dribbble;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAD,GAAA,EAAK;AAAA,CAAS,GAC1E,CAAC,MAAQ;EAAEC,CAAA,EAAG,4CAA8C;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAEC,CAAA,EAAG,kCAAoC;EAAAD,GAAA,EAAK;AAAU,GACnE;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,aAAYP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}