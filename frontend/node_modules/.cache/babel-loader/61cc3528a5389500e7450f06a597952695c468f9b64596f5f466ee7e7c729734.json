{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport Header from './Header';\nimport ChatPanel from '../Chat/ChatPanel';\nimport MainWorkArea from './MainWorkArea';\nimport ContextPanel from './ContextPanel';\nimport NotificationContainer from '../UI/NotificationContainer';\nimport { setCurrentSession } from '../../store/slices/sessionSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport api from '../../services/api';\nimport useRealTimeSync from '../../hooks/useRealTimeSync';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    sidebarCollapsed,\n    rightPanelCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentSession\n  } = useSelector(state => state.session);\n\n  // Initialize real-time sync\n  useRealTimeSync();\n  const initializeSession = useCallback(async () => {\n    try {\n      const result = await api.session.create();\n      if (result.data) {\n        dispatch(setCurrentSession({\n          session_id: result.data.session_id,\n          created_at: result.data.created_at,\n          last_activity: new Date().toISOString(),\n          metadata: {}\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create session');\n      }\n    } catch (error) {\n      console.error('Failed to initialize session:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Session Error',\n        message: 'Failed to initialize session',\n        duration: 5000\n      }));\n    }\n  }, [dispatch]);\n  useEffect(() => {\n    // Initialize session on app load\n    if (!currentSession) {\n      initializeSession();\n    }\n  }, [currentSession, initializeSession]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"layout-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(ChatPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-work-area\",\n        children: /*#__PURE__*/_jsxDEV(MainWorkArea, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(ContextPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NotificationContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"Dx/ElDNBbSkDC8AZVWUy2Xt/Huk=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useRealTimeSync];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useEffect", "useCallback", "useDispatch", "useSelector", "Header", "ChatPanel", "MainWorkArea", "ContextPanel", "NotificationContainer", "setCurrentSession", "addNotification", "api", "useRealTimeSync", "jsxDEV", "_jsxDEV", "Layout", "_s", "dispatch", "sidebarCollapsed", "rightPanelCollapsed", "state", "ui", "currentSession", "session", "initializeSession", "result", "create", "data", "session_id", "created_at", "last_activity", "Date", "toISOString", "metadata", "Error", "error", "console", "type", "title", "message", "duration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport Header from './Header';\nimport ChatPanel from '../Chat/ChatPanel';\nimport MainWorkArea from './MainWorkArea';\nimport ContextPanel from './ContextPanel';\nimport NotificationContainer from '../UI/NotificationContainer';\nimport { setCurrentSession } from '../../store/slices/sessionSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport api from '../../services/api';\nimport useRealTimeSync from '../../hooks/useRealTimeSync';\nimport './Layout.css';\n\nconst Layout: React.FC = () => {\n  const dispatch = useDispatch();\n  const { sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentSession } = useSelector((state: RootState) => state.session);\n\n  // Initialize real-time sync\n  useRealTimeSync();\n\n  const initializeSession = useCallback(async () => {\n    try {\n      const result = await api.session.create();\n\n      if (result.data) {\n        dispatch(setCurrentSession({\n          session_id: result.data.session_id,\n          created_at: result.data.created_at,\n          last_activity: new Date().toISOString(),\n          metadata: {},\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create session');\n      }\n    } catch (error) {\n      console.error('Failed to initialize session:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Session Error',\n        message: 'Failed to initialize session',\n        duration: 5000,\n      }));\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Initialize session on app load\n    if (!currentSession) {\n      initializeSession();\n    }\n  }, [currentSession, initializeSession]);\n\n  return (\n    <div className=\"layout\">\n      <Header />\n\n      <div className=\"layout-body\">\n        {/* Chat Panel - Left Side */}\n        <div className={`chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`}>\n          <ChatPanel />\n        </div>\n\n        {/* Main Work Area - Center */}\n        <div className=\"main-work-area\">\n          <MainWorkArea />\n        </div>\n\n        {/* Context Panel - Right Side */}\n        <div className={`context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`}>\n          <ContextPanel />\n        </div>\n      </div>\n\n      {/* Global Notifications */}\n      <NotificationContainer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB,gBAAgB;IAAEC;EAAoB,CAAC,GAAGhB,WAAW,CAAEiB,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EAC7F,MAAM;IAAEC;EAAe,CAAC,GAAGnB,WAAW,CAAEiB,KAAgB,IAAKA,KAAK,CAACG,OAAO,CAAC;;EAE3E;EACAX,eAAe,CAAC,CAAC;EAEjB,MAAMY,iBAAiB,GAAGvB,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMwB,MAAM,GAAG,MAAMd,GAAG,CAACY,OAAO,CAACG,MAAM,CAAC,CAAC;MAEzC,IAAID,MAAM,CAACE,IAAI,EAAE;QACfV,QAAQ,CAACR,iBAAiB,CAAC;UACzBmB,UAAU,EAAEH,MAAM,CAACE,IAAI,CAACC,UAAU;UAClCC,UAAU,EAAEJ,MAAM,CAACE,IAAI,CAACE,UAAU;UAClCC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACvCC,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACT,MAAM,CAACU,KAAK,IAAI,0BAA0B,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDlB,QAAQ,CAACP,eAAe,CAAC;QACvB2B,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE,8BAA8B;QACvCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;EAEdjB,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACsB,cAAc,EAAE;MACnBE,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACF,cAAc,EAAEE,iBAAiB,CAAC,CAAC;EAEvC,oBACEV,OAAA;IAAK2B,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrB5B,OAAA,CAACV,MAAM;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEVhC,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1B5B,OAAA;QAAK2B,SAAS,EAAE,cAAcvB,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAwB,QAAA,eAClE5B,OAAA,CAACT,SAAS;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B5B,OAAA,CAACR,YAAY;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGNhC,OAAA;QAAK2B,SAAS,EAAE,iBAAiBtB,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAuB,QAAA,eACxE5B,OAAA,CAACP,YAAY;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhC,OAAA,CAACN,qBAAqB;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAjEID,MAAgB;EAAA,QACHb,WAAW,EACsBC,WAAW,EAClCA,WAAW,EAGtCS,eAAe;AAAA;AAAAmC,EAAA,GANXhC,MAAgB;AAmEtB,eAAeA,MAAM;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}