{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx\";\nimport React from 'react';\nimport { Search, FileText, BarChart3, Database, Zap, Brain } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeScreen = () => {\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(Search, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this),\n    title: 'Smart Research',\n    description: 'Search academic papers and conduct deep research analysis with AI-powered insights.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FileText, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this),\n    title: 'Live Paper Editor',\n    description: 'Write and edit research papers with real-time markdown rendering and auto-save.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(BarChart3, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this),\n    title: 'Data Visualization',\n    description: 'Generate interactive charts and graphs to visualize your research findings.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Database, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    title: 'Data Management',\n    description: 'Store and organize your research data with integrated Airtable support.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Zap, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: 'Real-time Streaming',\n    description: 'Watch AI tools execute in real-time with live progress updates and results.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Brain, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: 'AI Assistant',\n    description: 'Get intelligent help with research, writing, and data analysis tasks.'\n  }];\n  const exampleQueries = ['Research AI applications in healthcare', 'Create a chart showing research methodology trends', 'Write a paper about machine learning', 'Search for papers on quantum computing', 'Analyze climate change research data', 'Generate a literature review outline'];\n  const handleExampleClick = query => {\n    // TODO: Implement sending example query to chat\n    console.log('Example query clicked:', query);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"welcome-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"welcome-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"welcome-title\",\n        children: \"Welcome to Paper Agent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"welcome-subtitle\",\n        children: \"Your AI-powered research assistant for academic papers, data analysis, and scientific writing. Powered by LangGraph with real-time streaming and advanced tool integration.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-features\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"feature-title\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"feature-description\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-cta\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"cta-text\",\n          children: \"Get started by asking a question in the chat panel, or try one of these examples:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"example-queries\",\n          children: exampleQueries.map((query, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"example-query\",\n            onClick: () => handleExampleClick(query),\n            children: query\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeScreen;\nexport default WelcomeScreen;\nvar _c;\n$RefreshReg$(_c, \"WelcomeScreen\");", "map": {"version": 3, "names": ["React", "Search", "FileText", "BarChart3", "Database", "Zap", "Brain", "jsxDEV", "_jsxDEV", "WelcomeScreen", "features", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "exampleQueries", "handleExampleClick", "query", "console", "log", "className", "children", "map", "feature", "index", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx"], "sourcesContent": ["import React from 'react';\nimport { Search, FileText, BarChart3, Database, Zap, Brain } from 'lucide-react';\n\nconst WelcomeScreen: React.FC = () => {\n  const features = [\n    {\n      icon: <Search size={24} />,\n      title: 'Smart Research',\n      description: 'Search academic papers and conduct deep research analysis with AI-powered insights.'\n    },\n    {\n      icon: <FileText size={24} />,\n      title: 'Live Paper Editor',\n      description: 'Write and edit research papers with real-time markdown rendering and auto-save.'\n    },\n    {\n      icon: <BarChart3 size={24} />,\n      title: 'Data Visualization',\n      description: 'Generate interactive charts and graphs to visualize your research findings.'\n    },\n    {\n      icon: <Database size={24} />,\n      title: 'Data Management',\n      description: 'Store and organize your research data with integrated Airtable support.'\n    },\n    {\n      icon: <Zap size={24} />,\n      title: 'Real-time Streaming',\n      description: 'Watch AI tools execute in real-time with live progress updates and results.'\n    },\n    {\n      icon: <Brain size={24} />,\n      title: 'AI Assistant',\n      description: 'Get intelligent help with research, writing, and data analysis tasks.'\n    }\n  ];\n\n  const exampleQueries = [\n    'Research AI applications in healthcare',\n    'Create a chart showing research methodology trends',\n    'Write a paper about machine learning',\n    'Search for papers on quantum computing',\n    'Analyze climate change research data',\n    'Generate a literature review outline'\n  ];\n\n  const handleExampleClick = (query: string) => {\n    // TODO: Implement sending example query to chat\n    console.log('Example query clicked:', query);\n  };\n\n  return (\n    <div className=\"welcome-screen\">\n      <div className=\"welcome-content\">\n        <h1 className=\"welcome-title\">\n          Welcome to Paper Agent\n        </h1>\n\n        <p className=\"welcome-subtitle\">\n          Your AI-powered research assistant for academic papers, data analysis, and scientific writing.\n          Powered by LangGraph with real-time streaming and advanced tool integration.\n        </p>\n\n        <div className=\"welcome-features\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"feature-card\">\n              <div className=\"feature-icon\">\n                {feature.icon}\n              </div>\n              <h3 className=\"feature-title\">{feature.title}</h3>\n              <p className=\"feature-description\">{feature.description}</p>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"welcome-cta\">\n          <p className=\"cta-text\">\n            Get started by asking a question in the chat panel, or try one of these examples:\n          </p>\n\n          <div className=\"example-queries\">\n            {exampleQueries.map((query, index) => (\n              <button\n                key={index}\n                className=\"example-query\"\n                onClick={() => handleExampleClick(query)}\n              >\n                {query}\n              </button>\n            ))}\n          </div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjF,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EACpC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEH,OAAA,CAACP,MAAM;MAACW,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACN,QAAQ;MAACU,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACL,SAAS;MAACS,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACJ,QAAQ;MAACQ,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACH,GAAG;MAACO,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEH,OAAA,CAACF,KAAK;MAACM,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB,wCAAwC,EACxC,oDAAoD,EACpD,sCAAsC,EACtC,wCAAwC,EACxC,sCAAsC,EACtC,sCAAsC,CACvC;EAED,MAAMC,kBAAkB,GAAIC,KAAa,IAAK;IAC5C;IACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,KAAK,CAAC;EAC9C,CAAC;EAED,oBACEb,OAAA;IAAKgB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BjB,OAAA;MAAKgB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BjB,OAAA;QAAIgB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE9B;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELR,OAAA;QAAGgB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAGhC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJR,OAAA;QAAKgB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9Bf,QAAQ,CAACgB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BpB,OAAA;UAAiBgB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACvCjB,OAAA;YAAKgB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BE,OAAO,CAAChB;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNR,OAAA;YAAIgB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEE,OAAO,CAACV;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClDR,OAAA;YAAGgB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEE,OAAO,CAACT;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GALpDY,KAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENR,OAAA;QAAKgB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjB,OAAA;UAAGgB,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAExB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UAAKgB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7BN,cAAc,CAACO,GAAG,CAAC,CAACL,KAAK,EAAEO,KAAK,kBAC/BpB,OAAA;YAEEgB,SAAS,EAAC,eAAe;YACzBK,OAAO,EAAEA,CAAA,KAAMT,kBAAkB,CAACC,KAAK,CAAE;YAAAI,QAAA,EAExCJ;UAAK,GAJDO,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACc,EAAA,GA9FIrB,aAAuB;AAgG7B,eAAeA,aAAa;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}