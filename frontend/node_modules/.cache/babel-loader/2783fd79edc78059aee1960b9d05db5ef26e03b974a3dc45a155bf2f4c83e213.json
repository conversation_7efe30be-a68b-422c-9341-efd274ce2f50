{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 3v18\",\n  key: \"14nvp0\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M21 9H3\",\n  key: \"1338ky\"\n}], [\"path\", {\n  d: \"M21 15H3\",\n  key: \"9uk58r\"\n}]];\nconst TableProperties = createLucideIcon(\"table-properties\", __iconNode);\nexport { __iconNode, TableProperties as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "TableProperties", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/table-properties.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3v18', key: '14nvp0' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M21 9H3', key: '1338ky' }],\n  ['path', { d: 'M21 15H3', key: '9uk58r' }],\n];\n\n/**\n * @component @name TableProperties\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM3YxOCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0yMSA5SDMiIC8+CiAgPHBhdGggZD0iTTIxIDE1SDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/table-properties\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TableProperties = createLucideIcon('table-properties', __iconNode);\n\nexport default TableProperties;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAM,eAAA,GAAkBC,gBAAiB,qBAAoBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}