{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 21V3\",\n  key: \"1bzk4w\"\n}], [\"path\", {\n  d: \"M2 5h18a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2.26\",\n  key: \"1d64pi\"\n}], [\"path\", {\n  d: \"M7 17v3a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-3\",\n  key: \"5hbqbf\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"qt15rb\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"11\",\n  r: \"2\",\n  key: \"ssideg\"\n}]];\nconst Gpu = createLucideIcon(\"gpu\", __iconNode);\nexport { __iconNode, Gpu as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Gpu", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/gpu.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 21V3', key: '1bzk4w' }],\n  ['path', { d: 'M2 5h18a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2.26', key: '1d64pi' }],\n  ['path', { d: 'M7 17v3a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-3', key: '5hbqbf' }],\n  ['circle', { cx: '16', cy: '11', r: '2', key: 'qt15rb' }],\n  ['circle', { cx: '8', cy: '11', r: '2', key: 'ssideg' }],\n];\n\n/**\n * @component @name Gpu\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMVYzIiAvPgogIDxwYXRoIGQ9Ik0yIDVoMThhMiAyIDAgMCAxIDIgMnY4YTIgMiAwIDAgMS0yIDJIMi4yNiIgLz4KICA8cGF0aCBkPSJNNyAxN3YzYTEgMSAwIDAgMCAxIDFoNWExIDEgMCAwIDAgMS0xdi0zIiAvPgogIDxjaXJjbGUgY3g9IjE2IiBjeT0iMTEiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjExIiByPSIyIiAvPgo8L3N2Zz4=) - https://lucide.dev/icons/gpu\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Gpu = createLucideIcon('gpu', __iconNode);\n\nexport default Gpu;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3E,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,GAAA,GAAMC,gBAAiB,QAAOP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}