{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 13.44 4.442 17.082A2 2 0 0 0 4.982 21H19a2 2 0 0 0 .558-3.921l-1.115-.32A2 2 0 0 1 17 14.837V7.66\",\n  key: \"13vns8\"\n}], [\"path\", {\n  d: \"m7 10.56 12.558-3.642A2 2 0 0 0 19.018 3H5a2 2 0 0 0-.558 3.921l1.115.32A2 2 0 0 1 7 9.163v7.178\",\n  key: \"s8x3u0\"\n}]];\nconst Spool = createLucideIcon(\"spool\", __iconNode);\nexport { __iconNode, Spool as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Spool", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/spool.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M17 13.44 4.442 17.082A2 2 0 0 0 4.982 21H19a2 2 0 0 0 .558-3.921l-1.115-.32A2 2 0 0 1 17 14.837V7.66',\n      key: '13vns8',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'm7 10.56 12.558-3.642A2 2 0 0 0 19.018 3H5a2 2 0 0 0-.558 3.921l1.115.32A2 2 0 0 1 7 9.163v7.178',\n      key: 's8x3u0',\n    },\n  ],\n];\n\n/**\n * @component @name Spool\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMTMuNDQgNC40NDIgMTcuMDgyQTIgMiAwIDAgMCA0Ljk4MiAyMUgxOWEyIDIgMCAwIDAgLjU1OC0zLjkyMWwtMS4xMTUtLjMyQTIgMiAwIDAgMSAxNyAxNC44MzdWNy42NiIgLz4KICA8cGF0aCBkPSJtNyAxMC41NiAxMi41NTgtMy42NDJBMiAyIDAgMCAwIDE5LjAxOCAzSDVhMiAyIDAgMCAwLS41NTggMy45MjFsMS4xMTUuMzJBMiAyIDAgMCAxIDcgOS4xNjN2Ny4xNzgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/spool\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Spool = createLucideIcon('spool', __iconNode);\n\nexport default Spool;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}