{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport './ChatPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatPanel = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    messages,\n    isStreaming,\n    isLoading,\n    currentSessionId\n  } = useSelector(state => state.chat);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper\n  } = useSelector(state => state.papers);\n  const [inputValue, setInputValue] = useState('');\n  const eventSourceRef = useRef(null);\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString()\n    };\n    dispatch(addMessage(userMessage));\n    setInputValue('');\n    dispatch(setLoading(true));\n    try {\n      // Start streaming chat\n      const response = await fetch('http://localhost:8000/api/v1/chat/stream', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message: inputValue.trim(),\n          session_id: currentSessionId,\n          paper_id: currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id,\n          stream: true\n        })\n      });\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Close existing EventSource if any\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n\n      // Create new EventSource for streaming\n      const eventSource = new EventSource(`http://localhost:8000/api/v1/chat/stream?session_id=${currentSessionId}&message=${encodeURIComponent(inputValue.trim())}`);\n      eventSourceRef.current = eventSource;\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n      let assistantMessageId = null;\n      let assistantContent = '';\n      eventSource.onmessage = event => {\n        const data = JSON.parse(event.data);\n        switch (data.event) {\n          case 'session':\n            // Handle session info\n            break;\n          case 'start':\n            // Create assistant message\n            assistantMessageId = Date.now().toString();\n            dispatch(addMessage({\n              id: assistantMessageId,\n              role: 'assistant',\n              content: '',\n              timestamp: new Date().toISOString()\n            }));\n            break;\n          case 'step':\n            // Handle intermediate steps (tool executions)\n            if (data.data.type === 'tool_call') {\n              dispatch(addNotification({\n                type: 'info',\n                title: 'Tool Execution',\n                message: `Executing ${data.data.tool_name}...`,\n                duration: 3000\n              }));\n            }\n            break;\n          case 'response':\n            // Update assistant message content\n            if (assistantMessageId && data.data.message) {\n              assistantContent = data.data.message;\n              dispatch(addMessage({\n                id: assistantMessageId,\n                role: 'assistant',\n                content: assistantContent,\n                timestamp: new Date().toISOString()\n              }));\n            }\n            break;\n          case 'complete':\n            dispatch(setStreaming(false));\n            eventSource.close();\n            break;\n          case 'error':\n            dispatch(setStreaming(false));\n            dispatch(addNotification({\n              type: 'error',\n              title: 'Chat Error',\n              message: data.data.error || 'An error occurred',\n              duration: 5000\n            }));\n            eventSource.close();\n            break;\n        }\n      };\n      eventSource.onerror = () => {\n        dispatch(setStreaming(false));\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Connection Error',\n          message: 'Lost connection to the server',\n          duration: 5000\n        }));\n        eventSource.close();\n      };\n    } catch (error) {\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to send message',\n        duration: 5000\n      }));\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Cleanup EventSource on unmount\n  useEffect(() => {\n    return () => {\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n    };\n  }, []);\n  if (sidebarCollapsed) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-panel-collapsed\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapsed-icon\",\n        children: /*#__PURE__*/_jsxDEV(Send, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-panel-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), currentPaper && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-paper-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC4 \", currentPaper.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-body\",\n      children: [/*#__PURE__*/_jsxDEV(MessageList, {\n        messages: messages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToolStatusList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-footer\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        value: inputValue,\n        onChange: setInputValue,\n        onSend: handleSendMessage,\n        onKeyPress: handleKeyPress,\n        disabled: isStreaming || isLoading,\n        placeholder: \"Ask me anything about research papers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatPanel, \"24WUB1zfH1Ul69SGU0o17TaT1ic=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = ChatPanel;\nexport default ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useDispatch", "useSelector", "addMessage", "setStreaming", "setLoading", "addNotification", "MessageList", "MessageInput", "ToolStatusList", "Send", "jsxDEV", "_jsxDEV", "ChatPanel", "_s", "dispatch", "messages", "isStreaming", "isLoading", "currentSessionId", "state", "chat", "sidebarCollapsed", "ui", "currentPaper", "papers", "inputValue", "setInputValue", "eventSourceRef", "handleSendMessage", "trim", "userMessage", "id", "Date", "now", "toString", "role", "content", "timestamp", "toISOString", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "message", "session_id", "paper_id", "stream", "ok", "Error", "current", "close", "eventSource", "EventSource", "encodeURIComponent", "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onmessage", "event", "data", "parse", "type", "title", "tool_name", "duration", "error", "onerror", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "onSend", "onKeyPress", "disabled", "placeholder", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { addMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport './ChatPanel.css';\n\nconst ChatPanel: React.FC = () => {\n  const dispatch = useDispatch();\n  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentPaper } = useSelector((state: RootState) => state.papers);\n  const [inputValue, setInputValue] = useState('');\n  const eventSourceRef = useRef<EventSource | null>(null);\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n\n    const userMessage = {\n      id: Date.now().toString(),\n      role: 'user' as const,\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString(),\n    };\n\n    dispatch(addMessage(userMessage));\n    setInputValue('');\n    dispatch(setLoading(true));\n\n    try {\n      // Start streaming chat\n      const response = await fetch('http://localhost:8000/api/v1/chat/stream', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: inputValue.trim(),\n          session_id: currentSessionId,\n          paper_id: currentPaper?.paper_id,\n          stream: true,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Close existing EventSource if any\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n\n      // Create new EventSource for streaming\n      const eventSource = new EventSource(`http://localhost:8000/api/v1/chat/stream?session_id=${currentSessionId}&message=${encodeURIComponent(inputValue.trim())}`);\n      eventSourceRef.current = eventSource;\n\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      let assistantMessageId: string | null = null;\n      let assistantContent = '';\n\n      eventSource.onmessage = (event) => {\n        const data = JSON.parse(event.data);\n\n        switch (data.event) {\n          case 'session':\n            // Handle session info\n            break;\n\n          case 'start':\n            // Create assistant message\n            assistantMessageId = Date.now().toString();\n            dispatch(addMessage({\n              id: assistantMessageId,\n              role: 'assistant',\n              content: '',\n              timestamp: new Date().toISOString(),\n            }));\n            break;\n\n          case 'step':\n            // Handle intermediate steps (tool executions)\n            if (data.data.type === 'tool_call') {\n              dispatch(addNotification({\n                type: 'info',\n                title: 'Tool Execution',\n                message: `Executing ${data.data.tool_name}...`,\n                duration: 3000,\n              }));\n            }\n            break;\n\n          case 'response':\n            // Update assistant message content\n            if (assistantMessageId && data.data.message) {\n              assistantContent = data.data.message;\n              dispatch(addMessage({\n                id: assistantMessageId,\n                role: 'assistant',\n                content: assistantContent,\n                timestamp: new Date().toISOString(),\n              }));\n            }\n            break;\n\n          case 'complete':\n            dispatch(setStreaming(false));\n            eventSource.close();\n            break;\n\n          case 'error':\n            dispatch(setStreaming(false));\n            dispatch(addNotification({\n              type: 'error',\n              title: 'Chat Error',\n              message: data.data.error || 'An error occurred',\n              duration: 5000,\n            }));\n            eventSource.close();\n            break;\n        }\n      };\n\n      eventSource.onerror = () => {\n        dispatch(setStreaming(false));\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Connection Error',\n          message: 'Lost connection to the server',\n          duration: 5000,\n        }));\n        eventSource.close();\n      };\n\n    } catch (error) {\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to send message',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Cleanup EventSource on unmount\n  useEffect(() => {\n    return () => {\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n    };\n  }, []);\n\n  if (sidebarCollapsed) {\n    return (\n      <div className=\"chat-panel-collapsed\">\n        <div className=\"collapsed-icon\">\n          <Send size={20} />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"chat-panel-content\">\n      <div className=\"chat-header\">\n        <h2>Chat</h2>\n        {currentPaper && (\n          <div className=\"current-paper-indicator\">\n            <span>📄 {currentPaper.title}</span>\n          </div>\n        )}\n      </div>\n\n      <div className=\"chat-body\">\n        <MessageList messages={messages} />\n        <ToolStatusList />\n      </div>\n\n      <div className=\"chat-footer\">\n        <MessageInput\n          value={inputValue}\n          onChange={setInputValue}\n          onSend={handleSendMessage}\n          onKeyPress={handleKeyPress}\n          disabled={isStreaming || isLoading}\n          placeholder=\"Ask me anything about research papers...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ChatPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,UAAU,EAAEC,YAAY,EAAEC,UAAU,QAAQ,8BAA8B;AACnF,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,QAAQ;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAiB,CAAC,GAAGjB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC5G,MAAM;IAAEC;EAAiB,CAAC,GAAGpB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EACxE,MAAM;IAAEC;EAAa,CAAC,GAAGtB,WAAW,CAAEkB,KAAgB,IAAKA,KAAK,CAACK,MAAM,CAAC;EACxE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM8B,cAAc,GAAG7B,MAAM,CAAqB,IAAI,CAAC;EAEvD,MAAM8B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACH,UAAU,CAACI,IAAI,CAAC,CAAC,IAAIb,WAAW,IAAIC,SAAS,EAAE;IAEpD,MAAMa,WAAW,GAAG;MAClBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE,MAAe;MACrBC,OAAO,EAAEX,UAAU,CAACI,IAAI,CAAC,CAAC;MAC1BQ,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;IACpC,CAAC;IAEDxB,QAAQ,CAACZ,UAAU,CAAC4B,WAAW,CAAC,CAAC;IACjCJ,aAAa,CAAC,EAAE,CAAC;IACjBZ,QAAQ,CAACV,UAAU,CAAC,IAAI,CAAC,CAAC;IAE1B,IAAI;MACF;MACA,MAAMmC,QAAQ,GAAG,MAAMC,KAAK,CAAC,0CAA0C,EAAE;QACvEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,OAAO,EAAErB,UAAU,CAACI,IAAI,CAAC,CAAC;UAC1BkB,UAAU,EAAE7B,gBAAgB;UAC5B8B,QAAQ,EAAEzB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyB,QAAQ;UAChCC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACV,QAAQ,CAACW,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;;MAEA;MACA,IAAIxB,cAAc,CAACyB,OAAO,EAAE;QAC1BzB,cAAc,CAACyB,OAAO,CAACC,KAAK,CAAC,CAAC;MAChC;;MAEA;MACA,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,uDAAuDrC,gBAAgB,YAAYsC,kBAAkB,CAAC/B,UAAU,CAACI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;MAC/JF,cAAc,CAACyB,OAAO,GAAGE,WAAW;MAEpCxC,QAAQ,CAACV,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BU,QAAQ,CAACX,YAAY,CAAC,IAAI,CAAC,CAAC;MAE5B,IAAIsD,kBAAiC,GAAG,IAAI;MAC5C,IAAIC,gBAAgB,GAAG,EAAE;MAEzBJ,WAAW,CAACK,SAAS,GAAIC,KAAK,IAAK;QACjC,MAAMC,IAAI,GAAGjB,IAAI,CAACkB,KAAK,CAACF,KAAK,CAACC,IAAI,CAAC;QAEnC,QAAQA,IAAI,CAACD,KAAK;UAChB,KAAK,SAAS;YACZ;YACA;UAEF,KAAK,OAAO;YACV;YACAH,kBAAkB,GAAGzB,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YAC1CpB,QAAQ,CAACZ,UAAU,CAAC;cAClB6B,EAAE,EAAE0B,kBAAkB;cACtBtB,IAAI,EAAE,WAAW;cACjBC,OAAO,EAAE,EAAE;cACXC,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;YACpC,CAAC,CAAC,CAAC;YACH;UAEF,KAAK,MAAM;YACT;YACA,IAAIuB,IAAI,CAACA,IAAI,CAACE,IAAI,KAAK,WAAW,EAAE;cAClCjD,QAAQ,CAACT,eAAe,CAAC;gBACvB0D,IAAI,EAAE,MAAM;gBACZC,KAAK,EAAE,gBAAgB;gBACvBlB,OAAO,EAAE,aAAae,IAAI,CAACA,IAAI,CAACI,SAAS,KAAK;gBAC9CC,QAAQ,EAAE;cACZ,CAAC,CAAC,CAAC;YACL;YACA;UAEF,KAAK,UAAU;YACb;YACA,IAAIT,kBAAkB,IAAII,IAAI,CAACA,IAAI,CAACf,OAAO,EAAE;cAC3CY,gBAAgB,GAAGG,IAAI,CAACA,IAAI,CAACf,OAAO;cACpChC,QAAQ,CAACZ,UAAU,CAAC;gBAClB6B,EAAE,EAAE0B,kBAAkB;gBACtBtB,IAAI,EAAE,WAAW;gBACjBC,OAAO,EAAEsB,gBAAgB;gBACzBrB,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;cACpC,CAAC,CAAC,CAAC;YACL;YACA;UAEF,KAAK,UAAU;YACbxB,QAAQ,CAACX,YAAY,CAAC,KAAK,CAAC,CAAC;YAC7BmD,WAAW,CAACD,KAAK,CAAC,CAAC;YACnB;UAEF,KAAK,OAAO;YACVvC,QAAQ,CAACX,YAAY,CAAC,KAAK,CAAC,CAAC;YAC7BW,QAAQ,CAACT,eAAe,CAAC;cACvB0D,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE,YAAY;cACnBlB,OAAO,EAAEe,IAAI,CAACA,IAAI,CAACM,KAAK,IAAI,mBAAmB;cAC/CD,QAAQ,EAAE;YACZ,CAAC,CAAC,CAAC;YACHZ,WAAW,CAACD,KAAK,CAAC,CAAC;YACnB;QACJ;MACF,CAAC;MAEDC,WAAW,CAACc,OAAO,GAAG,MAAM;QAC1BtD,QAAQ,CAACX,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7BW,QAAQ,CAACT,eAAe,CAAC;UACvB0D,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,kBAAkB;UACzBlB,OAAO,EAAE,+BAA+B;UACxCoB,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;QACHZ,WAAW,CAACD,KAAK,CAAC,CAAC;MACrB,CAAC;IAEH,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdrD,QAAQ,CAACV,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BU,QAAQ,CAACT,eAAe,CAAC;QACvB0D,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdlB,OAAO,EAAE,wBAAwB;QACjCoB,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMG,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB7C,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI4B,cAAc,CAACyB,OAAO,EAAE;QAC1BzB,cAAc,CAACyB,OAAO,CAACC,KAAK,CAAC,CAAC;MAChC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIhC,gBAAgB,EAAE;IACpB,oBACEV,OAAA;MAAK+D,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnChE,OAAA;QAAK+D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BhE,OAAA,CAACF,IAAI;UAACmE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErE,OAAA;IAAK+D,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjChE,OAAA;MAAK+D,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhE,OAAA;QAAAgE,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACZzD,YAAY,iBACXZ,OAAA;QAAK+D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtChE,OAAA;UAAAgE,QAAA,GAAM,eAAG,EAACpD,YAAY,CAACyC,KAAK;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrE,OAAA;MAAK+D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhE,OAAA,CAACL,WAAW;QAACS,QAAQ,EAAEA;MAAS;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCrE,OAAA,CAACH,cAAc;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENrE,OAAA;MAAK+D,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BhE,OAAA,CAACJ,YAAY;QACX0E,KAAK,EAAExD,UAAW;QAClByD,QAAQ,EAAExD,aAAc;QACxByD,MAAM,EAAEvD,iBAAkB;QAC1BwD,UAAU,EAAEf,cAAe;QAC3BgB,QAAQ,EAAErE,WAAW,IAAIC,SAAU;QACnCqE,WAAW,EAAC;MAA0C;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnE,EAAA,CAlMID,SAAmB;EAAA,QACNZ,WAAW,EACmCC,WAAW,EAC7CA,WAAW,EACfA,WAAW;AAAA;AAAAsF,EAAA,GAJhC3E,SAAmB;AAoMzB,eAAeA,SAAS;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}