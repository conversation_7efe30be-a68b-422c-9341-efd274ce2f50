{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setPapers, setCurrentPaper, setSearchQuery } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { clearMessages } from '../../store/slices/chatSlice';\nimport { Search, Plus, FileText } from 'lucide-react';\nimport api from '../../services/api';\nimport './PaperList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PaperList = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    papers,\n    filteredPapers,\n    currentPaper,\n    searchQuery,\n    isLoading\n  } = useSelector(state => state.papers);\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);\n  const loadPapers = useCallback(async () => {\n    console.log('🔄 Loading papers from API...');\n    try {\n      // Use the API service\n      const result = await api.papers.list();\n      console.log('📄 API result:', result);\n      if (result.data && result.data.papers) {\n        console.log('✅ Papers loaded:', result.data.papers);\n        dispatch(setPapers(result.data.papers));\n      } else if (result.error) {\n        console.error('❌ API error:', result.error);\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Error',\n          message: result.error,\n          duration: 5000\n        }));\n      } else {\n        console.error('❌ Unexpected API response:', result);\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Error',\n          message: 'Unexpected response from server',\n          duration: 5000\n        }));\n      }\n    } catch (error) {\n      console.error('💥 Exception during API call:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: `Failed to load papers: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000\n      }));\n    }\n  }, [dispatch]);\n  useEffect(() => {\n    // Load papers on component mount\n    loadPapers();\n  }, [loadPapers]);\n  const handleSearchChange = e => {\n    const query = e.target.value;\n    setLocalSearchQuery(query);\n    dispatch(setSearchQuery(query));\n  };\n  const handlePaperSelect = async paper => {\n    try {\n      // Clear chat messages when switching papers (for paper-specific context)\n      dispatch(clearMessages());\n\n      // First set the paper with basic info\n      dispatch(setCurrentPaper(paper));\n\n      // Then fetch the full paper content\n      const result = await api.papers.get(paper.paper_id);\n      if (result.data) {\n        // Update with full content\n        dispatch(setCurrentPaper(result.data));\n      } else {\n        throw new Error(result.error || 'Failed to load paper content');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to load paper content',\n        duration: 5000\n      }));\n    }\n  };\n  const handleCreatePaper = async () => {\n    try {\n      const title = `New Paper ${new Date().toLocaleDateString()}`;\n      const content = `# ${title}\\n\\n## Introduction\\n\\nStart writing your paper here...`;\n      const result = await api.papers.create(title, content);\n      if (result.data) {\n        loadPapers(); // Reload papers list\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Created',\n          message: `Created \"${title}\"`,\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create paper');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to create paper',\n        duration: 5000\n      }));\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paper-list\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paper-list-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"paper-list-title\",\n        children: \"Papers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paper-search-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 16,\n            className: \"search-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search papers...\",\n            value: localSearchQuery,\n            onChange: handleSearchChange,\n            className: \"paper-search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreatePaper,\n        className: \"create-paper-button\",\n        title: \"Create new paper\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"New Paper\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"paper-list-content\",\n      children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Loading papers...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this) : filteredPapers.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: papers.length === 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            size: 48,\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"No Papers Yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Create your first research paper to get started.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCreatePaper,\n            className: \"create-first-paper\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this), \"Create First Paper\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            size: 48,\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"No Results Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No papers match your search query.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"papers-grid\",\n        children: filteredPapers.map(paper => {\n          var _paper$metadata;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `paper-item ${(currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id) === paper.paper_id ? 'active' : ''}`,\n            onClick: () => handlePaperSelect(paper),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"paper-item-title\",\n                children: paper.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `paper-item-status ${paper.status}`,\n                children: paper.status.replace('_', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-content\",\n              children: paper.content && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"paper-item-preview\",\n                children: [paper.content.replace(/[#*`]/g, '').substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"paper-item-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"paper-item-date\",\n                children: [\"Updated \", formatDate(paper.updated_at)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), ((_paper$metadata = paper.metadata) === null || _paper$metadata === void 0 ? void 0 : _paper$metadata.word_count) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"paper-item-words\",\n                children: [paper.metadata.word_count, \" words\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, paper.paper_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperList, \"udYG1k5LcobjNYABHmt/6Wa4ZZc=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = PaperList;\nexport default PaperList;\nvar _c;\n$RefreshReg$(_c, \"PaperList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "useSelector", "setPapers", "setCurrentPaper", "setSearch<PERSON>uery", "addNotification", "clearMessages", "Search", "Plus", "FileText", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PaperList", "_s", "dispatch", "papers", "filteredPapers", "currentPaper", "searchQuery", "isLoading", "state", "localSearchQuery", "setLocalSearchQuery", "loadPapers", "console", "log", "result", "list", "data", "error", "type", "title", "message", "duration", "Error", "handleSearchChange", "e", "query", "target", "value", "handlePaperSelect", "paper", "get", "paper_id", "handleCreatePaper", "Date", "toLocaleDateString", "content", "create", "formatDate", "dateString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "placeholder", "onChange", "onClick", "length", "map", "_paper$metadata", "status", "replace", "substring", "updated_at", "metadata", "word_count", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { setPapers, setCurrentPaper, setSearchQuery } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { clearMessages } from '../../store/slices/chatSlice';\nimport { Search, Plus, FileText } from 'lucide-react';\nimport api from '../../services/api';\nimport './PaperList.css';\n\nconst PaperList: React.FC = () => {\n  const dispatch = useDispatch();\n  const { papers, filteredPapers, currentPaper, searchQuery, isLoading } = useSelector((state: RootState) => state.papers);\n  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);\n\n  const loadPapers = useCallback(async () => {\n    console.log('🔄 Loading papers from API...');\n\n    try {\n      // Use the API service\n      const result = await api.papers.list();\n      console.log('📄 API result:', result);\n\n      if (result.data && result.data.papers) {\n        console.log('✅ Papers loaded:', result.data.papers);\n        dispatch(setPapers(result.data.papers));\n      } else if (result.error) {\n        console.error('❌ API error:', result.error);\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Error',\n          message: result.error,\n          duration: 5000,\n        }));\n      } else {\n        console.error('❌ Unexpected API response:', result);\n        dispatch(addNotification({\n          type: 'error',\n          title: 'Error',\n          message: 'Unexpected response from server',\n          duration: 5000,\n        }));\n      }\n    } catch (error) {\n      console.error('💥 Exception during API call:', error);\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: `Failed to load papers: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        duration: 5000,\n      }));\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Load papers on component mount\n    loadPapers();\n  }, [loadPapers]);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value;\n    setLocalSearchQuery(query);\n    dispatch(setSearchQuery(query));\n  };\n\n  const handlePaperSelect = async (paper: any) => {\n    try {\n      // Clear chat messages when switching papers (for paper-specific context)\n      dispatch(clearMessages());\n\n      // First set the paper with basic info\n      dispatch(setCurrentPaper(paper));\n\n      // Then fetch the full paper content\n      const result = await api.papers.get(paper.paper_id);\n      if (result.data) {\n        // Update with full content\n        dispatch(setCurrentPaper(result.data));\n      } else {\n        throw new Error(result.error || 'Failed to load paper content');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to load paper content',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleCreatePaper = async () => {\n    try {\n      const title = `New Paper ${new Date().toLocaleDateString()}`;\n      const content = `# ${title}\\n\\n## Introduction\\n\\nStart writing your paper here...`;\n\n      const result = await api.papers.create(title, content);\n\n      if (result.data) {\n        loadPapers(); // Reload papers list\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Created',\n          message: `Created \"${title}\"`,\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to create paper');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to create paper',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <div className=\"paper-list\">\n      <div className=\"paper-list-header\">\n        <h3 className=\"paper-list-title\">Papers</h3>\n\n        <div className=\"paper-search-container\">\n          <div className=\"search-input-wrapper\">\n            <Search size={16} className=\"search-icon\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search papers...\"\n              value={localSearchQuery}\n              onChange={handleSearchChange}\n              className=\"paper-search\"\n            />\n          </div>\n        </div>\n\n        <button\n          onClick={handleCreatePaper}\n          className=\"create-paper-button\"\n          title=\"Create new paper\"\n        >\n          <Plus size={16} />\n          <span>New Paper</span>\n        </button>\n      </div>\n\n      <div className=\"paper-list-content\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <span>Loading papers...</span>\n          </div>\n        ) : filteredPapers.length === 0 ? (\n          <div className=\"empty-state\">\n            {papers.length === 0 ? (\n              <>\n                <FileText size={48} className=\"empty-icon\" />\n                <h4>No Papers Yet</h4>\n                <p>Create your first research paper to get started.</p>\n                <button onClick={handleCreatePaper} className=\"create-first-paper\">\n                  <Plus size={16} />\n                  Create First Paper\n                </button>\n              </>\n            ) : (\n              <>\n                <Search size={48} className=\"empty-icon\" />\n                <h4>No Results Found</h4>\n                <p>No papers match your search query.</p>\n              </>\n            )}\n          </div>\n        ) : (\n          <div className=\"papers-grid\">\n            {filteredPapers.map((paper) => (\n              <div\n                key={paper.paper_id}\n                className={`paper-item ${currentPaper?.paper_id === paper.paper_id ? 'active' : ''}`}\n                onClick={() => handlePaperSelect(paper)}\n              >\n                <div className=\"paper-item-header\">\n                  <h4 className=\"paper-item-title\">{paper.title}</h4>\n                  <span className={`paper-item-status ${paper.status}`}>\n                    {paper.status.replace('_', ' ')}\n                  </span>\n                </div>\n\n                <div className=\"paper-item-content\">\n                  {paper.content && (\n                    <p className=\"paper-item-preview\">\n                      {paper.content.replace(/[#*`]/g, '').substring(0, 100)}...\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"paper-item-meta\">\n                  <span className=\"paper-item-date\">\n                    Updated {formatDate(paper.updated_at)}\n                  </span>\n                  {paper.metadata?.word_count && (\n                    <span className=\"paper-item-words\">\n                      {paper.metadata.word_count} words\n                    </span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PaperList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,SAAS,EAAEC,eAAe,EAAEC,cAAc,QAAQ,gCAAgC;AAC3F,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACrD,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,MAAM;IAAEC,cAAc;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAU,CAAC,GAAGrB,WAAW,CAAEsB,KAAgB,IAAKA,KAAK,CAACL,MAAM,CAAC;EACxH,MAAM,CAACM,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAACwB,WAAW,CAAC;EAErE,MAAMK,UAAU,GAAG3B,WAAW,CAAC,YAAY;IACzC4B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAE5C,IAAI;MACF;MACA,MAAMC,MAAM,GAAG,MAAMnB,GAAG,CAACQ,MAAM,CAACY,IAAI,CAAC,CAAC;MACtCH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,MAAM,CAAC;MAErC,IAAIA,MAAM,CAACE,IAAI,IAAIF,MAAM,CAACE,IAAI,CAACb,MAAM,EAAE;QACrCS,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,MAAM,CAACE,IAAI,CAACb,MAAM,CAAC;QACnDD,QAAQ,CAACf,SAAS,CAAC2B,MAAM,CAACE,IAAI,CAACb,MAAM,CAAC,CAAC;MACzC,CAAC,MAAM,IAAIW,MAAM,CAACG,KAAK,EAAE;QACvBL,OAAO,CAACK,KAAK,CAAC,cAAc,EAAEH,MAAM,CAACG,KAAK,CAAC;QAC3Cf,QAAQ,CAACZ,eAAe,CAAC;UACvB4B,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,OAAO;UACdC,OAAO,EAAEN,MAAM,CAACG,KAAK;UACrBI,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLT,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEH,MAAM,CAAC;QACnDZ,QAAQ,CAACZ,eAAe,CAAC;UACvB4B,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,OAAO;UACdC,OAAO,EAAE,iCAAiC;UAC1CC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDf,QAAQ,CAACZ,eAAe,CAAC;QACvB4B,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,0BAA0BH,KAAK,YAAYK,KAAK,GAAGL,KAAK,CAACG,OAAO,GAAG,eAAe,EAAE;QAC7FC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;EAEdnB,SAAS,CAAC,MAAM;IACd;IACA4B,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMY,kBAAkB,GAAIC,CAAsC,IAAK;IACrE,MAAMC,KAAK,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IAC5BjB,mBAAmB,CAACe,KAAK,CAAC;IAC1BvB,QAAQ,CAACb,cAAc,CAACoC,KAAK,CAAC,CAAC;EACjC,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOC,KAAU,IAAK;IAC9C,IAAI;MACF;MACA3B,QAAQ,CAACX,aAAa,CAAC,CAAC,CAAC;;MAEzB;MACAW,QAAQ,CAACd,eAAe,CAACyC,KAAK,CAAC,CAAC;;MAEhC;MACA,MAAMf,MAAM,GAAG,MAAMnB,GAAG,CAACQ,MAAM,CAAC2B,GAAG,CAACD,KAAK,CAACE,QAAQ,CAAC;MACnD,IAAIjB,MAAM,CAACE,IAAI,EAAE;QACf;QACAd,QAAQ,CAACd,eAAe,CAAC0B,MAAM,CAACE,IAAI,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAACR,MAAM,CAACG,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdf,QAAQ,CAACZ,eAAe,CAAC;QACvB4B,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,8BAA8B;QACvCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMW,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMb,KAAK,GAAG,aAAa,IAAIc,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC5D,MAAMC,OAAO,GAAG,KAAKhB,KAAK,yDAAyD;MAEnF,MAAML,MAAM,GAAG,MAAMnB,GAAG,CAACQ,MAAM,CAACiC,MAAM,CAACjB,KAAK,EAAEgB,OAAO,CAAC;MAEtD,IAAIrB,MAAM,CAACE,IAAI,EAAE;QACfL,UAAU,CAAC,CAAC,CAAC,CAAC;QACdT,QAAQ,CAACZ,eAAe,CAAC;UACvB4B,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,eAAe;UACtBC,OAAO,EAAE,YAAYD,KAAK,GAAG;UAC7BE,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,MAAM,CAACG,KAAK,IAAI,wBAAwB,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdf,QAAQ,CAACZ,eAAe,CAAC;QACvB4B,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,wBAAwB;QACjCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMgB,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAIL,IAAI,CAACK,UAAU,CAAC,CAACJ,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACErC,OAAA;IAAK0C,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzB3C,OAAA;MAAK0C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3C,OAAA;QAAI0C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5C/C,OAAA;QAAK0C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC3C,OAAA;UAAK0C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC3C,OAAA,CAACL,MAAM;YAACqD,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C/C,OAAA;YACEqB,IAAI,EAAC,MAAM;YACX4B,WAAW,EAAC,kBAAkB;YAC9BnB,KAAK,EAAElB,gBAAiB;YACxBsC,QAAQ,EAAExB,kBAAmB;YAC7BgB,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA;QACEmD,OAAO,EAAEhB,iBAAkB;QAC3BO,SAAS,EAAC,qBAAqB;QAC/BpB,KAAK,EAAC,kBAAkB;QAAAqB,QAAA,gBAExB3C,OAAA,CAACJ,IAAI;UAACoD,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClB/C,OAAA;UAAA2C,QAAA,EAAM;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/C,OAAA;MAAK0C,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCjC,SAAS,gBACRV,OAAA;QAAK0C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B3C,OAAA;UAAK0C,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/C,OAAA;UAAA2C,QAAA,EAAM;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,GACJxC,cAAc,CAAC6C,MAAM,KAAK,CAAC,gBAC7BpD,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBrC,MAAM,CAAC8C,MAAM,KAAK,CAAC,gBAClBpD,OAAA,CAAAE,SAAA;UAAAyC,QAAA,gBACE3C,OAAA,CAACH,QAAQ;YAACmD,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7C/C,OAAA;YAAA2C,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB/C,OAAA;YAAA2C,QAAA,EAAG;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvD/C,OAAA;YAAQmD,OAAO,EAAEhB,iBAAkB;YAACO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAChE3C,OAAA,CAACJ,IAAI;cAACoD,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEH/C,OAAA,CAAAE,SAAA;UAAAyC,QAAA,gBACE3C,OAAA,CAACL,MAAM;YAACqD,IAAI,EAAE,EAAG;YAACN,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C/C,OAAA;YAAA2C,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB/C,OAAA;YAAA2C,QAAA,EAAG;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA,eACzC;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAEN/C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBpC,cAAc,CAAC8C,GAAG,CAAErB,KAAK;UAAA,IAAAsB,eAAA;UAAA,oBACxBtD,OAAA;YAEE0C,SAAS,EAAE,cAAc,CAAAlC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,MAAKF,KAAK,CAACE,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrFiB,OAAO,EAAEA,CAAA,KAAMpB,iBAAiB,CAACC,KAAK,CAAE;YAAAW,QAAA,gBAExC3C,OAAA;cAAK0C,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3C,OAAA;gBAAI0C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEX,KAAK,CAACV;cAAK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnD/C,OAAA;gBAAM0C,SAAS,EAAE,qBAAqBV,KAAK,CAACuB,MAAM,EAAG;gBAAAZ,QAAA,EAClDX,KAAK,CAACuB,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN/C,OAAA;cAAK0C,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAChCX,KAAK,CAACM,OAAO,iBACZtC,OAAA;gBAAG0C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAC9BX,KAAK,CAACM,OAAO,CAACkB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACzD;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACJ;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN/C,OAAA;cAAK0C,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B3C,OAAA;gBAAM0C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,UACxB,EAACH,UAAU,CAACR,KAAK,CAAC0B,UAAU,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,EACN,EAAAO,eAAA,GAAAtB,KAAK,CAAC2B,QAAQ,cAAAL,eAAA,uBAAdA,eAAA,CAAgBM,UAAU,kBACzB5D,OAAA;gBAAM0C,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAC/BX,KAAK,CAAC2B,QAAQ,CAACC,UAAU,EAAC,QAC7B;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GA5BDf,KAAK,CAACE,QAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6BhB,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA/MID,SAAmB;EAAA,QACNf,WAAW,EAC6CC,WAAW;AAAA;AAAAwE,EAAA,GAFhF1D,SAAmB;AAiNzB,eAAeA,SAAS;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}