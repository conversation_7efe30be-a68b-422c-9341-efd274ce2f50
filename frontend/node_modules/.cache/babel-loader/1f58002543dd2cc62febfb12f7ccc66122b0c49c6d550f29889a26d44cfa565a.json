{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { updateCurrentPaperContent, setCursorPosition as setCursorPositionAction } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaperEditor = ({\n  paper\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [lastSavedContent, setLastSavedContent] = useState(paper.content);\n  const autoSaveTimeoutRef = useRef(null);\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  // Save before page unload\n  useEffect(() => {\n    const handleBeforeUnload = event => {\n      if (hasUnsavedChanges) {\n        // Try to save before leaving\n        handleSave();\n\n        // Show confirmation dialog\n        event.preventDefault();\n        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';\n        return event.returnValue;\n      }\n    };\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [hasUnsavedChanges, handleSave]);\n\n  // Cleanup timeout on unmount\n  useEffect(() => {\n    return () => {\n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Auto-save function - uses the existing save logic\n  const autoSave = useCallback(async () => {\n    if (!hasUnsavedChanges || isSaving) {\n      return; // No changes to save or already saving\n    }\n    try {\n      console.log('Auto-saving...');\n      await handleSave();\n      console.log('Auto-saved successfully');\n    } catch (error) {\n      console.error('Auto-save error:', error);\n    }\n  }, [hasUnsavedChanges, isSaving, handleSave]);\n  const handleContentChange = useCallback(value => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n\n    // Mark as having unsaved changes\n    setHasUnsavedChanges(newContent !== lastSavedContent);\n\n    // Clear existing timeout\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    // Set new timeout for auto-save (3 seconds after user stops typing)\n    autoSaveTimeoutRef.current = setTimeout(() => {\n      autoSave();\n    }, 3000);\n  }, [dispatch, lastSavedContent, autoSave]);\n  const handleCursorPositionChange = useCallback(editor => {\n    const position = editor.getPosition();\n    if (position) {\n      const newPosition = {\n        line: position.lineNumber,\n        column: position.column\n      };\n      dispatch(setCursorPositionAction(newPosition));\n    }\n  }, [dispatch]);\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    // Clear any pending auto-save\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n      if (result.data) {\n        setLastSavedContent(content);\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleExport = () => {\n    const blob = new Blob([content], {\n      type: 'text/markdown'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n  const renderPreview = () => {\n    const contentToRender = content || '';\n    const html = marked.parse(contentToRender, {\n      async: false\n    });\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: sanitizedHtml\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 12\n    }, this);\n  };\n  const getWordCount = () => {\n    if (!content) return 0;\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave, toggleView]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `paper-editor ${isFullscreen ? 'fullscreen' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: paper.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"word-count\",\n            children: [getWordCount(), \" words\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-indicator\",\n            children: hasUnsavedChanges ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unsaved\",\n              children: \"\\u25CF Unsaved changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"saved\",\n              children: \"\\u2713 Saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleView,\n          className: \"editor-button\",\n          title: isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode',\n          children: [isSplitView ? /*#__PURE__*/_jsxDEV(Split, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 28\n          }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 66\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 89\n          }, this), isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleFullscreen,\n          className: \"editor-button\",\n          title: isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen',\n          children: [/*#__PURE__*/_jsxDEV(Maximize2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), isFullscreen ? 'Exit' : 'Fullscreen']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: !hasUnsavedChanges || isSaving,\n          className: \"editor-button save-button\",\n          title: \"Save changes (Ctrl+S)\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), isSaving ? 'Saving...' : 'Save']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          className: \"editor-button\",\n          title: \"Export as Markdown\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: isSplitView ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"split-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-pane\",\n          children: /*#__PURE__*/_jsxDEV(Editor, {\n            height: \"100%\",\n            defaultLanguage: \"markdown\",\n            value: content,\n            onChange: handleContentChange,\n            onMount: editor => {\n              // Track cursor position changes\n              editor.onDidChangeCursorPosition(() => {\n                handleCursorPositionChange(editor);\n              });\n            },\n            theme: \"vs-light\",\n            options: {\n              minimap: {\n                enabled: false\n              },\n              wordWrap: 'on',\n              lineNumbers: 'on',\n              scrollBeyondLastLine: false,\n              automaticLayout: true,\n              fontSize: 14,\n              fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n              padding: {\n                top: 16,\n                bottom: 16\n              },\n              suggest: {\n                showKeywords: false,\n                showSnippets: false\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-pane\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"markdown-content\",\n            children: renderPreview()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-pane full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-content\",\n          children: renderPreview()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"edit-pane full\",\n        children: /*#__PURE__*/_jsxDEV(Editor, {\n          height: \"100%\",\n          defaultLanguage: \"markdown\",\n          value: content,\n          onChange: handleContentChange,\n          onMount: editor => {\n            // Track cursor position changes\n            editor.onDidChangeCursorPosition(() => {\n              handleCursorPositionChange(editor);\n            });\n          },\n          theme: \"vs-light\",\n          options: {\n            minimap: {\n              enabled: true\n            },\n            wordWrap: 'on',\n            lineNumbers: 'on',\n            scrollBeyondLastLine: false,\n            automaticLayout: true,\n            fontSize: 16,\n            fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n            padding: {\n              top: 20,\n              bottom: 20\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Characters: \", (content === null || content === void 0 ? void 0 : content.length) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Words: \", getWordCount()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Lines: \", (content === null || content === void 0 ? void 0 : content.split('\\n').length) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-help\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Markdown supported \\u2022 Ctrl+S to save \\u2022 Ctrl+P to toggle view\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperEditor, \"aKp8pJuCBscmjcCpj9OrQ+0W4XE=\", false, function () {\n  return [useDispatch];\n});\n_c = PaperEditor;\nexport default PaperEditor;\nvar _c;\n$RefreshReg$(_c, \"PaperEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useDispatch", "updateCurrentPaperContent", "setCursorPosition", "setCursorPositionAction", "addNotification", "Save", "Eye", "Eye<PERSON>ff", "Download", "Split", "Maximize2", "marked", "DOMPurify", "Editor", "api", "jsxDEV", "_jsxDEV", "PaperEditor", "paper", "_s", "dispatch", "content", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewMode", "setIsPreviewMode", "isSplitView", "setIsSplitView", "isSaving", "setIsSaving", "hasUnsavedChanges", "setHasUnsavedChanges", "isFullscreen", "setIsFullscreen", "lastSavedContent", "setLastSavedContent", "autoSaveTimeoutRef", "paper_id", "handleBeforeUnload", "event", "handleSave", "preventDefault", "returnValue", "window", "addEventListener", "removeEventListener", "current", "clearTimeout", "autoSave", "console", "log", "error", "handleContentChange", "value", "newContent", "setTimeout", "handleCursorPositionChange", "editor", "position", "getPosition", "newPosition", "line", "lineNumber", "column", "result", "papers", "update", "title", "data", "type", "message", "duration", "Error", "handleExport", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "toLowerCase", "click", "revokeObjectURL", "renderPreview", "contentToRender", "html", "parse", "async", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "columnNumber", "getWordCount", "trim", "split", "filter", "word", "length", "to<PERSON><PERSON><PERSON><PERSON>", "toggleFullscreen", "timer", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "key", "className", "children", "onClick", "size", "disabled", "height", "defaultLanguage", "onChange", "onMount", "onDidChangeCursorPosition", "theme", "options", "minimap", "enabled", "wordWrap", "lineNumbers", "scrollBeyondLastLine", "automaticLayout", "fontSize", "fontFamily", "padding", "top", "bottom", "suggest", "showKeywords", "showSnippets", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Paper, updateCurrentPaperContent, setCursorPosition as setCursorPositionAction } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\n\ninterface PaperEditorProps {\n  paper: Paper;\n}\n\nconst PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [lastSavedContent, setLastSavedContent] = useState(paper.content);\n  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  // Save before page unload\n  useEffect(() => {\n    const handleBeforeUnload = (event: BeforeUnloadEvent) => {\n      if (hasUnsavedChanges) {\n        // Try to save before leaving\n        handleSave();\n\n        // Show confirmation dialog\n        event.preventDefault();\n        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';\n        return event.returnValue;\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [hasUnsavedChanges, handleSave]);\n\n  // Cleanup timeout on unmount\n  useEffect(() => {\n    return () => {\n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  // Auto-save function - uses the existing save logic\n  const autoSave = useCallback(async () => {\n    if (!hasUnsavedChanges || isSaving) {\n      return; // No changes to save or already saving\n    }\n\n    try {\n      console.log('Auto-saving...');\n      await handleSave();\n      console.log('Auto-saved successfully');\n    } catch (error) {\n      console.error('Auto-save error:', error);\n    }\n  }, [hasUnsavedChanges, isSaving, handleSave]);\n\n  const handleContentChange = useCallback((value: string | undefined) => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n\n    // Mark as having unsaved changes\n    setHasUnsavedChanges(newContent !== lastSavedContent);\n\n    // Clear existing timeout\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    // Set new timeout for auto-save (3 seconds after user stops typing)\n    autoSaveTimeoutRef.current = setTimeout(() => {\n      autoSave();\n    }, 3000);\n  }, [dispatch, lastSavedContent, autoSave]);\n\n  const handleCursorPositionChange = useCallback((editor: any) => {\n    const position = editor.getPosition();\n    if (position) {\n      const newPosition = { line: position.lineNumber, column: position.column };\n      dispatch(setCursorPositionAction(newPosition));\n    }\n  }, [dispatch]);\n\n\n\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    // Clear any pending auto-save\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n\n      if (result.data) {\n        setLastSavedContent(content);\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const renderPreview = () => {\n    const contentToRender = content || '';\n    const html = marked.parse(contentToRender, { async: false }) as string;\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n  };\n\n  const getWordCount = () => {\n    if (!content) return 0;\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave, toggleView]);\n\n  return (\n    <div className={`paper-editor ${isFullscreen ? 'fullscreen' : ''}`}>\n      <div className=\"editor-header\">\n        <div className=\"editor-title\">\n          <h2>{paper.title}</h2>\n          <div className=\"editor-meta\">\n            <span className=\"word-count\">{getWordCount()} words</span>\n            <span className=\"status-indicator\">\n              {hasUnsavedChanges ? (\n                <span className=\"unsaved\">● Unsaved changes</span>\n              ) : (\n                <span className=\"saved\">✓ Saved</span>\n              )}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"editor-actions\">\n          <button\n            onClick={toggleView}\n            className=\"editor-button\"\n            title={isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode'}\n          >\n            {isSplitView ? <Split size={16} /> : isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}\n            {isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview'}\n          </button>\n\n          <button\n            onClick={toggleFullscreen}\n            className=\"editor-button\"\n            title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}\n          >\n            <Maximize2 size={16} />\n            {isFullscreen ? 'Exit' : 'Fullscreen'}\n          </button>\n\n          <button\n            onClick={handleSave}\n            disabled={!hasUnsavedChanges || isSaving}\n            className=\"editor-button save-button\"\n            title=\"Save changes (Ctrl+S)\"\n          >\n            <Save size={16} />\n            {isSaving ? 'Saving...' : 'Save'}\n          </button>\n\n          <button\n            onClick={handleExport}\n            className=\"editor-button\"\n            title=\"Export as Markdown\"\n          >\n            <Download size={16} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      <div className=\"editor-content\">\n        {isSplitView ? (\n          <div className=\"split-view\">\n            <div className=\"edit-pane\">\n              <Editor\n                height=\"100%\"\n                defaultLanguage=\"markdown\"\n                value={content}\n                onChange={handleContentChange}\n                onMount={(editor) => {\n                  // Track cursor position changes\n                  editor.onDidChangeCursorPosition(() => {\n                    handleCursorPositionChange(editor);\n                  });\n                }}\n                theme=\"vs-light\"\n                options={{\n                  minimap: { enabled: false },\n                  wordWrap: 'on',\n                  lineNumbers: 'on',\n                  scrollBeyondLastLine: false,\n                  automaticLayout: true,\n                  fontSize: 14,\n                  fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                  padding: { top: 16, bottom: 16 },\n                  suggest: {\n                    showKeywords: false,\n                    showSnippets: false,\n                  },\n                }}\n              />\n            </div>\n            <div className=\"preview-pane\">\n              <div className=\"markdown-content\">\n                {renderPreview()}\n              </div>\n            </div>\n          </div>\n        ) : isPreviewMode ? (\n          <div className=\"preview-pane full\">\n            <div className=\"markdown-content\">\n              {renderPreview()}\n            </div>\n          </div>\n        ) : (\n          <div className=\"edit-pane full\">\n            <Editor\n              height=\"100%\"\n              defaultLanguage=\"markdown\"\n              value={content}\n              onChange={handleContentChange}\n              onMount={(editor) => {\n                // Track cursor position changes\n                editor.onDidChangeCursorPosition(() => {\n                  handleCursorPositionChange(editor);\n                });\n              }}\n              theme=\"vs-light\"\n              options={{\n                minimap: { enabled: true },\n                wordWrap: 'on',\n                lineNumbers: 'on',\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n                fontSize: 16,\n                fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                padding: { top: 20, bottom: 20 },\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"editor-footer\">\n        <div className=\"editor-stats\">\n          <span>Characters: {content?.length || 0}</span>\n          <span>Words: {getWordCount()}</span>\n          <span>Lines: {content?.split('\\n').length || 0}</span>\n        </div>\n\n        <div className=\"editor-help\">\n          <span>Markdown supported • Ctrl+S to save • Ctrl+P to toggle view</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaperEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAAgBC,yBAAyB,EAAEC,iBAAiB,IAAIC,uBAAuB,QAAQ,gCAAgC;AAC/H,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,cAAc;AAC5E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAACsB,KAAK,CAACG,OAAO,CAAC;EACrD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAACsB,KAAK,CAACG,OAAO,CAAC;EACvE,MAAMc,kBAAkB,GAAGpC,MAAM,CAAwB,IAAI,CAAC;EAE9DF,SAAS,CAAC,MAAM;IACdyB,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC;IACzBS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACZ,KAAK,CAACkB,QAAQ,EAAElB,KAAK,CAACG,OAAO,CAAC,CAAC;EAEnCxB,SAAS,CAAC,MAAM;IACdiC,oBAAoB,CAACT,OAAO,KAAKH,KAAK,CAACG,OAAO,CAAC;EACjD,CAAC,EAAE,CAACA,OAAO,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC;;EAE5B;EACAxB,SAAS,CAAC,MAAM;IACd,MAAMwC,kBAAkB,GAAIC,KAAwB,IAAK;MACvD,IAAIT,iBAAiB,EAAE;QACrB;QACAU,UAAU,CAAC,CAAC;;QAEZ;QACAD,KAAK,CAACE,cAAc,CAAC,CAAC;QACtBF,KAAK,CAACG,WAAW,GAAG,2DAA2D;QAC/E,OAAOH,KAAK,CAACG,WAAW;MAC1B;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEN,kBAAkB,CAAC;IAE3D,OAAO,MAAM;MACXK,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEP,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACR,iBAAiB,EAAEU,UAAU,CAAC,CAAC;;EAEnC;EACA1C,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIsC,kBAAkB,CAACU,OAAO,EAAE;QAC9BC,YAAY,CAACX,kBAAkB,CAACU,OAAO,CAAC;MAC1C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,QAAQ,GAAGjD,WAAW,CAAC,YAAY;IACvC,IAAI,CAAC+B,iBAAiB,IAAIF,QAAQ,EAAE;MAClC,OAAO,CAAC;IACV;IAEA,IAAI;MACFqB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,MAAMV,UAAU,CAAC,CAAC;MAClBS,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC,EAAE,CAACrB,iBAAiB,EAAEF,QAAQ,EAAEY,UAAU,CAAC,CAAC;EAE7C,MAAMY,mBAAmB,GAAGrD,WAAW,CAAEsD,KAAyB,IAAK;IACrE,MAAMC,UAAU,GAAGD,KAAK,IAAI,EAAE;IAC9B9B,UAAU,CAAC+B,UAAU,CAAC;IACtBjC,QAAQ,CAACnB,yBAAyB,CAACoD,UAAU,CAAC,CAAC;;IAE/C;IACAvB,oBAAoB,CAACuB,UAAU,KAAKpB,gBAAgB,CAAC;;IAErD;IACA,IAAIE,kBAAkB,CAACU,OAAO,EAAE;MAC9BC,YAAY,CAACX,kBAAkB,CAACU,OAAO,CAAC;IAC1C;;IAEA;IACAV,kBAAkB,CAACU,OAAO,GAAGS,UAAU,CAAC,MAAM;MAC5CP,QAAQ,CAAC,CAAC;IACZ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAAC3B,QAAQ,EAAEa,gBAAgB,EAAEc,QAAQ,CAAC,CAAC;EAE1C,MAAMQ,0BAA0B,GAAGzD,WAAW,CAAE0D,MAAW,IAAK;IAC9D,MAAMC,QAAQ,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;IACrC,IAAID,QAAQ,EAAE;MACZ,MAAME,WAAW,GAAG;QAAEC,IAAI,EAAEH,QAAQ,CAACI,UAAU;QAAEC,MAAM,EAAEL,QAAQ,CAACK;MAAO,CAAC;MAC1E1C,QAAQ,CAACjB,uBAAuB,CAACwD,WAAW,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,CAACvC,QAAQ,CAAC,CAAC;EAId,MAAMmB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACV,iBAAiB,EAAE;;IAExB;IACA,IAAIM,kBAAkB,CAACU,OAAO,EAAE;MAC9BC,YAAY,CAACX,kBAAkB,CAACU,OAAO,CAAC;IAC1C;IAEAjB,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMmC,MAAM,GAAG,MAAMjD,GAAG,CAACkD,MAAM,CAACC,MAAM,CAAC/C,KAAK,CAACkB,QAAQ,EAAElB,KAAK,CAACgD,KAAK,EAAE7C,OAAO,CAAC;MAE5E,IAAI0C,MAAM,CAACI,IAAI,EAAE;QACfjC,mBAAmB,CAACb,OAAO,CAAC;QAC5BS,oBAAoB,CAAC,KAAK,CAAC;QAC3BV,QAAQ,CAAChB,eAAe,CAAC;UACvBgE,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,aAAa;UACpBG,OAAO,EAAE,4CAA4C;UACrDC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,MAAM,CAACb,KAAK,IAAI,gBAAgB,CAAC;MACnD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd9B,QAAQ,CAAChB,eAAe,CAAC;QACvBgE,IAAI,EAAE,OAAO;QACbF,KAAK,EAAE,aAAa;QACpBG,OAAO,EAAE,gDAAgD;QACzDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR1C,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACrD,OAAO,CAAC,EAAE;MAAE+C,IAAI,EAAE;IAAgB,CAAC,CAAC;IAC3D,MAAMO,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAGhE,KAAK,CAACgD,KAAK,CAACiB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK;IAC7EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZT,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,eAAe,GAAGnE,OAAO,IAAI,EAAE;IACrC,MAAMoE,IAAI,GAAG9E,MAAM,CAAC+E,KAAK,CAACF,eAAe,EAAE;MAAEG,KAAK,EAAE;IAAM,CAAC,CAAW;IACtE,MAAMC,aAAa,GAAGhF,SAAS,CAACiF,QAAQ,CAACJ,IAAI,CAAC;IAC9C,oBAAOzE,OAAA;MAAK8E,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAc;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAApC,UAAA;MAAAqC,YAAA;IAAA,OAAE,CAAC;EACpE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC9E,OAAO,EAAE,OAAO,CAAC;IACtB,OAAOA,OAAO,CAAC+E,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC3E,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIhF,WAAW,EAAE;MACfD,gBAAgB,CAAC,CAACD,aAAa,CAAC;MAChCG,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACLA,cAAc,CAAC,IAAI,CAAC;MACpBF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMkF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1E,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIgC,iBAAiB,EAAE;MACrB,MAAM8E,KAAK,GAAGrD,UAAU,CAAC,MAAM;QAC7Bf,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMO,YAAY,CAAC6D,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACtF,OAAO,EAAEQ,iBAAiB,EAAEU,UAAU,CAAC,CAAC;;EAE5C;EACA1C,SAAS,CAAC,MAAM;IACd,MAAM+G,aAAa,GAAIC,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACrE,cAAc,CAAC,CAAC;QAClBD,UAAU,CAAC,CAAC;MACd;MACA,IAAI,CAACsE,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACrE,cAAc,CAAC,CAAC;QAClBiE,UAAU,CAAC,CAAC;MACd;IACF,CAAC;IAED1B,QAAQ,CAACpC,gBAAgB,CAAC,SAAS,EAAEiE,aAAa,CAAC;IACnD,OAAO,MAAM7B,QAAQ,CAACnC,mBAAmB,CAAC,SAAS,EAAEgE,aAAa,CAAC;EACrE,CAAC,EAAE,CAACrE,UAAU,EAAEkE,UAAU,CAAC,CAAC;EAE5B,oBACEzF,OAAA;IAAKiG,SAAS,EAAE,gBAAgBlF,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAAmF,QAAA,gBACjElG,OAAA;MAAKiG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BlG,OAAA;QAAKiG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlG,OAAA;UAAAkG,QAAA,EAAKhG,KAAK,CAACgD;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAK,CAAC,eACtBlF,OAAA;UAAKiG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlG,OAAA;YAAMiG,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEf,YAAY,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAM,CAAC,eAC1DlF,OAAA;YAAMiG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/BrF,iBAAiB,gBAChBb,OAAA;cAAMiG,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAlB,QAAA,EAAAC,YAAA;cAAApC,UAAA;cAAAqC,YAAA;YAAA,OAAM,CAAC,gBAElDlF,OAAA;cAAMiG,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAApC,UAAA;cAAAqC,YAAA;YAAA,OAAM;UACtC;YAAAF,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAF,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACH,CAAC,eAENlF,OAAA;QAAKiG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlG,OAAA;UACEmG,OAAO,EAAEV,UAAW;UACpBQ,SAAS,EAAC,eAAe;UACzB/C,KAAK,EAAEzC,WAAW,GAAG,oBAAoB,GAAGF,aAAa,GAAG,WAAW,GAAG,cAAe;UAAA2F,QAAA,GAExFzF,WAAW,gBAAGT,OAAA,CAACP,KAAK;YAAC2G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,GAAG3E,aAAa,gBAAGP,OAAA,CAACT,MAAM;YAAC6G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,gBAAGlF,OAAA,CAACV,GAAG;YAAC8G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,EAC5FzE,WAAW,GAAG,OAAO,GAAGF,aAAa,GAAG,MAAM,GAAG,SAAS;QAAA;UAAAyE,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACrD,CAAC,eAETlF,OAAA;UACEmG,OAAO,EAAET,gBAAiB;UAC1BO,SAAS,EAAC,eAAe;UACzB/C,KAAK,EAAEnC,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;UAAAmF,QAAA,gBAE7DlG,OAAA,CAACN,SAAS;YAAC0G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,EACtBnE,YAAY,GAAG,MAAM,GAAG,YAAY;QAAA;UAAAiE,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAC/B,CAAC,eAETlF,OAAA;UACEmG,OAAO,EAAE5E,UAAW;UACpB8E,QAAQ,EAAE,CAACxF,iBAAiB,IAAIF,QAAS;UACzCsF,SAAS,EAAC,2BAA2B;UACrC/C,KAAK,EAAC,uBAAuB;UAAAgD,QAAA,gBAE7BlG,OAAA,CAACX,IAAI;YAAC+G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,EACjBvE,QAAQ,GAAG,WAAW,GAAG,MAAM;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAC1B,CAAC,eAETlF,OAAA;UACEmG,OAAO,EAAE3C,YAAa;UACtByC,SAAS,EAAC,eAAe;UACzB/C,KAAK,EAAC,oBAAoB;UAAAgD,QAAA,gBAE1BlG,OAAA,CAACR,QAAQ;YAAC4G,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAF,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAApC,UAAA;MAAAqC,YAAA;IAAA,OACH,CAAC,eAENlF,OAAA;MAAKiG,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BzF,WAAW,gBACVT,OAAA;QAAKiG,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlG,OAAA;UAAKiG,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBlG,OAAA,CAACH,MAAM;YACLyG,MAAM,EAAC,MAAM;YACbC,eAAe,EAAC,UAAU;YAC1BnE,KAAK,EAAE/B,OAAQ;YACfmG,QAAQ,EAAErE,mBAAoB;YAC9BsE,OAAO,EAAGjE,MAAM,IAAK;cACnB;cACAA,MAAM,CAACkE,yBAAyB,CAAC,MAAM;gBACrCnE,0BAA0B,CAACC,MAAM,CAAC;cACpC,CAAC,CAAC;YACJ,CAAE;YACFmE,KAAK,EAAC,UAAU;YAChBC,OAAO,EAAE;cACPC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAC;cAC3BC,QAAQ,EAAE,IAAI;cACdC,WAAW,EAAE,IAAI;cACjBC,oBAAoB,EAAE,KAAK;cAC3BC,eAAe,EAAE,IAAI;cACrBC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,yCAAyC;cACrDC,OAAO,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAG,CAAC;cAChCC,OAAO,EAAE;gBACPC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB;YACF;UAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OACH;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACC,CAAC,eACNlF,OAAA;UAAKiG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlG,OAAA;YAAKiG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B3B,aAAa,CAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAApC,UAAA;YAAAqC,YAAA;UAAA,OACb;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACH,CAAC,GACJ3E,aAAa,gBACfP,OAAA;QAAKiG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChClG,OAAA;UAAKiG,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9B3B,aAAa,CAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACb;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACH,CAAC,gBAENlF,OAAA;QAAKiG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BlG,OAAA,CAACH,MAAM;UACLyG,MAAM,EAAC,MAAM;UACbC,eAAe,EAAC,UAAU;UAC1BnE,KAAK,EAAE/B,OAAQ;UACfmG,QAAQ,EAAErE,mBAAoB;UAC9BsE,OAAO,EAAGjE,MAAM,IAAK;YACnB;YACAA,MAAM,CAACkE,yBAAyB,CAAC,MAAM;cACrCnE,0BAA0B,CAACC,MAAM,CAAC;YACpC,CAAC,CAAC;UACJ,CAAE;UACFmE,KAAK,EAAC,UAAU;UAChBC,OAAO,EAAE;YACPC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAC;YAC1BC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE,IAAI;YACjBC,oBAAoB,EAAE,KAAK;YAC3BC,eAAe,EAAE,IAAI;YACrBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,yCAAyC;YACrDC,OAAO,EAAE;cAAEC,GAAG,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG;UACjC;QAAE;UAAAvC,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OACH;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACC;IACN;MAAAF,QAAA,EAAAC,YAAA;MAAApC,UAAA;MAAAqC,YAAA;IAAA,OACE,CAAC,eAENlF,OAAA;MAAKiG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BlG,OAAA;QAAKiG,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlG,OAAA;UAAAkG,QAAA,GAAM,cAAY,EAAC,CAAA7F,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmF,MAAM,KAAI,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAO,CAAC,eAC/ClF,OAAA;UAAAkG,QAAA,GAAM,SAAO,EAACf,YAAY,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAO,CAAC,eACpClF,OAAA;UAAAkG,QAAA,GAAM,SAAO,EAAC,CAAA7F,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgF,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM,KAAI,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACnD,CAAC,eAENlF,OAAA;QAAKiG,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BlG,OAAA;UAAAkG,QAAA,EAAM;QAA2D;UAAAlB,QAAA,EAAAC,YAAA;UAAApC,UAAA;UAAAqC,YAAA;QAAA,OAAM;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAApC,UAAA;QAAAqC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAApC,UAAA;MAAAqC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAF,QAAA,EAAAC,YAAA;IAAApC,UAAA;IAAAqC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAjVIF,WAAuC;EAAA,QAC1BjB,WAAW;AAAA;AAAA2I,EAAA,GADxB1H,WAAuC;AAmV7C,eAAeA,WAAW;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}