{"ast": null, "code": "// API service for backend communication\n\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\n// Session API\nexport const sessionApi = {\n  async create() {\n    try {\n      const response = await fetch(`${API_BASE_URL}/sessions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to create session');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n};\n\n// Papers API\nexport const papersApi = {\n  async list() {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch papers');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  },\n  async create(title, content = '') {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to create paper');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  },\n  async update(paperId, title, content) {\n    try {\n      const params = new URLSearchParams();\n      if (title) params.append('title', title);\n      if (content) params.append('content', content);\n      const response = await fetch(`${API_BASE_URL}/papers/${paperId}?${params.toString()}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to update paper');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  },\n  async get(paperId) {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers/${paperId}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch paper');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n};\n\n// Charts API\nexport const chartsApi = {\n  async list() {\n    try {\n      const response = await fetch(`${API_BASE_URL}/charts`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch charts');\n      }\n      const data = await response.json();\n      return {\n        data\n      };\n    } catch (error) {\n      return {\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n    }\n  }\n};\n\n// Chat API\nexport const chatApi = {\n  async sendMessage(message, sessionId, paperId) {\n    const response = await fetch(`${API_BASE_URL}/chat/stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        message,\n        session_id: sessionId,\n        paper_id: paperId\n      })\n    });\n    if (!response.ok) {\n      throw new Error('Failed to send message');\n    }\n    return response;\n  }\n};\nexport default {\n  session: sessionApi,\n  papers: papersApi,\n  charts: chartsApi,\n  chat: chatApi\n};", "map": {"version": 3, "names": ["API_BASE_URL", "sessionApi", "create", "response", "fetch", "method", "headers", "ok", "Error", "data", "json", "error", "message", "papersApi", "list", "title", "content", "encodeURIComponent", "update", "paperId", "params", "URLSearchParams", "append", "toString", "get", "chartsApi", "chatApi", "sendMessage", "sessionId", "body", "JSON", "stringify", "session_id", "paper_id", "session", "papers", "charts", "chat"], "sources": ["/home/<USER>/paper_ui/frontend/src/services/api.ts"], "sourcesContent": ["// API service for backend communication\n\nconst API_BASE_URL = 'http://localhost:8000/api/v1';\n\nexport interface ApiResponse<T> {\n  data?: T;\n  error?: string;\n}\n\nexport interface Chart {\n  id: string;\n  type: 'line' | 'bar' | 'pie' | 'polar' | 'doughnut';\n  title: string;\n  url: string;\n  data: any;\n  created_at: string;\n}\n\nexport interface Paper {\n  paper_id: string;\n  title: string;\n  content: string;\n  status: 'draft' | 'in_progress' | 'completed';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Session {\n  session_id: string;\n  created_at: string;\n}\n\n// Session API\nexport const sessionApi = {\n  async create(): Promise<ApiResponse<Session>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/sessions`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to create session');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n};\n\n// Papers API\nexport const papersApi = {\n  async list(): Promise<ApiResponse<{ papers: Paper[] }>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch papers');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n\n  async create(title: string, content: string = ''): Promise<ApiResponse<Paper>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers?title=${encodeURIComponent(title)}&content=${encodeURIComponent(content)}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to create paper');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n\n  async update(paperId: string, title?: string, content?: string): Promise<ApiResponse<Paper>> {\n    try {\n      const params = new URLSearchParams();\n      if (title) params.append('title', title);\n      if (content) params.append('content', content);\n      \n      const response = await fetch(`${API_BASE_URL}/papers/${paperId}?${params.toString()}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to update paper');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n\n  async get(paperId: string): Promise<ApiResponse<Paper>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/papers/${paperId}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch paper');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n};\n\n// Charts API\nexport const chartsApi = {\n  async list(): Promise<ApiResponse<{ charts: Chart[] }>> {\n    try {\n      const response = await fetch(`${API_BASE_URL}/charts`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch charts');\n      }\n      \n      const data = await response.json();\n      return { data };\n    } catch (error) {\n      return { error: error instanceof Error ? error.message : 'Unknown error' };\n    }\n  },\n};\n\n// Chat API\nexport const chatApi = {\n  async sendMessage(message: string, sessionId: string, paperId?: string): Promise<Response> {\n    const response = await fetch(`${API_BASE_URL}/chat/stream`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        message,\n        session_id: sessionId,\n        paper_id: paperId,\n      }),\n    });\n    \n    if (!response.ok) {\n      throw new Error('Failed to send message');\n    }\n    \n    return response;\n  },\n};\n\nexport default {\n  session: sessionApi,\n  papers: papersApi,\n  charts: chartsApi,\n  chat: chatApi,\n};\n"], "mappings": "AAAA;;AAEA,MAAMA,YAAY,GAAG,8BAA8B;AA8BnD;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB,MAAMC,MAAMA,CAAA,EAAkC;IAC5C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,WAAW,EAAE;QACvDK,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvB,MAAMC,IAAIA,CAAA,EAA8C;IACtD,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,SAAS,CAAC;MAEtD,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF,CAAC;EAED,MAAMV,MAAMA,CAACa,KAAa,EAAEC,OAAe,GAAG,EAAE,EAA+B;IAC7E,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,iBAAiBiB,kBAAkB,CAACF,KAAK,CAAC,YAAYE,kBAAkB,CAACD,OAAO,CAAC,EAAE,EAAE;QAC/HX,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF,CAAC;EAED,MAAMM,MAAMA,CAACC,OAAe,EAAEJ,KAAc,EAAEC,OAAgB,EAA+B;IAC3F,IAAI;MACF,MAAMI,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpC,IAAIN,KAAK,EAAEK,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEP,KAAK,CAAC;MACxC,IAAIC,OAAO,EAAEI,MAAM,CAACE,MAAM,CAAC,SAAS,EAAEN,OAAO,CAAC;MAE9C,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,WAAWmB,OAAO,IAAIC,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,EAAE;QACrFlB,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF,CAAC;EAED,MAAMY,GAAGA,CAACL,OAAe,EAA+B;IACtD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,WAAWmB,OAAO,EAAE,CAAC;MAEjE,IAAI,CAAChB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,SAAS,GAAG;EACvB,MAAMX,IAAIA,CAAA,EAA8C;IACtD,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,SAAS,CAAC;MAEtD,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEA,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAClC,OAAO;QAAED;MAAK,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,OAAO;QAAEA,KAAK,EAAEA,KAAK,YAAYH,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAG;MAAgB,CAAC;IAC5E;EACF;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,OAAO,GAAG;EACrB,MAAMC,WAAWA,CAACf,OAAe,EAAEgB,SAAiB,EAAET,OAAgB,EAAqB;IACzF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGJ,YAAY,cAAc,EAAE;MAC1DK,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDuB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBnB,OAAO;QACPoB,UAAU,EAAEJ,SAAS;QACrBK,QAAQ,EAAEd;MACZ,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAChB,QAAQ,CAACI,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,OAAOL,QAAQ;EACjB;AACF,CAAC;AAED,eAAe;EACb+B,OAAO,EAAEjC,UAAU;EACnBkC,MAAM,EAAEtB,SAAS;EACjBuB,MAAM,EAAEX,SAAS;EACjBY,IAAI,EAAEX;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}