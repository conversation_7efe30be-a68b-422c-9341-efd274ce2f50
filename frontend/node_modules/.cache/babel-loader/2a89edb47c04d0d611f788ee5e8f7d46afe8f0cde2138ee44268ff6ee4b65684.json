{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { updateCurrentPaperContent, setCursorPosition as setCursorPositionAction } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaperEditor = ({\n  paper\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [cursorPosition, setCursorPosition] = useState({\n    line: 1,\n    column: 1\n  });\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n  const handleContentChange = useCallback(value => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n  const handleCursorPositionChange = useCallback(editor => {\n    const position = editor.getPosition();\n    if (position) {\n      const newPosition = {\n        line: position.lineNumber,\n        column: position.column\n      };\n      setCursorPosition(newPosition);\n      dispatch(setCursorPositionAction(newPosition));\n    }\n  }, [dispatch]);\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleExport = () => {\n    const blob = new Blob([content], {\n      type: 'text/markdown'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n  const renderPreview = () => {\n    const contentToRender = content || '';\n    const html = marked.parse(contentToRender, {\n      async: false\n    });\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: sanitizedHtml\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 12\n    }, this);\n  };\n  const getWordCount = () => {\n    if (!content) return 0;\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave, toggleView]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `paper-editor ${isFullscreen ? 'fullscreen' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: paper.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"word-count\",\n            children: [getWordCount(), \" words\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-indicator\",\n            children: hasUnsavedChanges ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unsaved\",\n              children: \"\\u25CF Unsaved changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"saved\",\n              children: \"\\u2713 Saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleView,\n          className: \"editor-button\",\n          title: isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode',\n          children: [isSplitView ? /*#__PURE__*/_jsxDEV(Split, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 28\n          }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 66\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 89\n          }, this), isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleFullscreen,\n          className: \"editor-button\",\n          title: isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen',\n          children: [/*#__PURE__*/_jsxDEV(Maximize2, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), isFullscreen ? 'Exit' : 'Fullscreen']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: !hasUnsavedChanges || isSaving,\n          className: \"editor-button save-button\",\n          title: \"Save changes (Ctrl+S)\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), isSaving ? 'Saving...' : 'Save']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          className: \"editor-button\",\n          title: \"Export as Markdown\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: isSplitView ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"split-view\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-pane\",\n          children: /*#__PURE__*/_jsxDEV(Editor, {\n            height: \"100%\",\n            defaultLanguage: \"markdown\",\n            value: content,\n            onChange: handleContentChange,\n            onMount: editor => {\n              // Track cursor position changes\n              editor.onDidChangeCursorPosition(() => {\n                handleCursorPositionChange(editor);\n              });\n            },\n            theme: \"vs-light\",\n            options: {\n              minimap: {\n                enabled: false\n              },\n              wordWrap: 'on',\n              lineNumbers: 'on',\n              scrollBeyondLastLine: false,\n              automaticLayout: true,\n              fontSize: 14,\n              fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n              padding: {\n                top: 16,\n                bottom: 16\n              },\n              suggest: {\n                showKeywords: false,\n                showSnippets: false\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-pane\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"markdown-content\",\n            children: renderPreview()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this) : isPreviewMode ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-pane full\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-content\",\n          children: renderPreview()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"edit-pane full\",\n        children: /*#__PURE__*/_jsxDEV(Editor, {\n          height: \"100%\",\n          defaultLanguage: \"markdown\",\n          value: content,\n          onChange: handleContentChange,\n          theme: \"vs-light\",\n          options: {\n            minimap: {\n              enabled: true\n            },\n            wordWrap: 'on',\n            lineNumbers: 'on',\n            scrollBeyondLastLine: false,\n            automaticLayout: true,\n            fontSize: 16,\n            fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n            padding: {\n              top: 20,\n              bottom: 20\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Characters: \", (content === null || content === void 0 ? void 0 : content.length) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Words: \", getWordCount()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Lines: \", (content === null || content === void 0 ? void 0 : content.split('\\n').length) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-help\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Markdown supported \\u2022 Ctrl+S to save \\u2022 Ctrl+P to toggle view\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperEditor, \"NraMQAbtYgPh6E2sBQ3s0xi7Vns=\", false, function () {\n  return [useDispatch];\n});\n_c = PaperEditor;\nexport default PaperEditor;\nvar _c;\n$RefreshReg$(_c, \"PaperEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "updateCurrentPaperContent", "setCursorPosition", "setCursorPositionAction", "addNotification", "Save", "Eye", "Eye<PERSON>ff", "Download", "Split", "Maximize2", "marked", "DOMPurify", "Editor", "api", "jsxDEV", "_jsxDEV", "PaperEditor", "paper", "_s", "dispatch", "content", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewMode", "setIsPreviewMode", "isSplitView", "setIsSplitView", "isSaving", "setIsSaving", "hasUnsavedChanges", "setHasUnsavedChanges", "isFullscreen", "setIsFullscreen", "cursorPosition", "line", "column", "paper_id", "handleContentChange", "value", "newContent", "handleCursorPositionChange", "editor", "position", "getPosition", "newPosition", "lineNumber", "handleSave", "result", "papers", "update", "title", "data", "type", "message", "duration", "Error", "error", "handleExport", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "toLowerCase", "click", "revokeObjectURL", "renderPreview", "contentToRender", "html", "parse", "async", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "columnNumber", "getWordCount", "trim", "split", "filter", "word", "length", "to<PERSON><PERSON><PERSON><PERSON>", "toggleFullscreen", "timer", "setTimeout", "clearTimeout", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "addEventListener", "removeEventListener", "className", "children", "onClick", "size", "disabled", "height", "defaultLanguage", "onChange", "onMount", "onDidChangeCursorPosition", "theme", "options", "minimap", "enabled", "wordWrap", "lineNumbers", "scrollBeyondLastLine", "automaticLayout", "fontSize", "fontFamily", "padding", "top", "bottom", "suggest", "showKeywords", "showSnippets", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Paper, updateCurrentPaperContent, setCursorPosition as setCursorPositionAction } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\n\ninterface PaperEditorProps {\n  paper: Paper;\n}\n\nconst PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [cursorPosition, setCursorPosition] = useState({ line: 1, column: 1 });\n\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  const handleContentChange = useCallback((value: string | undefined) => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n\n  const handleCursorPositionChange = useCallback((editor: any) => {\n    const position = editor.getPosition();\n    if (position) {\n      const newPosition = { line: position.lineNumber, column: position.column };\n      setCursorPosition(newPosition);\n      dispatch(setCursorPositionAction(newPosition));\n    }\n  }, [dispatch]);\n\n\n\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n\n      if (result.data) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const renderPreview = () => {\n    const contentToRender = content || '';\n    const html = marked.parse(contentToRender, { async: false }) as string;\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n  };\n\n  const getWordCount = () => {\n    if (!content) return 0;\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n\n  const toggleView = () => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  };\n\n  const toggleFullscreen = () => {\n    setIsFullscreen(!isFullscreen);\n  };\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges) {\n      const timer = setTimeout(() => {\n        handleSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n\n      return () => clearTimeout(timer);\n    }\n  }, [content, hasUnsavedChanges, handleSave]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {\n        e.preventDefault();\n        toggleView();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave, toggleView]);\n\n  return (\n    <div className={`paper-editor ${isFullscreen ? 'fullscreen' : ''}`}>\n      <div className=\"editor-header\">\n        <div className=\"editor-title\">\n          <h2>{paper.title}</h2>\n          <div className=\"editor-meta\">\n            <span className=\"word-count\">{getWordCount()} words</span>\n            <span className=\"status-indicator\">\n              {hasUnsavedChanges ? (\n                <span className=\"unsaved\">● Unsaved changes</span>\n              ) : (\n                <span className=\"saved\">✓ Saved</span>\n              )}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"editor-actions\">\n          <button\n            onClick={toggleView}\n            className=\"editor-button\"\n            title={isSplitView ? 'Toggle single view' : isPreviewMode ? 'Edit mode' : 'Preview mode'}\n          >\n            {isSplitView ? <Split size={16} /> : isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}\n            {isSplitView ? 'Split' : isPreviewMode ? 'Edit' : 'Preview'}\n          </button>\n\n          <button\n            onClick={toggleFullscreen}\n            className=\"editor-button\"\n            title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}\n          >\n            <Maximize2 size={16} />\n            {isFullscreen ? 'Exit' : 'Fullscreen'}\n          </button>\n\n          <button\n            onClick={handleSave}\n            disabled={!hasUnsavedChanges || isSaving}\n            className=\"editor-button save-button\"\n            title=\"Save changes (Ctrl+S)\"\n          >\n            <Save size={16} />\n            {isSaving ? 'Saving...' : 'Save'}\n          </button>\n\n          <button\n            onClick={handleExport}\n            className=\"editor-button\"\n            title=\"Export as Markdown\"\n          >\n            <Download size={16} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      <div className=\"editor-content\">\n        {isSplitView ? (\n          <div className=\"split-view\">\n            <div className=\"edit-pane\">\n              <Editor\n                height=\"100%\"\n                defaultLanguage=\"markdown\"\n                value={content}\n                onChange={handleContentChange}\n                onMount={(editor) => {\n                  // Track cursor position changes\n                  editor.onDidChangeCursorPosition(() => {\n                    handleCursorPositionChange(editor);\n                  });\n                }}\n                theme=\"vs-light\"\n                options={{\n                  minimap: { enabled: false },\n                  wordWrap: 'on',\n                  lineNumbers: 'on',\n                  scrollBeyondLastLine: false,\n                  automaticLayout: true,\n                  fontSize: 14,\n                  fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                  padding: { top: 16, bottom: 16 },\n                  suggest: {\n                    showKeywords: false,\n                    showSnippets: false,\n                  },\n                }}\n              />\n            </div>\n            <div className=\"preview-pane\">\n              <div className=\"markdown-content\">\n                {renderPreview()}\n              </div>\n            </div>\n          </div>\n        ) : isPreviewMode ? (\n          <div className=\"preview-pane full\">\n            <div className=\"markdown-content\">\n              {renderPreview()}\n            </div>\n          </div>\n        ) : (\n          <div className=\"edit-pane full\">\n            <Editor\n              height=\"100%\"\n              defaultLanguage=\"markdown\"\n              value={content}\n              onChange={handleContentChange}\n              theme=\"vs-light\"\n              options={{\n                minimap: { enabled: true },\n                wordWrap: 'on',\n                lineNumbers: 'on',\n                scrollBeyondLastLine: false,\n                automaticLayout: true,\n                fontSize: 16,\n                fontFamily: 'Monaco, Menlo, \"Ubuntu Mono\", monospace',\n                padding: { top: 20, bottom: 20 },\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"editor-footer\">\n        <div className=\"editor-stats\">\n          <span>Characters: {content?.length || 0}</span>\n          <span>Words: {getWordCount()}</span>\n          <span>Lines: {content?.split('\\n').length || 0}</span>\n        </div>\n\n        <div className=\"editor-help\">\n          <span>Markdown supported • Ctrl+S to save • Ctrl+P to toggle view</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaperEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAAgBC,yBAAyB,EAAEC,iBAAiB,IAAIC,uBAAuB,QAAQ,gCAAgC;AAC/H,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,cAAc;AAC5E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAACqB,KAAK,CAACG,OAAO,CAAC;EACrD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,cAAc,EAAE/B,iBAAiB,CAAC,GAAGL,QAAQ,CAAC;IAAEqC,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EAE5ErC,SAAS,CAAC,MAAM;IACdwB,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC;IACzBS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACZ,KAAK,CAACkB,QAAQ,EAAElB,KAAK,CAACG,OAAO,CAAC,CAAC;EAEnCvB,SAAS,CAAC,MAAM;IACdgC,oBAAoB,CAACT,OAAO,KAAKH,KAAK,CAACG,OAAO,CAAC;EACjD,CAAC,EAAE,CAACA,OAAO,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC;EAE5B,MAAMgB,mBAAmB,GAAGtC,WAAW,CAAEuC,KAAyB,IAAK;IACrE,MAAMC,UAAU,GAAGD,KAAK,IAAI,EAAE;IAC9BhB,UAAU,CAACiB,UAAU,CAAC;IACtBnB,QAAQ,CAACnB,yBAAyB,CAACsC,UAAU,CAAC,CAAC;EACjD,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;EAEd,MAAMoB,0BAA0B,GAAGzC,WAAW,CAAE0C,MAAW,IAAK;IAC9D,MAAMC,QAAQ,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;IACrC,IAAID,QAAQ,EAAE;MACZ,MAAME,WAAW,GAAG;QAAEV,IAAI,EAAEQ,QAAQ,CAACG,UAAU;QAAEV,MAAM,EAAEO,QAAQ,CAACP;MAAO,CAAC;MAC1EjC,iBAAiB,CAAC0C,WAAW,CAAC;MAC9BxB,QAAQ,CAACjB,uBAAuB,CAACyC,WAAW,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;EAId,MAAM0B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACjB,iBAAiB,EAAE;IAExBD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMmB,MAAM,GAAG,MAAMjC,GAAG,CAACkC,MAAM,CAACC,MAAM,CAAC/B,KAAK,CAACkB,QAAQ,EAAElB,KAAK,CAACgC,KAAK,EAAE7B,OAAO,CAAC;MAE5E,IAAI0B,MAAM,CAACI,IAAI,EAAE;QACfrB,oBAAoB,CAAC,KAAK,CAAC;QAC3BV,QAAQ,CAAChB,eAAe,CAAC;UACvBgD,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,aAAa;UACpBG,OAAO,EAAE,4CAA4C;UACrDC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACR,MAAM,CAACS,KAAK,IAAI,gBAAgB,CAAC;MACnD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdpC,QAAQ,CAAChB,eAAe,CAAC;QACvBgD,IAAI,EAAE,OAAO;QACbF,KAAK,EAAE,aAAa;QACpBG,OAAO,EAAE,gDAAgD;QACzDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR1B,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACtC,OAAO,CAAC,EAAE;MAAE+B,IAAI,EAAE;IAAgB,CAAC,CAAC;IAC3D,MAAMQ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAGjD,KAAK,CAACgC,KAAK,CAACkB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK;IAC7EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZT,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,eAAe,GAAGpD,OAAO,IAAI,EAAE;IACrC,MAAMqD,IAAI,GAAG/D,MAAM,CAACgE,KAAK,CAACF,eAAe,EAAE;MAAEG,KAAK,EAAE;IAAM,CAAC,CAAW;IACtE,MAAMC,aAAa,GAAGjE,SAAS,CAACkE,QAAQ,CAACJ,IAAI,CAAC;IAC9C,oBAAO1D,OAAA;MAAK+D,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAc;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAArC,UAAA;MAAAsC,YAAA;IAAA,OAAE,CAAC;EACpE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC/D,OAAO,EAAE,OAAO,CAAC;IACtB,OAAOA,OAAO,CAACgE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC3E,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIjE,WAAW,EAAE;MACfD,gBAAgB,CAAC,CAACD,aAAa,CAAC;MAChCG,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACLA,cAAc,CAAC,IAAI,CAAC;MACpBF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3D,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACAjC,SAAS,CAAC,MAAM;IACd,IAAI+B,iBAAiB,EAAE;MACrB,MAAM+D,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7B/C,UAAU,CAAC,CAAC;MACd,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAMgD,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACvE,OAAO,EAAEQ,iBAAiB,EAAEiB,UAAU,CAAC,CAAC;;EAE5C;EACAhD,SAAS,CAAC,MAAM;IACd,MAAMiG,aAAa,GAAIC,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBtD,UAAU,CAAC,CAAC;MACd;MACA,IAAI,CAACkD,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBV,UAAU,CAAC,CAAC;MACd;IACF,CAAC;IAED1B,QAAQ,CAACqC,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACnD,OAAO,MAAM/B,QAAQ,CAACsC,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;EACrE,CAAC,EAAE,CAACjD,UAAU,EAAE4C,UAAU,CAAC,CAAC;EAE5B,oBACE1E,OAAA;IAAKuF,SAAS,EAAE,gBAAgBxE,YAAY,GAAG,YAAY,GAAG,EAAE,EAAG;IAAAyE,QAAA,gBACjExF,OAAA;MAAKuF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxF,OAAA;QAAKuF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxF,OAAA;UAAAwF,QAAA,EAAKtF,KAAK,CAACgC;QAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAK,CAAC,eACtBnE,OAAA;UAAKuF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BxF,OAAA;YAAMuF,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEpB,YAAY,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAM,CAAC,eAC1DnE,OAAA;YAAMuF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/B3E,iBAAiB,gBAChBb,OAAA;cAAMuF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAvB,QAAA,EAAAC,YAAA;cAAArC,UAAA;cAAAsC,YAAA;YAAA,OAAM,CAAC,gBAElDnE,OAAA;cAAMuF,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAvB,QAAA,EAAAC,YAAA;cAAArC,UAAA;cAAAsC,YAAA;YAAA,OAAM;UACtC;YAAAF,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAF,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACH,CAAC,eAENnE,OAAA;QAAKuF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxF,OAAA;UACEyF,OAAO,EAAEf,UAAW;UACpBa,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAEzB,WAAW,GAAG,oBAAoB,GAAGF,aAAa,GAAG,WAAW,GAAG,cAAe;UAAAiF,QAAA,GAExF/E,WAAW,gBAAGT,OAAA,CAACP,KAAK;YAACiG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,GAAG5D,aAAa,gBAAGP,OAAA,CAACT,MAAM;YAACmG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACV,GAAG;YAACoG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,EAC5F1D,WAAW,GAAG,OAAO,GAAGF,aAAa,GAAG,MAAM,GAAG,SAAS;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACrD,CAAC,eAETnE,OAAA;UACEyF,OAAO,EAAEd,gBAAiB;UAC1BY,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAEnB,YAAY,GAAG,iBAAiB,GAAG,kBAAmB;UAAAyE,QAAA,gBAE7DxF,OAAA,CAACN,SAAS;YAACgG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,EACtBpD,YAAY,GAAG,MAAM,GAAG,YAAY;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAC/B,CAAC,eAETnE,OAAA;UACEyF,OAAO,EAAE3D,UAAW;UACpB6D,QAAQ,EAAE,CAAC9E,iBAAiB,IAAIF,QAAS;UACzC4E,SAAS,EAAC,2BAA2B;UACrCrD,KAAK,EAAC,uBAAuB;UAAAsD,QAAA,gBAE7BxF,OAAA,CAACX,IAAI;YAACqG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,EACjBxD,QAAQ,GAAG,WAAW,GAAG,MAAM;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAC1B,CAAC,eAETnE,OAAA;UACEyF,OAAO,EAAEhD,YAAa;UACtB8C,SAAS,EAAC,eAAe;UACzBrD,KAAK,EAAC,oBAAoB;UAAAsD,QAAA,gBAE1BxF,OAAA,CAACR,QAAQ;YAACkG,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAF,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArC,UAAA;MAAAsC,YAAA;IAAA,OACH,CAAC,eAENnE,OAAA;MAAKuF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5B/E,WAAW,gBACVT,OAAA;QAAKuF,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBxF,OAAA;UAAKuF,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBxF,OAAA,CAACH,MAAM;YACL+F,MAAM,EAAC,MAAM;YACbC,eAAe,EAAC,UAAU;YAC1BvE,KAAK,EAAEjB,OAAQ;YACfyF,QAAQ,EAAEzE,mBAAoB;YAC9B0E,OAAO,EAAGtE,MAAM,IAAK;cACnB;cACAA,MAAM,CAACuE,yBAAyB,CAAC,MAAM;gBACrCxE,0BAA0B,CAACC,MAAM,CAAC;cACpC,CAAC,CAAC;YACJ,CAAE;YACFwE,KAAK,EAAC,UAAU;YAChBC,OAAO,EAAE;cACPC,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAC;cAC3BC,QAAQ,EAAE,IAAI;cACdC,WAAW,EAAE,IAAI;cACjBC,oBAAoB,EAAE,KAAK;cAC3BC,eAAe,EAAE,IAAI;cACrBC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,yCAAyC;cACrDC,OAAO,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAG,CAAC;cAChCC,OAAO,EAAE;gBACPC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB;YACF;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OACH;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACC,CAAC,eACNnE,OAAA;UAAKuF,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxF,OAAA;YAAKuF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9BhC,aAAa,CAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAArC,UAAA;YAAAsC,YAAA;UAAA,OACb;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACH,CAAC,GACJ5D,aAAa,gBACfP,OAAA;QAAKuF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCxF,OAAA;UAAKuF,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BhC,aAAa,CAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACb;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACH,CAAC,gBAENnE,OAAA;QAAKuF,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BxF,OAAA,CAACH,MAAM;UACL+F,MAAM,EAAC,MAAM;UACbC,eAAe,EAAC,UAAU;UAC1BvE,KAAK,EAAEjB,OAAQ;UACfyF,QAAQ,EAAEzE,mBAAoB;UAC9B4E,KAAK,EAAC,UAAU;UAChBC,OAAO,EAAE;YACPC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAC;YAC1BC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE,IAAI;YACjBC,oBAAoB,EAAE,KAAK;YAC3BC,eAAe,EAAE,IAAI;YACrBC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,yCAAyC;YACrDC,OAAO,EAAE;cAAEC,GAAG,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG;UACjC;QAAE;UAAA5C,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OACH;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACC;IACN;MAAAF,QAAA,EAAAC,YAAA;MAAArC,UAAA;MAAAsC,YAAA;IAAA,OACE,CAAC,eAENnE,OAAA;MAAKuF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BxF,OAAA;QAAKuF,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxF,OAAA;UAAAwF,QAAA,GAAM,cAAY,EAAC,CAAAnF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoE,MAAM,KAAI,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAO,CAAC,eAC/CnE,OAAA;UAAAwF,QAAA,GAAM,SAAO,EAACpB,YAAY,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAO,CAAC,eACpCnE,OAAA;UAAAwF,QAAA,GAAM,SAAO,EAAC,CAAAnF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiE,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM,KAAI,CAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACnD,CAAC,eAENnE,OAAA;QAAKuF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxF,OAAA;UAAAwF,QAAA,EAAM;QAA2D;UAAAvB,QAAA,EAAAC,YAAA;UAAArC,UAAA;UAAAsC,YAAA;QAAA,OAAM;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArC,UAAA;QAAAsC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArC,UAAA;MAAAsC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAF,QAAA,EAAAC,YAAA;IAAArC,UAAA;IAAAsC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA3QIF,WAAuC;EAAA,QAC1BjB,WAAW;AAAA;AAAAiI,EAAA,GADxBhH,WAAuC;AA6Q7C,eAAeA,WAAW;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}