{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"22\",\n  x2: \"2\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"15w7dq\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"2\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1ip48p\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a2lnyx\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"8vb6jd\"\n}]];\nconst Frame = createLucideIcon(\"frame\", __iconNode);\nexport { __iconNode, Frame as default };", "map": {"version": 3, "names": ["__iconNode", "x1", "x2", "y1", "y2", "key", "<PERSON>ame", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/frame.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '22', x2: '2', y1: '6', y2: '6', key: '15w7dq' }],\n  ['line', { x1: '22', x2: '2', y1: '18', y2: '18', key: '1ip48p' }],\n  ['line', { x1: '6', x2: '6', y1: '2', y2: '22', key: 'a2lnyx' }],\n  ['line', { x1: '18', x2: '18', y1: '2', y2: '22', key: '8vb6jd' }],\n];\n\n/**\n * @component @name Frame\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMjIiIHgyPSIyIiB5MT0iNiIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjIyIiB4Mj0iMiIgeTE9IjE4IiB5Mj0iMTgiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSI2IiB5MT0iMiIgeTI9IjIyIiAvPgogIDxsaW5lIHgxPSIxOCIgeDI9IjE4IiB5MT0iMiIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/frame\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Frame = createLucideIcon('frame', __iconNode);\n\nexport default Frame;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAU,GACnE;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}