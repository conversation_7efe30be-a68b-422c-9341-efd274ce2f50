{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"15\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"2no95f\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"7\",\n  x: \"6\",\n  y: \"8\",\n  rx: \"1\",\n  key: \"zh9wx\"\n}], [\"path\", {\n  d: \"M18 8v7\",\n  key: \"o5zi4n\"\n}], [\"path\", {\n  d: \"M6 19v2\",\n  key: \"1loha6\"\n}], [\"path\", {\n  d: \"M18 19v2\",\n  key: \"1dawf0\"\n}]];\nconst Microwave = createLucideIcon(\"microwave\", __iconNode);\nexport { __iconNode, Microwave as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "Microwave", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/microwave.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '15', x: '2', y: '4', rx: '2', key: '2no95f' }],\n  ['rect', { width: '8', height: '7', x: '6', y: '8', rx: '1', key: 'zh9wx' }],\n  ['path', { d: 'M18 8v7', key: 'o5zi4n' }],\n  ['path', { d: 'M6 19v2', key: '1loha6' }],\n  ['path', { d: 'M18 19v2', key: '1dawf0' }],\n];\n\n/**\n * @component @name Microwave\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTUiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxyZWN0IHdpZHRoPSI4IiBoZWlnaHQ9IjciIHg9IjYiIHk9IjgiIHJ4PSIxIiAvPgogIDxwYXRoIGQ9Ik0xOCA4djciIC8+CiAgPHBhdGggZD0iTTYgMTl2MiIgLz4KICA8cGF0aCBkPSJNMTggMTl2MiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/microwave\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Microwave = createLucideIcon('microwave', __iconNode);\n\nexport default Microwave;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC3E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAE,SAAA,GAAYC,gBAAiB,cAAaT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}