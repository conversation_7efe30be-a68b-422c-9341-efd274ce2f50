{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 16v2\",\n  key: \"g5qcv5\"\n}], [\"path\", {\n  d: \"M19 16v2\",\n  key: \"1gbaio\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"8\",\n  x: \"2\",\n  y: \"8\",\n  rx: \"2\",\n  key: \"vjsjur\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}]];\nconst RadioReceiver = createLucideIcon(\"radio-receiver\", __iconNode);\nexport { __iconNode, RadioReceiver as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "RadioReceiver", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/radio-receiver.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 16v2', key: 'g5qcv5' }],\n  ['path', { d: 'M19 16v2', key: '1gbaio' }],\n  ['rect', { width: '20', height: '8', x: '2', y: '8', rx: '2', key: 'vjsjur' }],\n  ['path', { d: 'M18 12h.01', key: 'yjnet6' }],\n];\n\n/**\n * @component @name RadioReceiver\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxNnYyIiAvPgogIDxwYXRoIGQ9Ik0xOSAxNnYyIiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSI4IiB4PSIyIiB5PSI4IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMTggMTJoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/radio-receiver\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RadioReceiver = createLucideIcon('radio-receiver', __iconNode);\n\nexport default RadioReceiver;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAM,aAAA,GAAgBC,gBAAiB,mBAAkBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}