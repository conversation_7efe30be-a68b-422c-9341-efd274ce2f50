{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses, hasA11yProp } from './shared/src/utils.js';\nconst Icon = forwardRef((_ref, ref) => {\n  let {\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  } = _ref;\n  return createElement(\"svg\", {\n    ref,\n    ...defaultAttributes,\n    width: size,\n    height: size,\n    stroke: color,\n    strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n    className: mergeClasses(\"lucide\", className),\n    ...(!children && !hasA11yProp(rest) && {\n      \"aria-hidden\": \"true\"\n    }),\n    ...rest\n  }, [...iconNode.map(_ref2 => {\n    let [tag, attrs] = _ref2;\n    return createElement(tag, attrs);\n  }), ...(Array.isArray(children) ? children : [children])]);\n});\nexport { Icon as default };", "map": {"version": 3, "names": ["Icon", "forwardRef", "_ref", "ref", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "iconNode", "rest", "createElement", "defaultAttributes", "width", "height", "stroke", "Number", "mergeClasses", "hasA11yProp", "map", "_ref2", "tag", "attrs", "Array", "isArray"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "mappings": ";;;;;;;;;;AAwBA,MAAMA,IAAO,GAAAC,UAAA,CACX,CAAAC,IAAA,EAWEC,GAEA;EAAA,IAZA;IACEC,KAAQ;IACRC,IAAO;IACPC,WAAc;IACdC,mBAAA;IACAC,SAAY;IACZC,QAAA;IACAC,QAAA;IACA,GAAGC;EAAA,IAAAT,IAAA;EAAA,OAILU,aAAA,CACE,OACA;IACET,GAAA;IACA,GAAGU,iBAAA;IACHC,KAAO,EAAAT,IAAA;IACPU,MAAQ,EAAAV,IAAA;IACRW,MAAQ,EAAAZ,KAAA;IACRE,WAAA,EAAaC,mBAAA,GAAuBU,MAAO,CAAAX,WAAW,IAAI,EAAM,GAAAW,MAAA,CAAOZ,IAAI,CAAI,GAAAC,WAAA;IAC/EE,SAAA,EAAWU,YAAa,WAAUV,SAAS;IAC3C,IAAI,CAACC,QAAY,KAACU,WAAA,CAAYR,IAAI,KAAK;MAAE,eAAe;IAAO;IAC/D,GAAGA;EACL,GACA,CACE,GAAGD,QAAS,CAAAU,GAAA,CAAIC,KAAA;IAAA,IAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,GAAAF,KAAA;IAAA,OAAAT,aAAA,CAAcU,GAAK,EAAAC,KAAK,CAAC;EAAA,IAC3D,IAAIC,KAAM,CAAAC,OAAA,CAAQhB,QAAQ,CAAI,GAAAA,QAAA,GAAW,CAACA,QAAQ,GACpD;AAAA,CAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}