{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  notifications: [],\n  sidebarCollapsed: false,\n  rightPanelCollapsed: false,\n  currentView: 'chat',\n  theme: 'light',\n  isLoading: false,\n  loadingMessage: ''\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    addNotification: (state, action) => {\n      const notification = {\n        ...action.payload,\n        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        timestamp: new Date().toISOString()\n      };\n      state.notifications.push(notification);\n    },\n    removeNotification: (state, action) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    clearNotifications: state => {\n      state.notifications = [];\n    },\n    toggleSidebar: state => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    toggleRightPanel: state => {\n      state.rightPanelCollapsed = !state.rightPanelCollapsed;\n    },\n    setCurrentView: (state, action) => {\n      state.currentView = action.payload;\n    },\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload.isLoading;\n      state.loadingMessage = action.payload.message || '';\n    }\n  }\n});\nexport const {\n  addNotification,\n  removeNotification,\n  clearNotifications,\n  toggleSidebar,\n  toggleRightPanel,\n  setCurrentView,\n  setTheme,\n  setLoading\n} = uiSlice.actions;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "notifications", "sidebarCollapsed", "rightPanelCollapsed", "current<PERSON>iew", "theme", "isLoading", "loadingMessage", "uiSlice", "name", "reducers", "addNotification", "state", "action", "notification", "payload", "id", "Date", "now", "Math", "random", "toString", "substr", "timestamp", "toISOString", "push", "removeNotification", "filter", "n", "clearNotifications", "toggleSidebar", "toggleRightPanel", "set<PERSON><PERSON><PERSON>View", "setTheme", "setLoading", "message", "actions", "reducer"], "sources": ["/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  duration?: number;\n  timestamp: string;\n}\n\nexport interface UIState {\n  notifications: Notification[];\n  sidebarCollapsed: boolean;\n  rightPanelCollapsed: boolean;\n  currentView: 'chat' | 'editor' | 'charts' | 'papers';\n  theme: 'light' | 'dark';\n  isLoading: boolean;\n  loadingMessage: string;\n}\n\nconst initialState: UIState = {\n  notifications: [],\n  sidebarCollapsed: false,\n  rightPanelCollapsed: false,\n  currentView: 'chat',\n  theme: 'light',\n  isLoading: false,\n  loadingMessage: '',\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        timestamp: new Date().toISOString(),\n      };\n      state.notifications.push(notification);\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n    toggleSidebar: (state) => {\n      state.sidebarCollapsed = !state.sidebarCollapsed;\n    },\n    toggleRightPanel: (state) => {\n      state.rightPanelCollapsed = !state.rightPanelCollapsed;\n    },\n    setCurrentView: (state, action: PayloadAction<UIState['currentView']>) => {\n      state.currentView = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<{ isLoading: boolean; message?: string }>) => {\n      state.isLoading = action.payload.isLoading;\n      state.loadingMessage = action.payload.message || '';\n    },\n  },\n});\n\nexport const {\n  addNotification,\n  removeNotification,\n  clearNotifications,\n  toggleSidebar,\n  toggleRightPanel,\n  setCurrentView,\n  setTheme,\n  setLoading,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAqB7D,MAAMC,YAAqB,GAAG;EAC5BC,aAAa,EAAE,EAAE;EACjBC,gBAAgB,EAAE,KAAK;EACvBC,mBAAmB,EAAE,KAAK;EAC1BC,WAAW,EAAE,MAAM;EACnBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,KAAK;EAChBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,OAAO,GAAGT,WAAW,CAAC;EAC1BU,IAAI,EAAE,IAAI;EACVT,YAAY;EACZU,QAAQ,EAAE;IACRC,eAAe,EAAEA,CAACC,KAAK,EAAEC,MAA6D,KAAK;MACzF,MAAMC,YAA0B,GAAG;QACjC,GAAGD,MAAM,CAACE,OAAO;QACjBC,EAAE,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9DC,SAAS,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC;MACpC,CAAC;MACDZ,KAAK,CAACX,aAAa,CAACwB,IAAI,CAACX,YAAY,CAAC;IACxC,CAAC;IACDY,kBAAkB,EAAEA,CAACd,KAAK,EAAEC,MAA6B,KAAK;MAC5DD,KAAK,CAACX,aAAa,GAAGW,KAAK,CAACX,aAAa,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKH,MAAM,CAACE,OAAO,CAAC;IAChF,CAAC;IACDc,kBAAkB,EAAGjB,KAAK,IAAK;MAC7BA,KAAK,CAACX,aAAa,GAAG,EAAE;IAC1B,CAAC;IACD6B,aAAa,EAAGlB,KAAK,IAAK;MACxBA,KAAK,CAACV,gBAAgB,GAAG,CAACU,KAAK,CAACV,gBAAgB;IAClD,CAAC;IACD6B,gBAAgB,EAAGnB,KAAK,IAAK;MAC3BA,KAAK,CAACT,mBAAmB,GAAG,CAACS,KAAK,CAACT,mBAAmB;IACxD,CAAC;IACD6B,cAAc,EAAEA,CAACpB,KAAK,EAAEC,MAA6C,KAAK;MACxED,KAAK,CAACR,WAAW,GAAGS,MAAM,CAACE,OAAO;IACpC,CAAC;IACDkB,QAAQ,EAAEA,CAACrB,KAAK,EAAEC,MAAuC,KAAK;MAC5DD,KAAK,CAACP,KAAK,GAAGQ,MAAM,CAACE,OAAO;IAC9B,CAAC;IACDmB,UAAU,EAAEA,CAACtB,KAAK,EAAEC,MAA+D,KAAK;MACtFD,KAAK,CAACN,SAAS,GAAGO,MAAM,CAACE,OAAO,CAACT,SAAS;MAC1CM,KAAK,CAACL,cAAc,GAAGM,MAAM,CAACE,OAAO,CAACoB,OAAO,IAAI,EAAE;IACrD;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXxB,eAAe;EACfe,kBAAkB;EAClBG,kBAAkB;EAClBC,aAAa;EACbC,gBAAgB;EAChBC,cAAc;EACdC,QAAQ;EACRC;AACF,CAAC,GAAG1B,OAAO,CAAC4B,OAAO;AAEnB,eAAe5B,OAAO,CAAC6B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}