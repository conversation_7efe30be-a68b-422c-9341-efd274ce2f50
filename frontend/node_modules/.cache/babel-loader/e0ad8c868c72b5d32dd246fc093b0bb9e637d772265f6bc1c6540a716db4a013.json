{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\",\n  key: \"wrbu53\"\n}], [\"path\", {\n  d: \"M15 18H9\",\n  key: \"1lyqi6\"\n}], [\"path\", {\n  d: \"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14\",\n  key: \"lysw3i\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"332jqn\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}]];\nconst Truck = createLucideIcon(\"truck\", __iconNode);\nexport { __iconNode, Truck as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Truck", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/truck.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2', key: 'wrbu53' }],\n  ['path', { d: 'M15 18H9', key: '1lyqi6' }],\n  [\n    'path',\n    {\n      d: 'M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14',\n      key: 'lysw3i',\n    },\n  ],\n  ['circle', { cx: '17', cy: '18', r: '2', key: '332jqn' }],\n  ['circle', { cx: '7', cy: '18', r: '2', key: '19iecd' }],\n];\n\n/**\n * @component @name Truck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMThWNmEyIDIgMCAwIDAtMi0ySDRhMiAyIDAgMCAwLTIgMnYxMWExIDEgMCAwIDAgMSAxaDIiIC8+CiAgPHBhdGggZD0iTTE1IDE4SDkiIC8+CiAgPHBhdGggZD0iTTE5IDE4aDJhMSAxIDAgMCAwIDEtMXYtMy42NWExIDEgMCAwIDAtLjIyLS42MjRsLTMuNDgtNC4zNUExIDEgMCAwIDAgMTcuNTIgOEgxNCIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjE4IiByPSIyIiAvPgogIDxjaXJjbGUgY3g9IjciIGN5PSIxOCIgcj0iMiIgLz4KPC9zdmc+) - https://lucide.dev/icons/truck\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Truck = createLucideIcon('truck', __iconNode);\n\nexport default Truck;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,KAAA,GAAQC,gBAAiB,UAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}