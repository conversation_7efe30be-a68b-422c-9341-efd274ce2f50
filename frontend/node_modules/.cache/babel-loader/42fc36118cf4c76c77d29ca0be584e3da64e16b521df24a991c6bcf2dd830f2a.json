{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatPanel = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    messages,\n    isStreaming,\n    isLoading,\n    currentSessionId\n  } = useSelector(state => state.chat);\n  const {\n    sidebarCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentPaper\n  } = useSelector(state => state.papers);\n  const [inputValue, setInputValue] = useState('');\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n    const userMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString()\n    };\n    dispatch(addMessage(userMessage));\n    setInputValue('');\n    dispatch(setLoading(true));\n    try {\n      var _response$body;\n      // Create assistant message placeholder\n      const assistantMessageId = Date.now().toString();\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString()\n      }));\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(inputValue.trim(), currentSessionId || '', currentPaper === null || currentPaper === void 0 ? void 0 : currentPaper.paper_id);\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = (_response$body = response.body) === null || _response$body === void 0 ? void 0 : _response$body.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n      if (reader) {\n        while (true) {\n          const {\n            done,\n            value\n          } = await reader.read();\n          if (done) break;\n          const chunk = decoder.decode(value);\n          const lines = chunk.split('\\n');\n          for (const line of lines) {\n            if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n                switch (data.event) {\n                  case 'start':\n                    // Message already created\n                    break;\n                  case 'step':\n                    if (data.data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.data.tool_name}...`,\n                        duration: 3000\n                      }));\n                    }\n                    break;\n                  case 'response':\n                    if (data.data.message) {\n                      assistantContent = data.data.message;\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent\n                      }));\n                    }\n                    break;\n                  case 'complete':\n                    dispatch(setStreaming(false));\n                    return;\n                  case 'error':\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.data.error || 'An error occurred',\n                      duration: 5000\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n              }\n            }\n          }\n        }\n      }\n      dispatch(setStreaming(false));\n    } catch (error) {\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to send message',\n        duration: 5000\n      }));\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  if (sidebarCollapsed) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-panel-collapsed\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapsed-icon\",\n        children: /*#__PURE__*/_jsxDEV(Send, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-panel-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), currentPaper && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-paper-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDCC4 \", currentPaper.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-body\",\n      children: [/*#__PURE__*/_jsxDEV(MessageList, {\n        messages: messages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToolStatusList, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-footer\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        value: inputValue,\n        onChange: setInputValue,\n        onSend: handleSendMessage,\n        onKeyPress: handleKeyPress,\n        disabled: isStreaming || isLoading,\n        placeholder: \"Ask me anything about research papers...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatPanel, \"ZJBzwaaU7kXohrmqrUozXzX5jCE=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useSelector];\n});\n_c = ChatPanel;\nexport default ChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ChatPanel\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "addMessage", "updateMessage", "setStreaming", "setLoading", "addNotification", "MessageList", "MessageInput", "ToolStatusList", "Send", "api", "jsxDEV", "_jsxDEV", "ChatPanel", "_s", "dispatch", "messages", "isStreaming", "isLoading", "currentSessionId", "state", "chat", "sidebarCollapsed", "ui", "currentPaper", "papers", "inputValue", "setInputValue", "handleSendMessage", "trim", "userMessage", "id", "Date", "now", "toString", "role", "content", "timestamp", "toISOString", "_response$body", "assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "sendMessage", "paper_id", "ok", "Error", "reader", "body", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "chunk", "decode", "lines", "split", "line", "startsWith", "data", "JSON", "parse", "slice", "event", "type", "title", "message", "tool_name", "duration", "error", "e", "handleKeyPress", "key", "shift<PERSON>ey", "preventDefault", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "onSend", "onKeyPress", "disabled", "placeholder", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport { addMessage, updateMessage, setStreaming, setLoading } from '../../store/slices/chatSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport MessageList from './MessageList';\nimport MessageInput from './MessageInput';\nimport ToolStatusList from './ToolStatusList';\nimport { Send } from 'lucide-react';\nimport api from '../../services/api';\nimport './ChatPanel.css';\n\nconst ChatPanel: React.FC = () => {\n  const dispatch = useDispatch();\n  const { messages, isStreaming, isLoading, currentSessionId } = useSelector((state: RootState) => state.chat);\n  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentPaper } = useSelector((state: RootState) => state.papers);\n  const [inputValue, setInputValue] = useState('');\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim() || isStreaming || isLoading) return;\n\n    const userMessage = {\n      id: Date.now().toString(),\n      role: 'user' as const,\n      content: inputValue.trim(),\n      timestamp: new Date().toISOString(),\n    };\n\n    dispatch(addMessage(userMessage));\n    setInputValue('');\n    dispatch(setLoading(true));\n\n    try {\n      // Create assistant message placeholder\n      const assistantMessageId = Date.now().toString();\n      dispatch(addMessage({\n        id: assistantMessageId,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date().toISOString(),\n      }));\n\n      dispatch(setLoading(false));\n      dispatch(setStreaming(true));\n\n      // Start streaming chat\n      const response = await api.chat.sendMessage(\n        inputValue.trim(),\n        currentSessionId || '',\n        currentPaper?.paper_id\n      );\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      // Read the streaming response\n      const reader = response.body?.getReader();\n      const decoder = new TextDecoder();\n      let assistantContent = '';\n\n      if (reader) {\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n\n          const chunk = decoder.decode(value);\n          const lines = chunk.split('\\n');\n\n          for (const line of lines) {\n            if (line.startsWith('data: ')) {\n              try {\n                const data = JSON.parse(line.slice(6));\n\n                switch (data.event) {\n                  case 'start':\n                    // Message already created\n                    break;\n\n                  case 'step':\n                    if (data.data.type === 'tool_call') {\n                      dispatch(addNotification({\n                        type: 'info',\n                        title: 'Tool Execution',\n                        message: `Executing ${data.data.tool_name}...`,\n                        duration: 3000,\n                      }));\n                    }\n                    break;\n\n                  case 'response':\n                    if (data.data.message) {\n                      assistantContent = data.data.message;\n                      dispatch(updateMessage({\n                        id: assistantMessageId,\n                        content: assistantContent,\n                      }));\n                    }\n                    break;\n\n                  case 'complete':\n                    dispatch(setStreaming(false));\n                    return;\n\n                  case 'error':\n                    dispatch(setStreaming(false));\n                    dispatch(addNotification({\n                      type: 'error',\n                      title: 'Chat Error',\n                      message: data.data.error || 'An error occurred',\n                      duration: 5000,\n                    }));\n                    return;\n                }\n              } catch (e) {\n                // Skip invalid JSON lines\n              }\n            }\n          }\n        }\n      }\n\n      dispatch(setStreaming(false));\n\n    } catch (error) {\n      dispatch(setLoading(false));\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to send message',\n        duration: 5000,\n      }));\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n\n\n  if (sidebarCollapsed) {\n    return (\n      <div className=\"chat-panel-collapsed\">\n        <div className=\"collapsed-icon\">\n          <Send size={20} />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"chat-panel-content\">\n      <div className=\"chat-header\">\n        <h2>Chat</h2>\n        {currentPaper && (\n          <div className=\"current-paper-indicator\">\n            <span>📄 {currentPaper.title}</span>\n          </div>\n        )}\n      </div>\n\n      <div className=\"chat-body\">\n        <MessageList messages={messages} />\n        <ToolStatusList />\n      </div>\n\n      <div className=\"chat-footer\">\n        <MessageInput\n          value={inputValue}\n          onChange={setInputValue}\n          onSend={handleSendMessage}\n          onKeyPress={handleKeyPress}\n          disabled={isStreaming || isLoading}\n          placeholder=\"Ask me anything about research papers...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default ChatPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,UAAU,EAAEC,aAAa,EAAEC,YAAY,EAAEC,UAAU,QAAQ,8BAA8B;AAClG,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,QAAQ;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAiB,CAAC,GAAGnB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC5G,MAAM;IAAEC;EAAiB,CAAC,GAAGtB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACG,EAAE,CAAC;EACxE,MAAM;IAAEC;EAAa,CAAC,GAAGxB,WAAW,CAAEoB,KAAgB,IAAKA,KAAK,CAACK,MAAM,CAAC;EACxE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM8B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACF,UAAU,CAACG,IAAI,CAAC,CAAC,IAAIZ,WAAW,IAAIC,SAAS,EAAE;IAEpD,MAAMY,WAAW,GAAG;MAClBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,IAAI,EAAE,MAAe;MACrBC,OAAO,EAAEV,UAAU,CAACG,IAAI,CAAC,CAAC;MAC1BQ,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;IACpC,CAAC;IAEDvB,QAAQ,CAACd,UAAU,CAAC6B,WAAW,CAAC,CAAC;IACjCH,aAAa,CAAC,EAAE,CAAC;IACjBZ,QAAQ,CAACX,UAAU,CAAC,IAAI,CAAC,CAAC;IAE1B,IAAI;MAAA,IAAAmC,cAAA;MACF;MACA,MAAMC,kBAAkB,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MAChDnB,QAAQ,CAACd,UAAU,CAAC;QAClB8B,EAAE,EAAES,kBAAkB;QACtBL,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;MACpC,CAAC,CAAC,CAAC;MAEHvB,QAAQ,CAACX,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BW,QAAQ,CAACZ,YAAY,CAAC,IAAI,CAAC,CAAC;;MAE5B;MACA,MAAMsC,QAAQ,GAAG,MAAM/B,GAAG,CAACW,IAAI,CAACqB,WAAW,CACzChB,UAAU,CAACG,IAAI,CAAC,CAAC,EACjBV,gBAAgB,IAAI,EAAE,EACtBK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEmB,QAChB,CAAC;MAED,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;;MAEA;MACA,MAAMC,MAAM,IAAAP,cAAA,GAAGE,QAAQ,CAACM,IAAI,cAAAR,cAAA,uBAAbA,cAAA,CAAeS,SAAS,CAAC,CAAC;MACzC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MACjC,IAAIC,gBAAgB,GAAG,EAAE;MAEzB,IAAIL,MAAM,EAAE;QACV,OAAO,IAAI,EAAE;UACX,MAAM;YAAEM,IAAI;YAAEC;UAAM,CAAC,GAAG,MAAMP,MAAM,CAACQ,IAAI,CAAC,CAAC;UAC3C,IAAIF,IAAI,EAAE;UAEV,MAAMG,KAAK,GAAGN,OAAO,CAACO,MAAM,CAACH,KAAK,CAAC;UACnC,MAAMI,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;UAE/B,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;YACxB,IAAIE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;cAC7B,IAAI;gBACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtC,QAAQH,IAAI,CAACI,KAAK;kBAChB,KAAK,OAAO;oBACV;oBACA;kBAEF,KAAK,MAAM;oBACT,IAAIJ,IAAI,CAACA,IAAI,CAACK,IAAI,KAAK,WAAW,EAAE;sBAClCnD,QAAQ,CAACV,eAAe,CAAC;wBACvB6D,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,gBAAgB;wBACvBC,OAAO,EAAE,aAAaP,IAAI,CAACA,IAAI,CAACQ,SAAS,KAAK;wBAC9CC,QAAQ,EAAE;sBACZ,CAAC,CAAC,CAAC;oBACL;oBACA;kBAEF,KAAK,UAAU;oBACb,IAAIT,IAAI,CAACA,IAAI,CAACO,OAAO,EAAE;sBACrBjB,gBAAgB,GAAGU,IAAI,CAACA,IAAI,CAACO,OAAO;sBACpCrD,QAAQ,CAACb,aAAa,CAAC;wBACrB6B,EAAE,EAAES,kBAAkB;wBACtBJ,OAAO,EAAEe;sBACX,CAAC,CAAC,CAAC;oBACL;oBACA;kBAEF,KAAK,UAAU;oBACbpC,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7B;kBAEF,KAAK,OAAO;oBACVY,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;oBAC7BY,QAAQ,CAACV,eAAe,CAAC;sBACvB6D,IAAI,EAAE,OAAO;sBACbC,KAAK,EAAE,YAAY;sBACnBC,OAAO,EAAEP,IAAI,CAACA,IAAI,CAACU,KAAK,IAAI,mBAAmB;sBAC/CD,QAAQ,EAAE;oBACZ,CAAC,CAAC,CAAC;oBACH;gBACJ;cACF,CAAC,CAAC,OAAOE,CAAC,EAAE;gBACV;cAAA;YAEJ;UACF;QACF;MACF;MAEAzD,QAAQ,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC;IAE/B,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdxD,QAAQ,CAACX,UAAU,CAAC,KAAK,CAAC,CAAC;MAC3BW,QAAQ,CAACV,eAAe,CAAC;QACvB6D,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,OAAO;QACdC,OAAO,EAAE,wBAAwB;QACjCE,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMG,cAAc,GAAID,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACE,GAAG,KAAK,OAAO,IAAI,CAACF,CAAC,CAACG,QAAQ,EAAE;MACpCH,CAAC,CAACI,cAAc,CAAC,CAAC;MAClBhD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAID,IAAIN,gBAAgB,EAAE;IACpB,oBACEV,OAAA;MAAKiE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnClE,OAAA;QAAKiE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BlE,OAAA,CAACH,IAAI;UAACsE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvE,OAAA;IAAKiE,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjClE,OAAA;MAAKiE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlE,OAAA;QAAAkE,QAAA,EAAI;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACZ3D,YAAY,iBACXZ,OAAA;QAAKiE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtClE,OAAA;UAAAkE,QAAA,GAAM,eAAG,EAACtD,YAAY,CAAC2C,KAAK;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvE,OAAA;MAAKiE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlE,OAAA,CAACN,WAAW;QAACU,QAAQ,EAAEA;MAAS;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnCvE,OAAA,CAACJ,cAAc;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENvE,OAAA;MAAKiE,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BlE,OAAA,CAACL,YAAY;QACX8C,KAAK,EAAE3B,UAAW;QAClB0D,QAAQ,EAAEzD,aAAc;QACxB0D,MAAM,EAAEzD,iBAAkB;QAC1B0D,UAAU,EAAEb,cAAe;QAC3Bc,QAAQ,EAAEtE,WAAW,IAAIC,SAAU;QACnCsE,WAAW,EAAC;MAA0C;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrE,EAAA,CA3KID,SAAmB;EAAA,QACNd,WAAW,EACmCC,WAAW,EAC7CA,WAAW,EACfA,WAAW;AAAA;AAAAyF,EAAA,GAJhC5E,SAAmB;AA6KzB,eAAeA,SAAS;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}