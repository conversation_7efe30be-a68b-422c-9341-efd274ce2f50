{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"8\",\n  key: \"46899m\"\n}], [\"path\", {\n  d: \"M12 2v7.5\",\n  key: \"1e5rl5\"\n}], [\"path\", {\n  d: \"m19 5-5.23 5.23\",\n  key: \"1ezxxf\"\n}], [\"path\", {\n  d: \"M22 12h-7.5\",\n  key: \"le1719\"\n}], [\"path\", {\n  d: \"m19 19-5.23-5.23\",\n  key: \"p3fmgn\"\n}], [\"path\", {\n  d: \"M12 14.5V22\",\n  key: \"dgcmos\"\n}], [\"path\", {\n  d: \"M10.23 13.77 5 19\",\n  key: \"qwopd4\"\n}], [\"path\", {\n  d: \"M9.5 12H2\",\n  key: \"r7bup8\"\n}], [\"path\", {\n  d: \"M10.23 10.23 5 5\",\n  key: \"k2y7lj\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2.5\",\n  key: \"ix0uyj\"\n}]];\nconst ShipWheel = createLucideIcon(\"ship-wheel\", __iconNode);\nexport { __iconNode, ShipWheel as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "ShipWheel", "createLucideIcon"], "sources": ["/home/<USER>/paper_ui/frontend/node_modules/lucide-react/src/icons/ship-wheel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '8', key: '46899m' }],\n  ['path', { d: 'M12 2v7.5', key: '1e5rl5' }],\n  ['path', { d: 'm19 5-5.23 5.23', key: '1ezxxf' }],\n  ['path', { d: 'M22 12h-7.5', key: 'le1719' }],\n  ['path', { d: 'm19 19-5.23-5.23', key: 'p3fmgn' }],\n  ['path', { d: 'M12 14.5V22', key: 'dgcmos' }],\n  ['path', { d: 'M10.23 13.77 5 19', key: 'qwopd4' }],\n  ['path', { d: 'M9.5 12H2', key: 'r7bup8' }],\n  ['path', { d: 'M10.23 10.23 5 5', key: 'k2y7lj' }],\n  ['circle', { cx: '12', cy: '12', r: '2.5', key: 'ix0uyj' }],\n];\n\n/**\n * @component @name ShipWheel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI4IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjcuNSIgLz4KICA8cGF0aCBkPSJtMTkgNS01LjIzIDUuMjMiIC8+CiAgPHBhdGggZD0iTTIyIDEyaC03LjUiIC8+CiAgPHBhdGggZD0ibTE5IDE5LTUuMjMtNS4yMyIgLz4KICA8cGF0aCBkPSJNMTIgMTQuNVYyMiIgLz4KICA8cGF0aCBkPSJNMTAuMjMgMTMuNzcgNSAxOSIgLz4KICA8cGF0aCBkPSJNOS41IDEySDIiIC8+CiAgPHBhdGggZD0iTTEwLjIzIDEwLjIzIDUgNSIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/ship-wheel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShipWheel = createLucideIcon('ship-wheel', __iconNode);\n\nexport default ShipWheel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAOC,GAAK;AAAU,GAC5D;AAaM,MAAAE,SAAA,GAAYC,gBAAiB,eAAcP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}