{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport './PaperEditor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaperEditor = ({\n  paper\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n  const handleContentChange = useCallback(value => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n  const handleTextareaChange = e => {\n    handleContentChange(e.target.value);\n  };\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n    setIsSaving(true);\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/papers/${paper.paper_id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content,\n          title: paper.title\n        })\n      });\n      if (response.ok) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000\n        }));\n      } else {\n        throw new Error('Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleExport = () => {\n    const blob = new Blob([content], {\n      type: 'text/markdown'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n  const renderPreview = () => {\n    const html = marked.parse(content, {\n      async: false\n    });\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: sanitizedHtml\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 12\n    }, this);\n  };\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"paper-editor\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: paper.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"editor-meta\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"word-count\",\n            children: [getWordCount(), \" words\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status-indicator\",\n            children: hasUnsavedChanges ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"unsaved\",\n              children: \"\\u25CF Unsaved changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"saved\",\n              children: \"\\u2713 Saved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsPreviewMode(!isPreviewMode),\n          className: \"editor-button\",\n          title: isPreviewMode ? 'Edit mode' : 'Preview mode',\n          children: [isPreviewMode ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 30\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 53\n          }, this), isPreviewMode ? 'Edit' : 'Preview']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          disabled: !hasUnsavedChanges || isSaving,\n          className: \"editor-button save-button\",\n          title: \"Save changes\",\n          children: [/*#__PURE__*/_jsxDEV(Save, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), isSaving ? 'Saving...' : 'Save']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExport,\n          className: \"editor-button\",\n          title: \"Export as Markdown\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      children: isPreviewMode ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-pane\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"markdown-content\",\n          children: renderPreview()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"edit-pane\",\n        children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: content,\n          onChange: handleContentChange,\n          className: \"editor-textarea\",\n          placeholder: \"Start writing your paper here...\",\n          spellCheck: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Characters: \", content.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Words: \", getWordCount()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Lines: \", content.split('\\n').length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-help\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Markdown supported \\u2022 Ctrl+S to save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(PaperEditor, \"7LhUVuaq934TwSCtFYDIhl2wgK8=\", false, function () {\n  return [useDispatch];\n});\n_c = PaperEditor;\nexport default PaperEditor;\nvar _c;\n$RefreshReg$(_c, \"PaperEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useDispatch", "updateCurrentPaperContent", "addNotification", "Save", "Eye", "Eye<PERSON>ff", "Download", "marked", "DOMPurify", "jsxDEV", "_jsxDEV", "PaperEditor", "paper", "_s", "dispatch", "content", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewMode", "setIsPreviewMode", "isSplitView", "setIsSplitView", "isSaving", "setIsSaving", "hasUnsavedChanges", "setHasUnsavedChanges", "isFullscreen", "setIsFullscreen", "paper_id", "handleContentChange", "value", "newContent", "handleTextareaChange", "e", "target", "handleSave", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "title", "ok", "type", "message", "duration", "Error", "error", "handleExport", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "replace", "toLowerCase", "click", "revokeObjectURL", "renderPreview", "html", "parse", "async", "sanitizedHtml", "sanitize", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWordCount", "trim", "split", "filter", "word", "length", "className", "children", "onClick", "size", "disabled", "onChange", "placeholder", "spell<PERSON>heck", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { Paper, updateCurrentPaperContent } from '../../store/slices/papersSlice';\nimport { addNotification } from '../../store/slices/uiSlice';\nimport { Save, Eye, EyeOff, Download, Split, Maximize2 } from 'lucide-react';\nimport { marked } from 'marked';\nimport DOMPurify from 'dompurify';\nimport Editor from '@monaco-editor/react';\nimport api from '../../services/api';\nimport './PaperEditor.css';\n\ninterface PaperEditorProps {\n  paper: Paper;\n}\n\nconst PaperEditor: React.FC<PaperEditorProps> = ({ paper }) => {\n  const dispatch = useDispatch();\n  const [content, setContent] = useState(paper.content);\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\n  const [isSplitView, setIsSplitView] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n\n  useEffect(() => {\n    setContent(paper.content);\n    setHasUnsavedChanges(false);\n  }, [paper.paper_id, paper.content]);\n\n  useEffect(() => {\n    setHasUnsavedChanges(content !== paper.content);\n  }, [content, paper.content]);\n\n  const handleContentChange = useCallback((value: string | undefined) => {\n    const newContent = value || '';\n    setContent(newContent);\n    dispatch(updateCurrentPaperContent(newContent));\n  }, [dispatch]);\n\n  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    handleContentChange(e.target.value);\n  };\n\n  const handleSave = async () => {\n    if (!hasUnsavedChanges) return;\n\n    setIsSaving(true);\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/papers/${paper.paper_id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content,\n          title: paper.title,\n        }),\n      });\n\n      if (response.ok) {\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error('Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleExport = () => {\n    const blob = new Blob([content], { type: 'text/markdown' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `${paper.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;\n    link.click();\n    URL.revokeObjectURL(url);\n  };\n\n  const renderPreview = () => {\n    const html = marked.parse(content, { async: false }) as string;\n    const sanitizedHtml = DOMPurify.sanitize(html);\n    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;\n  };\n\n  const getWordCount = () => {\n    return content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  };\n\n  return (\n    <div className=\"paper-editor\">\n      <div className=\"editor-header\">\n        <div className=\"editor-title\">\n          <h2>{paper.title}</h2>\n          <div className=\"editor-meta\">\n            <span className=\"word-count\">{getWordCount()} words</span>\n            <span className=\"status-indicator\">\n              {hasUnsavedChanges ? (\n                <span className=\"unsaved\">● Unsaved changes</span>\n              ) : (\n                <span className=\"saved\">✓ Saved</span>\n              )}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"editor-actions\">\n          <button\n            onClick={() => setIsPreviewMode(!isPreviewMode)}\n            className=\"editor-button\"\n            title={isPreviewMode ? 'Edit mode' : 'Preview mode'}\n          >\n            {isPreviewMode ? <EyeOff size={16} /> : <Eye size={16} />}\n            {isPreviewMode ? 'Edit' : 'Preview'}\n          </button>\n\n          <button\n            onClick={handleSave}\n            disabled={!hasUnsavedChanges || isSaving}\n            className=\"editor-button save-button\"\n            title=\"Save changes\"\n          >\n            <Save size={16} />\n            {isSaving ? 'Saving...' : 'Save'}\n          </button>\n\n          <button\n            onClick={handleExport}\n            className=\"editor-button\"\n            title=\"Export as Markdown\"\n          >\n            <Download size={16} />\n            Export\n          </button>\n        </div>\n      </div>\n\n      <div className=\"editor-content\">\n        {isPreviewMode ? (\n          <div className=\"preview-pane\">\n            <div className=\"markdown-content\">\n              {renderPreview()}\n            </div>\n          </div>\n        ) : (\n          <div className=\"edit-pane\">\n            <textarea\n              value={content}\n              onChange={handleContentChange}\n              className=\"editor-textarea\"\n              placeholder=\"Start writing your paper here...\"\n              spellCheck={true}\n            />\n          </div>\n        )}\n      </div>\n\n      <div className=\"editor-footer\">\n        <div className=\"editor-stats\">\n          <span>Characters: {content.length}</span>\n          <span>Words: {getWordCount()}</span>\n          <span>Lines: {content.split('\\n').length}</span>\n        </div>\n\n        <div className=\"editor-help\">\n          <span>Markdown supported • Ctrl+S to save</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PaperEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAAgBC,yBAAyB,QAAQ,gCAAgC;AACjF,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,QAA0B,cAAc;AAC5E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,SAAS,MAAM,WAAW;AAGjC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3B,MAAMC,WAAuC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAACe,KAAK,CAACG,OAAO,CAAC;EACrD,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdkB,UAAU,CAACJ,KAAK,CAACG,OAAO,CAAC;IACzBS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACZ,KAAK,CAACe,QAAQ,EAAEf,KAAK,CAACG,OAAO,CAAC,CAAC;EAEnCjB,SAAS,CAAC,MAAM;IACd0B,oBAAoB,CAACT,OAAO,KAAKH,KAAK,CAACG,OAAO,CAAC;EACjD,CAAC,EAAE,CAACA,OAAO,EAAEH,KAAK,CAACG,OAAO,CAAC,CAAC;EAE5B,MAAMa,mBAAmB,GAAG7B,WAAW,CAAE8B,KAAyB,IAAK;IACrE,MAAMC,UAAU,GAAGD,KAAK,IAAI,EAAE;IAC9Bb,UAAU,CAACc,UAAU,CAAC;IACtBhB,QAAQ,CAACb,yBAAyB,CAAC6B,UAAU,CAAC,CAAC;EACjD,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMiB,oBAAoB,GAAIC,CAAyC,IAAK;IAC1EJ,mBAAmB,CAACI,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAC;EACrC,CAAC;EAED,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACX,iBAAiB,EAAE;IAExBD,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuCxB,KAAK,CAACe,QAAQ,EAAE,EAAE;QACpFU,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB1B,OAAO;UACP2B,KAAK,EAAE9B,KAAK,CAAC8B;QACf,CAAC;MACH,CAAC,CAAC;MAEF,IAAIP,QAAQ,CAACQ,EAAE,EAAE;QACfnB,oBAAoB,CAAC,KAAK,CAAC;QAC3BV,QAAQ,CAACZ,eAAe,CAAC;UACvB0C,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,aAAa;UACpBG,OAAO,EAAE,4CAA4C;UACrDC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,gBAAgB,CAAC;MACnC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlC,QAAQ,CAACZ,eAAe,CAAC;QACvB0C,IAAI,EAAE,OAAO;QACbF,KAAK,EAAE,aAAa;QACpBG,OAAO,EAAE,gDAAgD;QACzDC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRxB,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpC,OAAO,CAAC,EAAE;MAAE6B,IAAI,EAAE;IAAgB,CAAC,CAAC;IAC3D,MAAMQ,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,GAAG/C,KAAK,CAAC8B,KAAK,CAACkB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK;IAC7EN,IAAI,CAACO,KAAK,CAAC,CAAC;IACZT,GAAG,CAACU,eAAe,CAACX,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAG1D,MAAM,CAAC2D,KAAK,CAACnD,OAAO,EAAE;MAAEoD,KAAK,EAAE;IAAM,CAAC,CAAW;IAC9D,MAAMC,aAAa,GAAG5D,SAAS,CAAC6D,QAAQ,CAACJ,IAAI,CAAC;IAC9C,oBAAOvD,OAAA;MAAK4D,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAc;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO7D,OAAO,CAAC8D,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC3E,CAAC;EAED,oBACEvE,OAAA;IAAKwE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BzE,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QAAKwE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzE,OAAA;UAAAyE,QAAA,EAAKvE,KAAK,CAAC8B;QAAK;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtBjE,OAAA;UAAKwE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BzE,OAAA;YAAMwE,SAAS,EAAC,YAAY;YAAAC,QAAA,GAAEP,YAAY,CAAC,CAAC,EAAC,QAAM;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1DjE,OAAA;YAAMwE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC/B5D,iBAAiB,gBAChBb,OAAA;cAAMwE,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAiB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAElDjE,OAAA;cAAMwE,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAO;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACtC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA;QAAKwE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzE,OAAA;UACE0E,OAAO,EAAEA,CAAA,KAAMlE,gBAAgB,CAAC,CAACD,aAAa,CAAE;UAChDiE,SAAS,EAAC,eAAe;UACzBxC,KAAK,EAAEzB,aAAa,GAAG,WAAW,GAAG,cAAe;UAAAkE,QAAA,GAEnDlE,aAAa,gBAAGP,OAAA,CAACL,MAAM;YAACgF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,GAAG;YAACiF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxD1D,aAAa,GAAG,MAAM,GAAG,SAAS;QAAA;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAETjE,OAAA;UACE0E,OAAO,EAAElD,UAAW;UACpBoD,QAAQ,EAAE,CAAC/D,iBAAiB,IAAIF,QAAS;UACzC6D,SAAS,EAAC,2BAA2B;UACrCxC,KAAK,EAAC,cAAc;UAAAyC,QAAA,gBAEpBzE,OAAA,CAACP,IAAI;YAACkF,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjBtD,QAAQ,GAAG,WAAW,GAAG,MAAM;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAETjE,OAAA;UACE0E,OAAO,EAAEnC,YAAa;UACtBiC,SAAS,EAAC,eAAe;UACzBxC,KAAK,EAAC,oBAAoB;UAAAyC,QAAA,gBAE1BzE,OAAA,CAACJ,QAAQ;YAAC+E,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjE,OAAA;MAAKwE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BlE,aAAa,gBACZP,OAAA;QAAKwE,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzE,OAAA;UAAKwE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC9BnB,aAAa,CAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENjE,OAAA;QAAKwE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBzE,OAAA;UACEmB,KAAK,EAAEd,OAAQ;UACfwE,QAAQ,EAAE3D,mBAAoB;UAC9BsD,SAAS,EAAC,iBAAiB;UAC3BM,WAAW,EAAC,kCAAkC;UAC9CC,UAAU,EAAE;QAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjE,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QAAKwE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BzE,OAAA;UAAAyE,QAAA,GAAM,cAAY,EAACpE,OAAO,CAACkE,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCjE,OAAA;UAAAyE,QAAA,GAAM,SAAO,EAACP,YAAY,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCjE,OAAA;UAAAyE,QAAA,GAAM,SAAO,EAACpE,OAAO,CAAC+D,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAENjE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BzE,OAAA;UAAAyE,QAAA,EAAM;QAAmC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAxKIF,WAAuC;EAAA,QAC1BX,WAAW;AAAA;AAAA0F,EAAA,GADxB/E,WAAuC;AA0K7C,eAAeA,WAAW;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}