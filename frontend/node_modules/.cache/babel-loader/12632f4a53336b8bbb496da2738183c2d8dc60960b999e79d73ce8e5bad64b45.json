{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport Header from './Header';\nimport ChatPanel from '../Chat/ChatPanel';\nimport MainWorkArea from './MainWorkArea';\nimport ContextPanel from './ContextPanel';\nimport NotificationContainer from '../UI/NotificationContainer';\nimport { setCurrentSession } from '../../store/slices/sessionSlice';\nimport './Layout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    sidebarCollapsed,\n    rightPanelCollapsed\n  } = useSelector(state => state.ui);\n  const {\n    currentSession\n  } = useSelector(state => state.session);\n  const initializeSession = async () => {\n    try {\n      const response = await fetch('/api/v1/sessions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.ok) {\n        const sessionData = await response.json();\n        dispatch(setCurrentSession({\n          session_id: sessionData.session_id,\n          created_at: sessionData.created_at,\n          last_activity: new Date().toISOString(),\n          metadata: {}\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to initialize session:', error);\n    }\n  };\n  useEffect(() => {\n    // Initialize session on app load\n    if (!currentSession) {\n      initializeSession();\n    }\n  }, [currentSession]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"layout-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(ChatPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-work-area\",\n        children: /*#__PURE__*/_jsxDEV(MainWorkArea, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(ContextPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NotificationContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"Gwr3zLFgSHzAfvW8tI20yr631nM=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Header", "ChatPanel", "MainWorkArea", "ContextPanel", "NotificationContainer", "setCurrentSession", "jsxDEV", "_jsxDEV", "Layout", "_s", "dispatch", "sidebarCollapsed", "rightPanelCollapsed", "state", "ui", "currentSession", "session", "initializeSession", "response", "fetch", "method", "headers", "ok", "sessionData", "json", "session_id", "created_at", "last_activity", "Date", "toISOString", "metadata", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\nimport Header from './Header';\nimport ChatPanel from '../Chat/ChatPanel';\nimport MainWorkArea from './MainWorkArea';\nimport ContextPanel from './ContextPanel';\nimport NotificationContainer from '../UI/NotificationContainer';\nimport { setCurrentSession } from '../../store/slices/sessionSlice';\nimport './Layout.css';\n\nconst Layout: React.FC = () => {\n  const dispatch = useDispatch();\n  const { sidebarCollapsed, rightPanelCollapsed } = useSelector((state: RootState) => state.ui);\n  const { currentSession } = useSelector((state: RootState) => state.session);\n\n  const initializeSession = async () => {\n    try {\n      const response = await fetch('/api/v1/sessions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (response.ok) {\n        const sessionData = await response.json();\n        dispatch(setCurrentSession({\n          session_id: sessionData.session_id,\n          created_at: sessionData.created_at,\n          last_activity: new Date().toISOString(),\n          metadata: {},\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to initialize session:', error);\n    }\n  };\n\n  useEffect(() => {\n    // Initialize session on app load\n    if (!currentSession) {\n      initializeSession();\n    }\n  }, [currentSession]);\n\n  return (\n    <div className=\"layout\">\n      <Header />\n\n      <div className=\"layout-body\">\n        {/* Chat Panel - Left Side */}\n        <div className={`chat-panel ${sidebarCollapsed ? 'collapsed' : ''}`}>\n          <ChatPanel />\n        </div>\n\n        {/* Main Work Area - Center */}\n        <div className=\"main-work-area\">\n          <MainWorkArea />\n        </div>\n\n        {/* Context Panel - Right Side */}\n        <div className={`context-panel ${rightPanelCollapsed ? 'collapsed' : ''}`}>\n          <ContextPanel />\n        </div>\n      </div>\n\n      {/* Global Notifications */}\n      <NotificationContainer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEa,gBAAgB;IAAEC;EAAoB,CAAC,GAAGb,WAAW,CAAEc,KAAgB,IAAKA,KAAK,CAACC,EAAE,CAAC;EAC7F,MAAM;IAAEC;EAAe,CAAC,GAAGhB,WAAW,CAAEc,KAAgB,IAAKA,KAAK,CAACG,OAAO,CAAC;EAE3E,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkB,EAAE;QAC/CC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,WAAW,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACzCd,QAAQ,CAACL,iBAAiB,CAAC;UACzBoB,UAAU,EAAEF,WAAW,CAACE,UAAU;UAClCC,UAAU,EAAEH,WAAW,CAACG,UAAU;UAClCC,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACvCC,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACkB,cAAc,EAAE;MACnBE,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACF,cAAc,CAAC,CAAC;EAEpB,oBACER,OAAA;IAAK0B,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrB3B,OAAA,CAACP,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEV/B,OAAA;MAAK0B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAE1B3B,OAAA;QAAK0B,SAAS,EAAE,cAActB,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAuB,QAAA,eAClE3B,OAAA,CAACN,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN/B,OAAA;QAAK0B,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B3B,OAAA,CAACL,YAAY;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGN/B,OAAA;QAAK0B,SAAS,EAAE,iBAAiBrB,mBAAmB,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAsB,QAAA,eACxE3B,OAAA,CAACJ,YAAY;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/B,OAAA,CAACH,qBAAqB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA5DID,MAAgB;EAAA,QACHV,WAAW,EACsBC,WAAW,EAClCA,WAAW;AAAA;AAAAwC,EAAA,GAHlC/B,MAAgB;AA8DtB,eAAeA,MAAM;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}