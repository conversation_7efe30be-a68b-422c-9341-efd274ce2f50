{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  papers: [],\n  currentPaper: null,\n  isLoading: false,\n  error: null,\n  searchQuery: '',\n  filteredPapers: [],\n  cursorPosition: null\n};\nconst papersSlice = createSlice({\n  name: 'papers',\n  initialState,\n  reducers: {\n    setPapers: (state, action) => {\n      state.papers = action.payload;\n      state.filteredPapers = action.payload;\n    },\n    addPaper: (state, action) => {\n      state.papers.push(action.payload);\n      state.filteredPapers = state.papers.filter(paper => paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) || paper.content.toLowerCase().includes(state.searchQuery.toLowerCase()));\n    },\n    updatePaper: (state, action) => {\n      const index = state.papers.findIndex(p => p.paper_id === action.payload.paper_id);\n      if (index !== -1) {\n        var _state$currentPaper;\n        state.papers[index] = action.payload;\n        if (((_state$currentPaper = state.currentPaper) === null || _state$currentPaper === void 0 ? void 0 : _state$currentPaper.paper_id) === action.payload.paper_id) {\n          state.currentPaper = action.payload;\n        }\n      }\n      state.filteredPapers = state.papers.filter(paper => paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) || paper.content.toLowerCase().includes(state.searchQuery.toLowerCase()));\n    },\n    deletePaper: (state, action) => {\n      var _state$currentPaper2;\n      state.papers = state.papers.filter(p => p.paper_id !== action.payload);\n      if (((_state$currentPaper2 = state.currentPaper) === null || _state$currentPaper2 === void 0 ? void 0 : _state$currentPaper2.paper_id) === action.payload) {\n        state.currentPaper = null;\n      }\n      state.filteredPapers = state.papers.filter(paper => paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) || paper.content.toLowerCase().includes(state.searchQuery.toLowerCase()));\n    },\n    setCurrentPaper: (state, action) => {\n      state.currentPaper = action.payload;\n    },\n    updateCurrentPaperContent: (state, action) => {\n      if (state.currentPaper) {\n        state.currentPaper.content = action.payload;\n        state.currentPaper.updated_at = new Date().toISOString();\n\n        // Update in papers array too\n        const index = state.papers.findIndex(p => p.paper_id === state.currentPaper.paper_id);\n        if (index !== -1) {\n          state.papers[index] = {\n            ...state.currentPaper\n          };\n        }\n      }\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    setSearchQuery: (state, action) => {\n      state.searchQuery = action.payload;\n      state.filteredPapers = state.papers.filter(paper => paper.title.toLowerCase().includes(action.payload.toLowerCase()) || paper.content.toLowerCase().includes(action.payload.toLowerCase()));\n    },\n    setCursorPosition: (state, action) => {\n      state.cursorPosition = action.payload;\n    }\n  }\n});\nexport const {\n  setPapers,\n  addPaper,\n  updatePaper,\n  deletePaper,\n  setCurrentPaper,\n  updateCurrentPaperContent,\n  setLoading,\n  setError,\n  setSearchQuery\n} = papersSlice.actions;\nexport default papersSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "papers", "currentPaper", "isLoading", "error", "searchQuery", "filteredPapers", "cursorPosition", "papersSlice", "name", "reducers", "setPapers", "state", "action", "payload", "addPaper", "push", "filter", "paper", "title", "toLowerCase", "includes", "content", "updatePaper", "index", "findIndex", "p", "paper_id", "_state$currentPaper", "deletePaper", "_state$currentPaper2", "setCurrentPaper", "updateCurrentPaperContent", "updated_at", "Date", "toISOString", "setLoading", "setError", "setSearch<PERSON>uery", "setCursorPosition", "actions", "reducer"], "sources": ["/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Paper {\n  paper_id: string;\n  title: string;\n  content: string;\n  status: 'draft' | 'in_progress' | 'completed';\n  created_at: string;\n  updated_at: string;\n  airtable_record_id?: string;\n  metadata?: {\n    word_count?: number;\n    citations?: any[];\n    outline?: any;\n  };\n}\n\nexport interface PapersState {\n  papers: Paper[];\n  currentPaper: Paper | null;\n  isLoading: boolean;\n  error: string | null;\n  searchQuery: string;\n  filteredPapers: Paper[];\n  cursorPosition: { line: number; column: number } | null;\n}\n\nconst initialState: PapersState = {\n  papers: [],\n  currentPaper: null,\n  isLoading: false,\n  error: null,\n  searchQuery: '',\n  filteredPapers: [],\n  cursorPosition: null,\n};\n\nconst papersSlice = createSlice({\n  name: 'papers',\n  initialState,\n  reducers: {\n    setPapers: (state, action: PayloadAction<Paper[]>) => {\n      state.papers = action.payload;\n      state.filteredPapers = action.payload;\n    },\n    addPaper: (state, action: PayloadAction<Paper>) => {\n      state.papers.push(action.payload);\n      state.filteredPapers = state.papers.filter(paper =>\n        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||\n        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())\n      );\n    },\n    updatePaper: (state, action: PayloadAction<Paper>) => {\n      const index = state.papers.findIndex(p => p.paper_id === action.payload.paper_id);\n      if (index !== -1) {\n        state.papers[index] = action.payload;\n        if (state.currentPaper?.paper_id === action.payload.paper_id) {\n          state.currentPaper = action.payload;\n        }\n      }\n      state.filteredPapers = state.papers.filter(paper =>\n        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||\n        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())\n      );\n    },\n    deletePaper: (state, action: PayloadAction<string>) => {\n      state.papers = state.papers.filter(p => p.paper_id !== action.payload);\n      if (state.currentPaper?.paper_id === action.payload) {\n        state.currentPaper = null;\n      }\n      state.filteredPapers = state.papers.filter(paper =>\n        paper.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||\n        paper.content.toLowerCase().includes(state.searchQuery.toLowerCase())\n      );\n    },\n    setCurrentPaper: (state, action: PayloadAction<Paper | null>) => {\n      state.currentPaper = action.payload;\n    },\n    updateCurrentPaperContent: (state, action: PayloadAction<string>) => {\n      if (state.currentPaper) {\n        state.currentPaper.content = action.payload;\n        state.currentPaper.updated_at = new Date().toISOString();\n\n        // Update in papers array too\n        const index = state.papers.findIndex(p => p.paper_id === state.currentPaper!.paper_id);\n        if (index !== -1) {\n          state.papers[index] = { ...state.currentPaper };\n        }\n      }\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    setSearchQuery: (state, action: PayloadAction<string>) => {\n      state.searchQuery = action.payload;\n      state.filteredPapers = state.papers.filter(paper =>\n        paper.title.toLowerCase().includes(action.payload.toLowerCase()) ||\n        paper.content.toLowerCase().includes(action.payload.toLowerCase())\n      );\n    },\n    setCursorPosition: (state, action: PayloadAction<{ line: number; column: number } | null>) => {\n      state.cursorPosition = action.payload;\n    },\n  },\n});\n\nexport const {\n  setPapers,\n  addPaper,\n  updatePaper,\n  deletePaper,\n  setCurrentPaper,\n  updateCurrentPaperContent,\n  setLoading,\n  setError,\n  setSearchQuery,\n} = papersSlice.actions;\n\nexport default papersSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AA2B7D,MAAMC,YAAyB,GAAG;EAChCC,MAAM,EAAE,EAAE;EACVC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,WAAW,GAAGT,WAAW,CAAC;EAC9BU,IAAI,EAAE,QAAQ;EACdT,YAAY;EACZU,QAAQ,EAAE;IACRC,SAAS,EAAEA,CAACC,KAAK,EAAEC,MAA8B,KAAK;MACpDD,KAAK,CAACX,MAAM,GAAGY,MAAM,CAACC,OAAO;MAC7BF,KAAK,CAACN,cAAc,GAAGO,MAAM,CAACC,OAAO;IACvC,CAAC;IACDC,QAAQ,EAAEA,CAACH,KAAK,EAAEC,MAA4B,KAAK;MACjDD,KAAK,CAACX,MAAM,CAACe,IAAI,CAACH,MAAM,CAACC,OAAO,CAAC;MACjCF,KAAK,CAACN,cAAc,GAAGM,KAAK,CAACX,MAAM,CAACgB,MAAM,CAACC,KAAK,IAC9CA,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CAAC,IACnEF,KAAK,CAACI,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CACtE,CAAC;IACH,CAAC;IACDG,WAAW,EAAEA,CAACX,KAAK,EAAEC,MAA4B,KAAK;MACpD,MAAMW,KAAK,GAAGZ,KAAK,CAACX,MAAM,CAACwB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAKd,MAAM,CAACC,OAAO,CAACa,QAAQ,CAAC;MACjF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAAA,IAAAI,mBAAA;QAChBhB,KAAK,CAACX,MAAM,CAACuB,KAAK,CAAC,GAAGX,MAAM,CAACC,OAAO;QACpC,IAAI,EAAAc,mBAAA,GAAAhB,KAAK,CAACV,YAAY,cAAA0B,mBAAA,uBAAlBA,mBAAA,CAAoBD,QAAQ,MAAKd,MAAM,CAACC,OAAO,CAACa,QAAQ,EAAE;UAC5Df,KAAK,CAACV,YAAY,GAAGW,MAAM,CAACC,OAAO;QACrC;MACF;MACAF,KAAK,CAACN,cAAc,GAAGM,KAAK,CAACX,MAAM,CAACgB,MAAM,CAACC,KAAK,IAC9CA,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CAAC,IACnEF,KAAK,CAACI,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CACtE,CAAC;IACH,CAAC;IACDS,WAAW,EAAEA,CAACjB,KAAK,EAAEC,MAA6B,KAAK;MAAA,IAAAiB,oBAAA;MACrDlB,KAAK,CAACX,MAAM,GAAGW,KAAK,CAACX,MAAM,CAACgB,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAKd,MAAM,CAACC,OAAO,CAAC;MACtE,IAAI,EAAAgB,oBAAA,GAAAlB,KAAK,CAACV,YAAY,cAAA4B,oBAAA,uBAAlBA,oBAAA,CAAoBH,QAAQ,MAAKd,MAAM,CAACC,OAAO,EAAE;QACnDF,KAAK,CAACV,YAAY,GAAG,IAAI;MAC3B;MACAU,KAAK,CAACN,cAAc,GAAGM,KAAK,CAACX,MAAM,CAACgB,MAAM,CAACC,KAAK,IAC9CA,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CAAC,IACnEF,KAAK,CAACI,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,KAAK,CAACP,WAAW,CAACe,WAAW,CAAC,CAAC,CACtE,CAAC;IACH,CAAC;IACDW,eAAe,EAAEA,CAACnB,KAAK,EAAEC,MAAmC,KAAK;MAC/DD,KAAK,CAACV,YAAY,GAAGW,MAAM,CAACC,OAAO;IACrC,CAAC;IACDkB,yBAAyB,EAAEA,CAACpB,KAAK,EAAEC,MAA6B,KAAK;MACnE,IAAID,KAAK,CAACV,YAAY,EAAE;QACtBU,KAAK,CAACV,YAAY,CAACoB,OAAO,GAAGT,MAAM,CAACC,OAAO;QAC3CF,KAAK,CAACV,YAAY,CAAC+B,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;QAExD;QACA,MAAMX,KAAK,GAAGZ,KAAK,CAACX,MAAM,CAACwB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAKf,KAAK,CAACV,YAAY,CAAEyB,QAAQ,CAAC;QACtF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBZ,KAAK,CAACX,MAAM,CAACuB,KAAK,CAAC,GAAG;YAAE,GAAGZ,KAAK,CAACV;UAAa,CAAC;QACjD;MACF;IACF,CAAC;IACDkC,UAAU,EAAEA,CAACxB,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACT,SAAS,GAAGU,MAAM,CAACC,OAAO;IAClC,CAAC;IACDuB,QAAQ,EAAEA,CAACzB,KAAK,EAAEC,MAAoC,KAAK;MACzDD,KAAK,CAACR,KAAK,GAAGS,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDwB,cAAc,EAAEA,CAAC1B,KAAK,EAAEC,MAA6B,KAAK;MACxDD,KAAK,CAACP,WAAW,GAAGQ,MAAM,CAACC,OAAO;MAClCF,KAAK,CAACN,cAAc,GAAGM,KAAK,CAACX,MAAM,CAACgB,MAAM,CAACC,KAAK,IAC9CA,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,MAAM,CAACC,OAAO,CAACM,WAAW,CAAC,CAAC,CAAC,IAChEF,KAAK,CAACI,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,MAAM,CAACC,OAAO,CAACM,WAAW,CAAC,CAAC,CACnE,CAAC;IACH,CAAC;IACDmB,iBAAiB,EAAEA,CAAC3B,KAAK,EAAEC,MAA8D,KAAK;MAC5FD,KAAK,CAACL,cAAc,GAAGM,MAAM,CAACC,OAAO;IACvC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXH,SAAS;EACTI,QAAQ;EACRQ,WAAW;EACXM,WAAW;EACXE,eAAe;EACfC,yBAAyB;EACzBI,UAAU;EACVC,QAAQ;EACRC;AACF,CAAC,GAAG9B,WAAW,CAACgC,OAAO;AAEvB,eAAehC,WAAW,CAACiC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}