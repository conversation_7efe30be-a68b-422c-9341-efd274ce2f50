[{"/home/<USER>/paper_ui/frontend/src/index.tsx": "1", "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/paper_ui/frontend/src/App.tsx": "3", "/home/<USER>/paper_ui/frontend/src/store/store.ts": "4", "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx": "5", "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts": "6", "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts": "7", "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts": "8", "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts": "9", "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts": "10", "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx": "11", "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx": "12", "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx": "13", "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx": "14", "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx": "15", "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx": "16", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx": "17", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx": "18", "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx": "19", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx": "20", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx": "21", "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx": "22", "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx": "23", "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx": "24", "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx": "25", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx": "26", "/home/<USER>/paper_ui/frontend/src/services/api.ts": "27", "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts": "28"}, {"size": 554, "mtime": 1752405180447, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1752405180447, "results": "31", "hashOfConfig": "30"}, {"size": 434, "mtime": 1752405292530, "results": "32", "hashOfConfig": "30"}, {"size": 718, "mtime": 1752405308392, "results": "33", "hashOfConfig": "30"}, {"size": 2661, "mtime": 1752416204333, "results": "34", "hashOfConfig": "30"}, {"size": 3154, "mtime": 1752421997115, "results": "35", "hashOfConfig": "30"}, {"size": 3944, "mtime": 1752419648877, "results": "36", "hashOfConfig": "30"}, {"size": 1369, "mtime": 1752405356976, "results": "37", "hashOfConfig": "30"}, {"size": 4813, "mtime": 1752429815155, "results": "38", "hashOfConfig": "30"}, {"size": 2222, "mtime": 1752429757672, "results": "39", "hashOfConfig": "30"}, {"size": 3419, "mtime": 1752406243807, "results": "40", "hashOfConfig": "30"}, {"size": 3083, "mtime": 1752408748081, "results": "41", "hashOfConfig": "30"}, {"size": 2951, "mtime": 1752405713327, "results": "42", "hashOfConfig": "30"}, {"size": 9353, "mtime": 1752477517284, "results": "43", "hashOfConfig": "30"}, {"size": 2212, "mtime": 1752405770902, "results": "44", "hashOfConfig": "30"}, {"size": 3173, "mtime": 1752406508223, "results": "45", "hashOfConfig": "30"}, {"size": 1915, "mtime": 1752405565605, "results": "46", "hashOfConfig": "30"}, {"size": 1905, "mtime": 1752405599228, "results": "47", "hashOfConfig": "30"}, {"size": 13074, "mtime": 1752477568960, "results": "48", "hashOfConfig": "30"}, {"size": 4514, "mtime": 1752408666581, "results": "49", "hashOfConfig": "30"}, {"size": 5460, "mtime": 1752409063637, "results": "50", "hashOfConfig": "30"}, {"size": 3960, "mtime": 1752405621797, "results": "51", "hashOfConfig": "30"}, {"size": 5579, "mtime": 1752406211967, "results": "52", "hashOfConfig": "30"}, {"size": 7201, "mtime": 1752477825945, "results": "53", "hashOfConfig": "30"}, {"size": 6456, "mtime": 1752405927990, "results": "54", "hashOfConfig": "30"}, {"size": 2702, "mtime": 1752406378198, "results": "55", "hashOfConfig": "30"}, {"size": 4485, "mtime": 1752420110760, "results": "56", "hashOfConfig": "30"}, {"size": 2824, "mtime": 1752409168121, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hpsgo6", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/paper_ui/frontend/src/index.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/paper_ui/frontend/src/App.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/store.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx", ["142", "143", "144", "145", "146"], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/services/api.ts", ["147"], [], "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 33, "column": 6, "nodeType": "150", "endLine": 33, "endColumn": 22, "suggestions": "151"}, {"ruleId": "148", "severity": 1, "message": "152", "line": 83, "column": 9, "nodeType": "153", "endLine": 125, "endColumn": 4, "suggestions": "154"}, {"ruleId": "148", "severity": 1, "message": "155", "line": 83, "column": 9, "nodeType": "153", "endLine": 125, "endColumn": 4, "suggestions": "156"}, {"ruleId": "148", "severity": 1, "message": "157", "line": 83, "column": 9, "nodeType": "153", "endLine": 125, "endColumn": 4, "suggestions": "158"}, {"ruleId": "148", "severity": 1, "message": "159", "line": 152, "column": 9, "nodeType": "153", "endLine": 160, "endColumn": 4, "suggestions": "160"}, {"ruleId": "161", "severity": 1, "message": "162", "line": 175, "column": 1, "nodeType": "163", "endLine": 180, "endColumn": 3}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'paper.content'. Either include it or remove the dependency array. If 'setContent' needs the current value of 'paper.content', you can also switch to useReducer instead of useState and read 'paper.content' in the reducer.", "ArrayExpression", ["164"], "The 'handleSave' function makes the dependencies of useEffect Hook (at line 184) change on every render. To fix this, wrap the definition of 'handleSave' in its own useCallback() Hook.", "VariableDeclarator", ["165"], "The 'handleSave' function makes the dependencies of useEffect Hook (at line 234) change on every render. To fix this, wrap the definition of 'handleSave' in its own useCallback() Hook.", ["166"], "The 'handleSave' function makes the dependencies of useEffect Hook (at line 251) change on every render. To fix this, wrap the definition of 'handleSave' in its own useCallback() Hook.", ["167"], "The 'toggleView' function makes the dependencies of useEffect Hook (at line 251) change on every render. To fix this, wrap the definition of 'toggleView' in its own useCallback() Hook.", ["168"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"desc": "171", "fix": "173"}, {"desc": "171", "fix": "174"}, {"desc": "175", "fix": "176"}, "Update the dependencies array to be: [paper.content, paper.paper_id]", {"range": "177", "text": "178"}, "Wrap the definition of 'handleSave' in its own useCallback() Hook.", {"range": "179", "text": "180"}, {"range": "181", "text": "180"}, {"range": "182", "text": "180"}, "Wrap the definition of 'toggleView' in its own useCallback() Hook.", {"range": "183", "text": "184"}, [1503, 1519], "[paper.content, paper.paper_id]", [3038, 4337], "useCallback(async () => {\n    console.log('handleSave called:', { hasUnsavedChanges, isSaving, contentLength: content.length });\n    if (!hasUnsavedChanges) {\n      console.log('No unsaved changes, skipping save');\n      return;\n    }\n\n    // Clear any pending auto-save\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    setIsSaving(true);\n    console.log('Starting save...');\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n      console.log('Save result:', result);\n\n      if (result.data) {\n        setLastSavedContent(content);\n        setHasUnsavedChanges(false);\n        // Update Redux store only on successful save\n        dispatch(updateCurrentPaperContent(content));\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  })", [3038, 4337], [3038, 4337], [5222, 5403], "useCallback(() => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  })"]