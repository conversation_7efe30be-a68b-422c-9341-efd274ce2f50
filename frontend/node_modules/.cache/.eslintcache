[{"/home/<USER>/paper_ui/frontend/src/index.tsx": "1", "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/paper_ui/frontend/src/App.tsx": "3", "/home/<USER>/paper_ui/frontend/src/store/store.ts": "4", "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx": "5", "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts": "6", "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts": "7", "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts": "8", "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts": "9", "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts": "10", "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx": "11", "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx": "12", "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx": "13", "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx": "14", "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx": "15", "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx": "16", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx": "17", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx": "18", "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx": "19", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx": "20", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx": "21", "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx": "22", "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx": "23", "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx": "24", "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx": "25", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx": "26", "/home/<USER>/paper_ui/frontend/src/services/api.ts": "27", "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts": "28"}, {"size": 554, "mtime": 1752405180447, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1752405180447, "results": "31", "hashOfConfig": "30"}, {"size": 434, "mtime": 1752405292530, "results": "32", "hashOfConfig": "30"}, {"size": 718, "mtime": 1752405308392, "results": "33", "hashOfConfig": "30"}, {"size": 2661, "mtime": 1752416204333, "results": "34", "hashOfConfig": "30"}, {"size": 3154, "mtime": 1752421997115, "results": "35", "hashOfConfig": "30"}, {"size": 3944, "mtime": 1752419648877, "results": "36", "hashOfConfig": "30"}, {"size": 1369, "mtime": 1752405356976, "results": "37", "hashOfConfig": "30"}, {"size": 4776, "mtime": 1752422046319, "results": "38", "hashOfConfig": "30"}, {"size": 2185, "mtime": 1752405397274, "results": "39", "hashOfConfig": "30"}, {"size": 3419, "mtime": 1752406243807, "results": "40", "hashOfConfig": "30"}, {"size": 3083, "mtime": 1752408748081, "results": "41", "hashOfConfig": "30"}, {"size": 2951, "mtime": 1752405713327, "results": "42", "hashOfConfig": "30"}, {"size": 8814, "mtime": 1752421892867, "results": "43", "hashOfConfig": "30"}, {"size": 2212, "mtime": 1752405770902, "results": "44", "hashOfConfig": "30"}, {"size": 3173, "mtime": 1752406508223, "results": "45", "hashOfConfig": "30"}, {"size": 1915, "mtime": 1752405565605, "results": "46", "hashOfConfig": "30"}, {"size": 1905, "mtime": 1752405599228, "results": "47", "hashOfConfig": "30"}, {"size": 11052, "mtime": 1752423424535, "results": "48", "hashOfConfig": "30"}, {"size": 4514, "mtime": 1752408666581, "results": "49", "hashOfConfig": "30"}, {"size": 5460, "mtime": 1752409063637, "results": "50", "hashOfConfig": "30"}, {"size": 3960, "mtime": 1752405621797, "results": "51", "hashOfConfig": "30"}, {"size": 5579, "mtime": 1752406211967, "results": "52", "hashOfConfig": "30"}, {"size": 6270, "mtime": 1752416844453, "results": "53", "hashOfConfig": "30"}, {"size": 6456, "mtime": 1752405927990, "results": "54", "hashOfConfig": "30"}, {"size": 2702, "mtime": 1752406378198, "results": "55", "hashOfConfig": "30"}, {"size": 4485, "mtime": 1752420110760, "results": "56", "hashOfConfig": "30"}, {"size": 2824, "mtime": 1752409168121, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hpsgo6", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/paper_ui/frontend/src/index.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/paper_ui/frontend/src/App.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/store.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx", ["142", "143", "144"], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/services/api.ts", ["145"], [], "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts", [], [], {"ruleId": "146", "severity": 1, "message": "147", "line": 106, "column": 9, "nodeType": "148", "endLine": 140, "endColumn": 4, "suggestions": "149"}, {"ruleId": "146", "severity": 1, "message": "150", "line": 106, "column": 9, "nodeType": "148", "endLine": 140, "endColumn": 4, "suggestions": "151"}, {"ruleId": "146", "severity": 1, "message": "152", "line": 164, "column": 9, "nodeType": "148", "endLine": 172, "endColumn": 4, "suggestions": "153"}, {"ruleId": "154", "severity": 1, "message": "155", "line": 175, "column": 1, "nodeType": "156", "endLine": 180, "endColumn": 3}, "react-hooks/exhaustive-deps", "The 'handleSave' function makes the dependencies of useEffect Hook (at line 187) change on every render. To fix this, wrap the definition of 'handleSave' in its own useCallback() Hook.", "VariableDeclarator", ["157"], "The 'handleSave' function makes the dependencies of useEffect Hook (at line 204) change on every render. To fix this, wrap the definition of 'handleSave' in its own useCallback() Hook.", ["158"], "The 'toggleView' function makes the dependencies of useEffect Hook (at line 204) change on every render. To fix this, wrap the definition of 'toggleView' in its own useCallback() Hook.", ["159"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "160", "fix": "161"}, {"desc": "160", "fix": "162"}, {"desc": "163", "fix": "164"}, "Wrap the definition of 'handleSave' in its own useCallback() Hook.", {"range": "165", "text": "166"}, {"range": "167", "text": "166"}, "Wrap the definition of 'toggleView' in its own useCallback() Hook.", {"range": "168", "text": "169"}, [3442, 4380], "useCallback(async () => {\n    if (!hasUnsavedChanges) return;\n\n    // Clear any pending auto-save\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    setIsSaving(true);\n    try {\n      const result = await api.papers.update(paper.paper_id, paper.title, content);\n\n      if (result.data) {\n        setLastSavedContent(content);\n        setHasUnsavedChanges(false);\n        dispatch(addNotification({\n          type: 'success',\n          title: 'Paper Saved',\n          message: 'Your changes have been saved successfully.',\n          duration: 3000,\n        }));\n      } else {\n        throw new Error(result.error || 'Failed to save');\n      }\n    } catch (error) {\n      dispatch(addNotification({\n        type: 'error',\n        title: 'Save Failed',\n        message: 'Failed to save your changes. Please try again.',\n        duration: 5000,\n      }));\n    } finally {\n      setIsSaving(false);\n    }\n  })", [3442, 4380], [5172, 5353], "useCallback(() => {\n    if (isSplitView) {\n      setIsPreviewMode(!isPreviewMode);\n      setIsSplitView(false);\n    } else {\n      setIsSplitView(true);\n      setIsPreviewMode(false);\n    }\n  })"]