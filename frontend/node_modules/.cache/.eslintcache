[{"/home/<USER>/paper_ui/frontend/src/index.tsx": "1", "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/paper_ui/frontend/src/App.tsx": "3", "/home/<USER>/paper_ui/frontend/src/store/store.ts": "4", "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx": "5", "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts": "6", "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts": "7", "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts": "8", "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts": "9", "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts": "10", "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx": "11", "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx": "12", "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx": "13", "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx": "14", "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx": "15", "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx": "16", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx": "17", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx": "18", "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx": "19", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx": "20", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx": "21", "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx": "22", "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx": "23", "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx": "24", "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx": "25", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx": "26", "/home/<USER>/paper_ui/frontend/src/services/api.ts": "27"}, {"size": 554, "mtime": 1752405180447, "results": "28", "hashOfConfig": "29"}, {"size": 425, "mtime": 1752405180447, "results": "30", "hashOfConfig": "29"}, {"size": 434, "mtime": 1752405292530, "results": "31", "hashOfConfig": "29"}, {"size": 718, "mtime": 1752405308392, "results": "32", "hashOfConfig": "29"}, {"size": 2378, "mtime": 1752408548679, "results": "33", "hashOfConfig": "29"}, {"size": 2161, "mtime": 1752405323542, "results": "34", "hashOfConfig": "29"}, {"size": 3696, "mtime": 1752405342967, "results": "35", "hashOfConfig": "29"}, {"size": 1369, "mtime": 1752405356976, "results": "36", "hashOfConfig": "29"}, {"size": 4107, "mtime": 1752405378329, "results": "37", "hashOfConfig": "29"}, {"size": 2185, "mtime": 1752405397274, "results": "38", "hashOfConfig": "29"}, {"size": 3419, "mtime": 1752406243807, "results": "39", "hashOfConfig": "29"}, {"size": 3083, "mtime": 1752408748081, "results": "40", "hashOfConfig": "29"}, {"size": 2951, "mtime": 1752405713327, "results": "41", "hashOfConfig": "29"}, {"size": 5422, "mtime": 1752408632274, "results": "42", "hashOfConfig": "29"}, {"size": 2212, "mtime": 1752405770902, "results": "43", "hashOfConfig": "29"}, {"size": 3173, "mtime": 1752406508223, "results": "44", "hashOfConfig": "29"}, {"size": 1915, "mtime": 1752405565605, "results": "45", "hashOfConfig": "29"}, {"size": 1905, "mtime": 1752405599228, "results": "46", "hashOfConfig": "29"}, {"size": 5455, "mtime": 1752408824325, "results": "47", "hashOfConfig": "29"}, {"size": 4514, "mtime": 1752408666581, "results": "48", "hashOfConfig": "29"}, {"size": 5123, "mtime": 1752406615790, "results": "49", "hashOfConfig": "29"}, {"size": 3960, "mtime": 1752405621797, "results": "50", "hashOfConfig": "29"}, {"size": 5579, "mtime": 1752406211967, "results": "51", "hashOfConfig": "29"}, {"size": 5681, "mtime": 1752408602359, "results": "52", "hashOfConfig": "29"}, {"size": 6456, "mtime": 1752405927990, "results": "53", "hashOfConfig": "29"}, {"size": 2702, "mtime": 1752406378198, "results": "54", "hashOfConfig": "29"}, {"size": 4472, "mtime": 1752408518903, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hpsgo6", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/paper_ui/frontend/src/index.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/paper_ui/frontend/src/App.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/store.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx", ["137", "138"], [], "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx", ["139", "140", "141", "142", "143"], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/services/api.ts", ["144"], [], {"ruleId": "145", "severity": 1, "message": "146", "line": 1, "column": 27, "nodeType": "147", "messageId": "148", "endLine": 1, "endColumn": 33}, {"ruleId": "145", "severity": 1, "message": "149", "line": 1, "column": 35, "nodeType": "147", "messageId": "148", "endLine": 1, "endColumn": 44}, {"ruleId": "145", "severity": 1, "message": "150", "line": 1, "column": 38, "nodeType": "147", "messageId": "148", "endLine": 1, "endColumn": 49}, {"ruleId": "145", "severity": 1, "message": "151", "line": 5, "column": 39, "nodeType": "147", "messageId": "148", "endLine": 5, "endColumn": 44}, {"ruleId": "145", "severity": 1, "message": "152", "line": 5, "column": 46, "nodeType": "147", "messageId": "148", "endLine": 5, "endColumn": 55}, {"ruleId": "145", "severity": 1, "message": "153", "line": 8, "column": 8, "nodeType": "147", "messageId": "148", "endLine": 8, "endColumn": 14}, {"ruleId": "145", "severity": 1, "message": "154", "line": 9, "column": 8, "nodeType": "147", "messageId": "148", "endLine": 9, "endColumn": 11}, {"ruleId": "155", "severity": 1, "message": "156", "line": 174, "column": 1, "nodeType": "157", "endLine": 179, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'useRef' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'useCallback' is defined but never used.", "'Split' is defined but never used.", "'Maximize2' is defined but never used.", "'Editor' is defined but never used.", "'api' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration"]