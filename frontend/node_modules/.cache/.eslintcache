[{"/home/<USER>/paper_ui/frontend/src/index.tsx": "1", "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/paper_ui/frontend/src/App.tsx": "3", "/home/<USER>/paper_ui/frontend/src/store/store.ts": "4", "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx": "5", "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts": "6", "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts": "7", "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts": "8", "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts": "9", "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts": "10", "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx": "11", "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx": "12", "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx": "13", "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx": "14", "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx": "15", "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx": "16", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx": "17", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx": "18", "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx": "19", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx": "20", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx": "21", "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx": "22", "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx": "23", "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx": "24", "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx": "25", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx": "26", "/home/<USER>/paper_ui/frontend/src/services/api.ts": "27", "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts": "28"}, {"size": 554, "mtime": 1752405180447, "results": "29", "hashOfConfig": "30"}, {"size": 425, "mtime": 1752405180447, "results": "31", "hashOfConfig": "30"}, {"size": 434, "mtime": 1752405292530, "results": "32", "hashOfConfig": "30"}, {"size": 718, "mtime": 1752405308392, "results": "33", "hashOfConfig": "30"}, {"size": 2490, "mtime": 1752409201968, "results": "34", "hashOfConfig": "30"}, {"size": 2161, "mtime": 1752405323542, "results": "35", "hashOfConfig": "30"}, {"size": 3696, "mtime": 1752405342967, "results": "36", "hashOfConfig": "30"}, {"size": 1369, "mtime": 1752405356976, "results": "37", "hashOfConfig": "30"}, {"size": 4107, "mtime": 1752405378329, "results": "38", "hashOfConfig": "30"}, {"size": 2185, "mtime": 1752405397274, "results": "39", "hashOfConfig": "30"}, {"size": 3419, "mtime": 1752406243807, "results": "40", "hashOfConfig": "30"}, {"size": 3083, "mtime": 1752408748081, "results": "41", "hashOfConfig": "30"}, {"size": 2951, "mtime": 1752405713327, "results": "42", "hashOfConfig": "30"}, {"size": 5422, "mtime": 1752408632274, "results": "43", "hashOfConfig": "30"}, {"size": 2212, "mtime": 1752405770902, "results": "44", "hashOfConfig": "30"}, {"size": 3173, "mtime": 1752406508223, "results": "45", "hashOfConfig": "30"}, {"size": 1915, "mtime": 1752405565605, "results": "46", "hashOfConfig": "30"}, {"size": 1905, "mtime": 1752405599228, "results": "47", "hashOfConfig": "30"}, {"size": 8438, "mtime": 1752408938056, "results": "48", "hashOfConfig": "30"}, {"size": 4514, "mtime": 1752408666581, "results": "49", "hashOfConfig": "30"}, {"size": 5460, "mtime": 1752409063637, "results": "50", "hashOfConfig": "30"}, {"size": 3960, "mtime": 1752405621797, "results": "51", "hashOfConfig": "30"}, {"size": 5579, "mtime": 1752406211967, "results": "52", "hashOfConfig": "30"}, {"size": 5707, "mtime": 1752409125408, "results": "53", "hashOfConfig": "30"}, {"size": 6456, "mtime": 1752405927990, "results": "54", "hashOfConfig": "30"}, {"size": 2702, "mtime": 1752406378198, "results": "55", "hashOfConfig": "30"}, {"size": 4472, "mtime": 1752408518903, "results": "56", "hashOfConfig": "30"}, {"size": 2824, "mtime": 1752409168121, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hpsgo6", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/paper_ui/frontend/src/index.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/paper_ui/frontend/src/App.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/store.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx", ["142", "143"], [], "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx", ["144", "145", "146"], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/services/api.ts", ["147"], [], "/home/<USER>/paper_ui/frontend/src/hooks/useRealTimeSync.ts", [], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 1, "column": 27, "nodeType": "150", "messageId": "151", "endLine": 1, "endColumn": 33}, {"ruleId": "148", "severity": 1, "message": "152", "line": 1, "column": 35, "nodeType": "150", "messageId": "151", "endLine": 1, "endColumn": 44}, {"ruleId": "148", "severity": 1, "message": "153", "line": 40, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 40, "endColumn": 29}, {"ruleId": "154", "severity": 1, "message": "155", "line": 117, "column": 6, "nodeType": "156", "endLine": 117, "endColumn": 34, "suggestions": "157"}, {"ruleId": "154", "severity": 1, "message": "158", "line": 134, "column": 6, "nodeType": "156", "endLine": 134, "endColumn": 8, "suggestions": "159"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 174, "column": 1, "nodeType": "162", "endLine": 179, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'useRef' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'handleTextareaChange' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleSave'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], "React Hook useEffect has missing dependencies: 'handleSave' and 'toggleView'. Either include them or remove the dependency array.", ["164"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, "Update the dependencies array to be: [content, handleSave, hasUnsavedChanges]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [handleSave, toggleView]", {"range": "171", "text": "172"}, [3595, 3623], "[content, handleSave, hasUnsavedChanges]", [4090, 4092], "[handleSave, toggleView]"]