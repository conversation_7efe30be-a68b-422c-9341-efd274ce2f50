[{"/home/<USER>/paper_ui/frontend/src/index.tsx": "1", "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/paper_ui/frontend/src/App.tsx": "3", "/home/<USER>/paper_ui/frontend/src/store/store.ts": "4", "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx": "5", "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts": "6", "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts": "7", "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts": "8", "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts": "9", "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts": "10", "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx": "11", "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx": "12", "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx": "13", "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx": "14", "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx": "15", "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx": "16", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx": "17", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx": "18", "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx": "19", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx": "20", "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx": "21", "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx": "22", "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx": "23", "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx": "24", "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx": "25", "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx": "26"}, {"size": 554, "mtime": 1752405180447, "results": "27", "hashOfConfig": "28"}, {"size": 425, "mtime": 1752405180447, "results": "29", "hashOfConfig": "28"}, {"size": 434, "mtime": 1752405292530, "results": "30", "hashOfConfig": "28"}, {"size": 718, "mtime": 1752405308392, "results": "31", "hashOfConfig": "28"}, {"size": 2146, "mtime": 1752405413032, "results": "32", "hashOfConfig": "28"}, {"size": 2161, "mtime": 1752405323542, "results": "33", "hashOfConfig": "28"}, {"size": 3696, "mtime": 1752405342967, "results": "34", "hashOfConfig": "28"}, {"size": 1369, "mtime": 1752405356976, "results": "35", "hashOfConfig": "28"}, {"size": 4107, "mtime": 1752405378329, "results": "36", "hashOfConfig": "28"}, {"size": 2185, "mtime": 1752405397274, "results": "37", "hashOfConfig": "28"}, {"size": 3468, "mtime": 1752405453530, "results": "38", "hashOfConfig": "28"}, {"size": 2928, "mtime": 1752405640782, "results": "39", "hashOfConfig": "28"}, {"size": 2951, "mtime": 1752405713327, "results": "40", "hashOfConfig": "28"}, {"size": 5952, "mtime": 1752405515541, "results": "41", "hashOfConfig": "28"}, {"size": 2212, "mtime": 1752405770902, "results": "42", "hashOfConfig": "28"}, {"size": 3738, "mtime": 1752405695153, "results": "43", "hashOfConfig": "28"}, {"size": 1915, "mtime": 1752405565605, "results": "44", "hashOfConfig": "28"}, {"size": 1905, "mtime": 1752405599228, "results": "45", "hashOfConfig": "28"}, {"size": 5268, "mtime": 1752405954626, "results": "46", "hashOfConfig": "28"}, {"size": 3528, "mtime": 1752406171254, "results": "47", "hashOfConfig": "28"}, {"size": 5095, "mtime": 1752406198286, "results": "48", "hashOfConfig": "28"}, {"size": 3960, "mtime": 1752405621797, "results": "49", "hashOfConfig": "28"}, {"size": 5579, "mtime": 1752406211967, "results": "50", "hashOfConfig": "28"}, {"size": 5704, "mtime": 1752405880078, "results": "51", "hashOfConfig": "28"}, {"size": 6456, "mtime": 1752405927990, "results": "52", "hashOfConfig": "28"}, {"size": 2674, "mtime": 1752405582930, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hpsgo6", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/paper_ui/frontend/src/index.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/paper_ui/frontend/src/App.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/store/store.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Layout.tsx", ["132"], [], "/home/<USER>/paper_ui/frontend/src/store/slices/chatSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/papersSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/sessionSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/toolsSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/store/slices/uiSlice.ts", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/Header.tsx", ["133"], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/MainWorkArea.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/ContextPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ChatPanel.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/UI/NotificationContainer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Layout/WelcomeScreen.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageInput.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Editor/PaperEditor.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartGallery.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Charts/ChartViewer.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/ToolStatusList.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Search/SearchResults.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Papers/PaperList.tsx", ["134", "135"], [], "/home/<USER>/paper_ui/frontend/src/components/Session/SessionInfo.tsx", [], [], "/home/<USER>/paper_ui/frontend/src/components/Chat/MessageBubble.tsx", [], [], {"ruleId": "136", "severity": 1, "message": "137", "line": 22, "column": 6, "nodeType": "138", "endLine": 22, "endColumn": 22, "suggestions": "139"}, {"ruleId": "140", "severity": 1, "message": "141", "line": 7, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 7, "endColumn": 16}, {"ruleId": "136", "severity": 1, "message": "144", "line": 16, "column": 6, "nodeType": "138", "endLine": 16, "endColumn": 8, "suggestions": "145"}, {"ruleId": "140", "severity": 1, "message": "146", "line": 60, "column": 15, "nodeType": "142", "messageId": "143", "endLine": 60, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeSession'. Either include it or remove the dependency array.", "ArrayExpression", ["147"], "@typescript-eslint/no-unused-vars", "'MessageSquare' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'loadPapers'. Either include it or remove the dependency array.", ["148"], "'data' is assigned a value but never used.", {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, "Update the dependencies array to be: [currentSession, initializeSession]", {"range": "153", "text": "154"}, "Update the dependencies array to be: [loadPapers]", {"range": "155", "text": "156"}, [830, 846], "[currentSession, initializeSession]", [715, 717], "[loadPapers]"]