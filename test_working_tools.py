"""Test the working tools and demonstrate functionality."""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_firecrawl_search():
    """Test FireCrawl search - this is working!"""
    print("🔍 Testing FireCrawl Search (Working Tool)...")
    
    try:
        from app.tools.research import firecrawl_search_tool
        
        # Test different search queries
        test_queries = [
            "machine learning research papers 2024",
            "climate change scientific studies",
            "quantum computing breakthroughs"
        ]
        
        for query in test_queries:
            print(f"\n   Searching: '{query}'")
            result = firecrawl_search_tool._run(query=query, limit=2)
            
            if result.get("success"):
                print(f"   ✅ Found {result.get('count', 0)} results")
                if result.get('results') and result['results'].get('data'):
                    for i, item in enumerate(result['results']['data'][:2]):
                        print(f"      {i+1}. {item.get('title', 'No title')[:50]}...")
                        print(f"         URL: {item.get('url', 'No URL')}")
            else:
                print(f"   ❌ Search failed: {result.get('error')}")
                
    except Exception as e:
        print(f"❌ FireCrawl Search: EXCEPTION - {e}")


def test_chart_generation():
    """Test all chart generation tools - these are working!"""
    print("\n📈 Testing Chart Generation (Working Tools)...")
    
    try:
        from app.tools.visualization import (
            line_chart_tool, bar_chart_tool, pie_chart_tool, 
            polar_chart_tool, doughnut_chart_tool
        )
        
        # Research paper progress data
        research_data = {
            "title": "Research Paper Progress",
            "labels": ["Literature Review", "Data Collection", "Analysis", "Writing", "Review"],
            "data": [100, 85, 60, 30, 10]
        }
        
        # Publication types data
        publication_data = {
            "title": "Publication Types Distribution",
            "labels": ["Journal Articles", "Conference Papers", "Book Chapters", "Reports"],
            "data": [45, 30, 15, 10]
        }
        
        charts_to_test = [
            ("Research Progress (Line)", line_chart_tool, research_data),
            ("Research Progress (Bar)", bar_chart_tool, research_data),
            ("Publication Types (Pie)", pie_chart_tool, publication_data),
            ("Publication Types (Polar)", polar_chart_tool, publication_data),
            ("Publication Types (Doughnut)", doughnut_chart_tool, publication_data)
        ]
        
        print("\n   Generated Charts:")
        for chart_name, chart_tool, data in charts_to_test:
            result = chart_tool._run(**data)
            
            if result.get("success"):
                print(f"   ✅ {chart_name}")
                print(f"      URL: {result.get('chart_url')}")
                print(f"      Data points: {result.get('data_points')}")
            else:
                print(f"   ❌ {chart_name}: {result.get('error')}")
                
    except Exception as e:
        print(f"❌ Chart Generation: EXCEPTION - {e}")


def test_paper_service():
    """Test paper service without Redis dependency."""
    print("\n📄 Testing Paper Service (No Redis)...")
    
    try:
        from app.services.paper_service import PaperRenderer
        
        renderer = PaperRenderer()
        
        # Test markdown content
        test_paper = """# Research Paper: AI in Healthcare

## Abstract
This paper explores the applications of artificial intelligence in healthcare settings.

## Introduction
Healthcare is being transformed by AI technologies including:
- Machine learning for diagnosis
- Natural language processing for medical records
- Computer vision for medical imaging

## Methodology
We conducted a systematic review of 150 papers published between 2020-2024.

### Data Collection
```python
def collect_papers(query, years):
    papers = search_database(query, years)
    return filter_relevant(papers)
```

## Results
| Technology | Accuracy | Papers |
|------------|----------|--------|
| ML Diagnosis | 94% | 45 |
| NLP Records | 89% | 32 |
| CV Imaging | 96% | 28 |

## Conclusion
AI shows significant promise in healthcare applications.

## References
- [@smith2023] - AI in Medical Diagnosis
- (Johnson et al., 2024) - Healthcare ML Applications
"""
        
        # Test rendering
        print("   Testing markdown rendering...")
        rendered = renderer.render_markdown(test_paper)
        if rendered["success"]:
            print("   ✅ Markdown rendering successful")
            print(f"      Word count: {rendered['word_count']}")
            print(f"      Character count: {rendered['char_count']}")
            print(f"      HTML length: {len(rendered['html'])} chars")
        
        # Test citation extraction
        print("   Testing citation extraction...")
        citations = renderer.extract_citations(test_paper)
        print(f"   ✅ Found {len(citations)} citations:")
        for citation in citations:
            print(f"      - {citation['text']}")
        
        # Test outline generation
        print("   Testing outline generation...")
        outline = renderer.generate_paper_outline(test_paper)
        print(f"   ✅ Generated outline with {outline['section_count']} main sections:")
        for section in outline['outline']:
            print(f"      {section['level']}. {section['title']}")
            for subsection in section.get('subsections', []):
                print(f"         {subsection['level']}.{subsection['level']}. {subsection['title']}")
                
    except Exception as e:
        print(f"❌ Paper Service: EXCEPTION - {e}")


def demonstrate_integration():
    """Demonstrate how tools would work together."""
    print("\n🔗 Demonstrating Tool Integration...")
    
    print("   Scenario: Research paper on 'AI in Education'")
    print("   1. 🔍 Search for relevant papers with FireCrawl")
    print("   2. 📊 Create charts to visualize findings")
    print("   3. 📄 Generate paper with markdown rendering")
    
    try:
        from app.tools.research import firecrawl_search_tool
        from app.tools.visualization import bar_chart_tool
        from app.services.paper_service import PaperRenderer
        
        # Step 1: Search
        print("\n   Step 1: Searching for AI education papers...")
        search_result = firecrawl_search_tool._run(
            query="artificial intelligence education learning",
            limit=3
        )
        
        if search_result.get("success"):
            print(f"   ✅ Found {search_result.get('count', 0)} relevant papers")
        
        # Step 2: Create visualization
        print("\n   Step 2: Creating research methodology chart...")
        chart_result = bar_chart_tool._run(
            title="AI Education Research Methods",
            labels=["Surveys", "Experiments", "Case Studies", "Reviews"],
            data=[25, 35, 20, 20]
        )
        
        if chart_result.get("success"):
            print("   ✅ Generated methodology chart")
            print(f"      Chart URL: {chart_result.get('chart_url')}")
        
        # Step 3: Render paper content
        print("\n   Step 3: Rendering paper content...")
        renderer = PaperRenderer()
        paper_content = f"""# AI in Education: A Comprehensive Review

## Research Findings
Based on our search, we found {search_result.get('count', 0)} relevant papers.

## Methodology Distribution
![Research Methods]({chart_result.get('chart_url', '')})

## Conclusion
This demonstrates the integration of search, visualization, and rendering tools.
"""
        
        rendered = renderer.render_markdown(paper_content)
        if rendered.get("success"):
            print("   ✅ Paper rendered successfully")
            print(f"      Final word count: {rendered['word_count']}")
        
        print("\n   🎉 Integration demonstration complete!")
        
    except Exception as e:
        print(f"❌ Integration Demo: EXCEPTION - {e}")


def main():
    """Run working tool tests."""
    print("🚀 Testing Working Paper Agent Tools")
    print("=" * 60)
    
    # Check key environment variables
    if not os.getenv("FIRECRAWL_API_KEY"):
        print("❌ FIRECRAWL_API_KEY not found in environment")
        return
    
    print("✅ FireCrawl API key found")
    print()
    
    # Test working components
    test_firecrawl_search()
    test_chart_generation()
    test_paper_service()
    demonstrate_integration()
    
    print("\n" + "=" * 60)
    print("🎯 Working Tools Summary:")
    print("✅ FireCrawl Search - Web search functionality")
    print("✅ Chart Generation - All 5 chart types working")
    print("✅ Paper Rendering - Markdown to HTML conversion")
    print("✅ Citation Extraction - Reference parsing")
    print("✅ Outline Generation - Document structure analysis")
    
    print("\n🔧 To Fix:")
    print("⚠️  Deep Research API - Check endpoint/authentication")
    print("⚠️  Airtable - Verify base ID and permissions")
    print("⚠️  Redis - Start Redis server for session management")
    
    print("\n🚀 Ready to Use:")
    print("The core functionality is working! You can:")
    print("1. Search for research papers")
    print("2. Generate visualizations")
    print("3. Render markdown papers")
    print("4. Start building your React frontend")


if __name__ == "__main__":
    main()
