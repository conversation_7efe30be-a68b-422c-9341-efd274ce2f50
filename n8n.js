{"nodes": [{"parameters": {"model": "x-ai/grok-3", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-620, 520], "id": "e5ea0ca9-7bf2-4181-9731-810b60caa25e", "name": "OpenRouter Chat Model1", "credentials": {"openRouterApi": {"id": "A2TDodAx50lDBpQw", "name": "OpenRouter account"}}}, {"parameters": {"toolDescription": "This is a firecrawl API endpoint with a example of curl of:\n\ncurl -k -X POST \"https://web.w-post.com/v1/search\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Authorization: Bearer 12345\" \\\n  -d '{\"query\": \"artificial intelligence\", \"limit\": 5}'\n\nlimit parameter has to be in format of number not string!!!!", "method": "POST", "url": "https://web.w-post.com/v1/search", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters0_Value', ``, 'string') }}"}, {"name": "limit", "value": "={{ $fromAI('parameters1_Value', `it has to be number not string!!!`, number) }}"}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-400, 640], "id": "bd865d92-dd60-4518-b896-e21f07be047d", "name": "firecrawlAPI", "credentials": {"httpBearerAuth": {"id": "XN2GCfLrFFitXib2", "name": "Bearer Auth account"}}}, {"parameters": {"toolDescription": "This is to get the status of a deep research task. The method is GET and format is https://router.w-post.com/api/research/ + taskID which you will obtain after calling deepResearchAPI", "url": "={{ $fromAI('URL', ``, 'string') }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-280, 700], "id": "514f6933-b832-4a1a-9835-414c82edae51", "name": "deepResearchStatusAPI", "credentials": {"httpHeaderAuth": {"id": "9a8sWIhxsgOmY0cN", "name": "Header Auth account"}}}, {"parameters": {"toolDescription": "This is the tool for make deep research of a topic in curl format example of:\ncurl -X POST https://router.w-post.com/api/research \\\n   -H \"Content-Type: application/json\" \\\n   -H \"X-API-Key: 12345\" \\\n   -d '{\n     \"query\": \"molten carbonate fuel cell\",\n     \"depth\": 1,\n     \"breadth\": 1,\n     \"outputType\": \"report\",\n     \"priority\": \"normal\"\n   }'", "method": "POST", "url": "=https://router.w-post.com/api/research", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters0_Name', ``, 'string') }}", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters0_Value', ``, 'string') }}"}, {"name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters1_Name', ``, 'string') }}", "value": "={{ $fromAI('parameters1_Value', ``, number) }}"}, {"name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters2_Name', ``, 'string') }}", "value": "={{ $fromAI('parameters2_Value', ``, number) }}"}, {"name": "outputType", "value": "report"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-180, 760], "id": "50e55165-918a-4ddc-8733-10bb89501afc", "name": "deepResearchAPI", "credentials": {"httpHeaderAuth": {"id": "gA9jhCV425PTXKqz", "name": "deep_research"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "apppQOKoeMRhPi2DX", "mode": "list", "cachedResultName": "papers", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX"}, "table": {"__rl": true, "value": "tblCtJwTdUyKLlnB9", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX/tblCtJwTdUyKLlnB9"}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [-60, 800], "id": "1a876879-4c40-4801-80aa-f339b91414a5", "name": "Airtable_overview", "credentials": {"airtableTokenApi": {"id": "GWO7fcu7PxgEiJ4a", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "deleteRecord", "base": {"__rl": true, "value": "apppQOKoeMRhPi2DX", "mode": "list", "cachedResultName": "papers", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX"}, "table": {"__rl": true, "value": "tblCtJwTdUyKLlnB9", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX/tblCtJwTdUyKLlnB9"}, "id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Record_ID', ``, 'string') }}"}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [80, 820], "id": "97189a1f-f3e9-42da-9cd8-007fc116d8ba", "name": "Airtable_delete", "credentials": {"airtableTokenApi": {"id": "GWO7fcu7PxgEiJ4a", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Create or update a record in Airtable", "operation": "upsert", "base": {"__rl": true, "value": "apppQOKoeMRhPi2DX", "mode": "list", "cachedResultName": "papers", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX"}, "table": {"__rl": true, "value": "tblCtJwTdUyKLlnB9", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/apppQOKoeMRhPi2DX/tblCtJwTdUyKLlnB9"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('id__using_to_match_', ``, 'string') }}", "paper_id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('paper_id', ``, 'string') }}", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', ``, 'string') }}"}, "matchingColumns": ["id", "paper_id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "paper_id", "displayName": "paper_id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "text", "displayName": "text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Attachments", "displayName": "Attachments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [200, 740], "id": "e5f807ed-42cb-48be-82b2-02bd16c35249", "name": "Airtable_create_update", "credentials": {"airtableTokenApi": {"id": "GWO7fcu7PxgEiJ4a", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"chartType": "line", "labelsMode": "array", "labelsArray": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Labels_Array', ``, 'string') }}", "data": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Data', ``, 'json') }}", "output": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Put_Output_In_Field', ``, 'string') }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quickChartTool", "typeVersion": 1, "position": [320, 700], "id": "24c0ab18-f326-44cc-8606-86defa78957b", "name": "QuickChart_line"}, {"parameters": {"labelsMode": "array", "labelsArray": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Labels_Array', ``, 'string') }}", "data": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Data', ``, 'json') }}", "output": "={{ $fromAI('Put_Output_In_Field', ``, 'string') }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quickChartTool", "typeVersion": 1, "position": [440, 660], "id": "3ca1f173-c4c3-482c-9b9c-fcf0617a8778", "name": "QuickChart_bar"}, {"parameters": {"chartType": "pie", "labelsMode": "array", "labelsArray": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Labels_Array', ``, 'string') }}", "data": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Data', ``, 'json') }}", "output": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Put_Output_In_Field', ``, 'string') }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quickChartTool", "typeVersion": 1, "position": [540, 600], "id": "69c06029-0e56-4568-91a3-354445d9b3b7", "name": "QuickChart_pie"}, {"parameters": {"chartType": "polarArea", "labelsMode": "array", "labelsArray": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Labels_Array', ``, 'string') }}", "data": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Data', ``, 'json') }}", "output": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Put_Output_In_Field', ``, 'string') }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quickChartTool", "typeVersion": 1, "position": [640, 560], "id": "4b0dd850-c20e-4a61-ae48-4ea627bc6536", "name": "QuickChart_polar"}, {"parameters": {"chartType": "doughnut", "labelsMode": "array", "labelsArray": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Labels_Array', ``, 'string') }}", "data": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Data', ``, 'json') }}", "output": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Put_Output_In_Field', ``, 'string') }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quickChartTool", "typeVersion": 1, "position": [740, 460], "id": "9cb880b2-2fc0-46e1-b8a1-2bb76c8a28ce", "name": "QuickChart_doughnut"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-800, 500], "id": "b613d3f9-3ad3-435d-b310-44e7d3a8881f", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "WgBzYpgT5w1fkv2F", "name": "OpenAi account"}}}, {"parameters": {"httpMethod": "POST", "path": "6ff1fd20-8dc9-4528-aa58-4b511f73b2d3", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-400, 300], "id": "a7cac71f-83c4-42fb-ab9a-e8b4536881ee", "name": "Webhook", "webhookId": "6ff1fd20-8dc9-4528-aa58-4b511f73b2d3"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [260, 300], "id": "ce9ff484-8377-4d13-bad4-6b57f2b4ed9d", "name": "Respond to Webhook"}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-500, 560], "id": "2fcf2458-2a5a-4d46-b9a5-798457576b11", "name": "Simple Memory"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}\n", "options": {"systemMessage": "You are \"<PERSON><PERSON><PERSON><PERSON>,\" an intelligent assistant embedded in an n8n workflow, helping authors collaboratively plan, draft, and refine scientific papers (Markdown .md).\n\nCore Mission\n\nGuide users step-by-step through each section:\n\nTitle\n\nAbstract\n\nIntroduction / Literature Review\n\nMethods\n\nResults\n\nDiscussion\n\nReferences\n\nProvide clear, concise advice on structure, clarity, scientific style, and citation standards.\n\nDialogue Style\n\nConcise yet thorough explanations.\n\nFormal academic tone for drafting content; conversational and supportive when interacting.\n\nUse bullet points or checklists for clarity.\n\nAvailable Tools & Usage Conditions\n\n1. Airtable_overview (Directly accessible)\n\nPurpose: Retrieve an overview of existing papers.\n\nWhen to Use: When the user references a specific paper but does not provide an ID.\n\n2. deepResearchAPI (Expensive, directly accessible)\n\nPurpose: Conduct comprehensive literature searches.\n\nParameters:\n\nQuery (confirmed by user)\n\nDepth (default=2, range 1-4)\n\nBreadth (default=2, range 1-4)\n\nProcedure:\n\nConfirm topic/scope with user explicitly.\n\nSubmit task; store returned taskId.\n\nPoll deepResearchStatusAPI until status=\"complete\", retrieve full report.\n\n3. firecrawlAPI (Cheap, directly accessible)\n\nPurpose: Quick search for recent papers, facts, or grey literature.\n\nUsage: Rapid, targeted queries; always cite retrieved URLs.\n\n4. Airtable CRUD Operations (via Airtable_create_update, Airtable_get, Airtable_delete) (Directly accessible)\n\nPurpose: Create, read, update, or delete manuscripts.\n\nSchema:\n\npaper_id (string, primary key, when creating, return from the tool calling, so you have to update this for a second call)\n\ntext (markdown)\n\nAttachments (figures/files)\n\nInstructions:\n\nCreate: When no id provided by user, initialize new paper, a id will return when creating successful.\n\nRead: Fetch existing content for updates or reference.\n\nUpdate: Clearly state append, combine, or replace; specify section.\n\nDelete: Permanent removal; confirm explicitly with user.\n\n5. QuickChart Visualization (Directly accessible, chart types available)\n\nChart Types:\n\nBar Chart: Comparative categories, rankings, discrete metrics\n\nLine Chart: Time series, trends, continuous data\n\nPie Chart: Simple proportions (max 6 categories)\n\nDoughnut Chart: Multiple series, cleaner than pie\n\nPolar Chart: Cyclical or multi-dimensional data comparisons\n\nChart Creation Procedure:\n\nAnalyze incoming data, automatically select optimal chart type.\n\nImmediately generate visual chart.\n\nThe label has to be like\nlabels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'] \n\noutput can be URL which can be embedded in the .md file.\n\nAlways include descriptive titles, clear labels, professional colors, readable formatting.\n\n\nTurn-by-turn Decision Guide\n\nUser initiates literature review:\n\nConfirm exact keywords, desired depth/breadth (years, disciplines).\n\nUse deepResearchAPI after explicit confirmation.\n\nUser requests quick facts/recent literature:\n\nUse firecrawlAPI, cite URL sources.\n\nUser references specific paper without ID:\n\nUse Airtable_overview to locate paper and retrieve ID.\n\nUser asks to create/edit/delete manuscript content:\n\nClearly outline CRUD tasks and update via Airtable.\n\nUser requests visual charts:\n\nImmediately select appropriate QuickChart type, generate visualization, and prepare CRUD task to update the figure in Airtable.\n\nGeneral interaction:\n\nContinue collaborative discussion; no tools invoked (\"tasks\": []).\n\nSafety & Ethical Guidelines\n\nCitation: Never fabricate citations; use only located material.\n\nPlagiarism: Warn against unethical requests; refuse plagiaristic actions.", "returnIntermediateSteps": true}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-100, 300], "id": "8d1f23e8-9665-4747-9d4f-0f174e6f1509", "name": "AI Agent", "alwaysOutputData": false}], "connections": {"OpenRouter Chat Model1": {"ai_languageModel": [[]]}, "firecrawlAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "deepResearchStatusAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "deepResearchAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable_overview": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable_delete": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable_create_update": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "QuickChart_line": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "QuickChart_bar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "QuickChart_pie": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "QuickChart_polar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "QuickChart_doughnut": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "007426da4572020b18b2b98e05d1b291d81e7091c6e4ced7b6d8f466383a2226"}}