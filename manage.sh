#!/bin/bash

# Paper Agent Management Script
# This script provides various management commands for the Paper Agent system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pids=$(lsof -ti :$port)
    if [ ! -z "$pids" ]; then
        print_warning "Killing processes on port $port: $pids"
        echo $pids | xargs kill -9
        sleep 2
        print_success "Processes on port $port terminated"
    else
        print_status "No processes found on port $port"
    fi
}

# Function to show status
show_status() {
    echo
    print_status "=== Paper Agent System Status ==="
    echo
    
    # Check Redis
    print_status "Redis Status:"
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            print_success "  ✓ Redis: Running"
            redis_info=$(redis-cli info server | grep redis_version | cut -d: -f2 | tr -d '\r')
            echo "    Version: $redis_info"
            redis_memory=$(redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
            echo "    Memory: $redis_memory"
            redis_clients=$(redis-cli info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
            echo "    Clients: $redis_clients"
        else
            print_error "  ✗ Redis: Not responding"
        fi
    else
        print_error "  ✗ Redis: redis-cli not found"
    fi
    
    echo
    print_status "Backend Status:"
    if port_in_use 8000; then
        if curl -s http://localhost:8000/api/v1/health >/dev/null 2>&1; then
            print_success "  ✓ Backend: Running on port 8000"
            echo "    URL: http://localhost:8000"
            echo "    Health: http://localhost:8000/api/v1/health"
        else
            print_warning "  ⚠ Backend: Port 8000 in use but not responding to health check"
        fi
    else
        print_error "  ✗ Backend: Not running on port 8000"
    fi
    
    echo
    print_status "Frontend Status:"
    if port_in_use 3000; then
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "  ✓ Frontend: Running on port 3000"
            echo "    URL: http://localhost:3000"
        else
            print_warning "  ⚠ Frontend: Port 3000 in use but not responding"
        fi
    else
        print_error "  ✗ Frontend: Not running on port 3000"
    fi
    
    echo
    print_status "Process Information:"
    if port_in_use 8000; then
        backend_pids=$(lsof -ti :8000)
        echo "  Backend PIDs: $backend_pids"
    fi
    if port_in_use 3000; then
        frontend_pids=$(lsof -ti :3000)
        echo "  Frontend PIDs: $frontend_pids"
    fi
    echo
}

# Function to stop all services
stop_services() {
    print_status "Stopping all Paper Agent services..."
    
    # Stop backend
    print_status "Stopping backend (port 8000)..."
    kill_port 8000
    
    # Stop frontend
    print_status "Stopping frontend (port 3000)..."
    kill_port 3000
    
    print_success "All services stopped"
}

# Function to restart services
restart_services() {
    print_status "Restarting Paper Agent services..."
    stop_services
    sleep 3
    print_status "Starting services..."
    ./start.sh
}

# Function to check logs
check_logs() {
    echo
    print_status "=== Recent System Logs ==="
    
    # Check if there are any Python processes
    if pgrep -f "python.*main.py" >/dev/null; then
        print_status "Backend process is running"
    else
        print_warning "No backend process found"
    fi
    
    # Check if there are any npm/node processes
    if pgrep -f "npm.*start\|node.*react" >/dev/null; then
        print_status "Frontend process is running"
    else
        print_warning "No frontend process found"
    fi
    
    # Show recent system logs related to our services
    print_status "Recent Redis logs:"
    if command -v journalctl >/dev/null 2>&1; then
        journalctl -u redis --no-pager -n 5 2>/dev/null || echo "  No Redis service logs found"
    else
        echo "  journalctl not available"
    fi
    
    echo
}

# Function to test connections
test_connections() {
    print_status "Testing connections..."
    
    # Test Redis
    print_status "Testing Redis connection..."
    if redis-cli ping >/dev/null 2>&1; then
        print_success "  ✓ Redis connection successful"
    else
        print_error "  ✗ Redis connection failed"
    fi
    
    # Test Backend
    print_status "Testing Backend connection..."
    if curl -s http://localhost:8000/api/v1/health >/dev/null 2>&1; then
        print_success "  ✓ Backend health check successful"
        # Test a simple API endpoint
        response=$(curl -s http://localhost:8000/api/v1/health)
        echo "    Response: $response"
    else
        print_error "  ✗ Backend health check failed"
    fi
    
    # Test Frontend
    print_status "Testing Frontend connection..."
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        print_success "  ✓ Frontend connection successful"
    else
        print_error "  ✗ Frontend connection failed"
    fi
    
    # Test WebSocket
    print_status "Testing WebSocket connection..."
    if command -v wscat >/dev/null 2>&1; then
        timeout 5 wscat -c ws://localhost:8000/api/v1/ws/test >/dev/null 2>&1 && \
        print_success "  ✓ WebSocket connection successful" || \
        print_warning "  ⚠ WebSocket connection test inconclusive"
    else
        print_warning "  ⚠ wscat not available for WebSocket testing"
        print_status "    Install with: npm install -g wscat"
    fi
    
    echo
}

# Function to show help
show_help() {
    echo
    print_status "=== Paper Agent Management Script ==="
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  status      Show status of all services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  logs        Check recent logs"
    echo "  test        Test all connections"
    echo "  redis       Show detailed Redis information"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0 status   # Show current status"
    echo "  $0 stop     # Stop all services"
    echo "  $0 test     # Test all connections"
    echo
}

# Function to show detailed Redis info
show_redis_info() {
    print_status "=== Detailed Redis Information ==="
    
    if ! command -v redis-cli >/dev/null 2>&1; then
        print_error "redis-cli not found"
        return 1
    fi
    
    if ! redis-cli ping >/dev/null 2>&1; then
        print_error "Redis is not running or not responding"
        return 1
    fi
    
    echo
    print_status "Server Information:"
    redis-cli info server | grep -E "(redis_version|os|arch|process_id|uptime)"
    
    echo
    print_status "Memory Information:"
    redis-cli info memory | grep -E "(used_memory_human|used_memory_peak_human|maxmemory_human)"
    
    echo
    print_status "Client Information:"
    redis-cli info clients | grep -E "(connected_clients|client_recent_max_input_buffer|client_recent_max_output_buffer)"
    
    echo
    print_status "Keyspace Information:"
    redis-cli info keyspace
    
    echo
    print_status "Recent Keys (sample):"
    redis-cli keys "*" | head -10
    
    echo
}

# Main execution
case "${1:-status}" in
    "status")
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        check_logs
        ;;
    "test")
        test_connections
        ;;
    "redis")
        show_redis_info
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
