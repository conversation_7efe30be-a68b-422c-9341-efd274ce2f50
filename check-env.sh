#!/bin/bash

# Environment Check Script for Paper Agent
# This script checks if all required dependencies and configurations are in place

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python
check_python() {
    print_status "Checking Python environment..."
    
    if command_exists python3; then
        python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
        print_success "✓ Python 3 found: $python_version"
    else
        print_error "✗ Python 3 not found"
        return 1
    fi
    
    if command_exists pip3; then
        pip_version=$(pip3 --version 2>&1 | cut -d' ' -f2)
        print_success "✓ pip3 found: $pip_version"
    elif command_exists pip; then
        pip_version=$(pip --version 2>&1 | cut -d' ' -f2)
        print_success "✓ pip found: $pip_version"
    else
        print_error "✗ pip/pip3 not found"
        return 1
    fi
    
    # Check virtual environment
    if [ -d "venv" ]; then
        print_success "✓ Virtual environment found"
        
        # Check if requirements are installed
        if [ -f "requirements.txt" ]; then
            source venv/bin/activate
            missing_packages=()
            while IFS= read -r package; do
                # Skip empty lines and comments
                [[ -z "$package" || "$package" =~ ^#.*$ ]] && continue
                
                package_name=$(echo "$package" | cut -d'=' -f1 | cut -d'>' -f1 | cut -d'<' -f1)
                if ! pip show "$package_name" >/dev/null 2>&1; then
                    missing_packages+=("$package_name")
                fi
            done < requirements.txt
            
            if [ ${#missing_packages[@]} -eq 0 ]; then
                print_success "✓ All Python packages installed"
            else
                print_warning "⚠ Missing packages: ${missing_packages[*]}"
                print_status "Run: source venv/bin/activate && pip install -r requirements.txt"
            fi
            deactivate 2>/dev/null || true
        else
            print_warning "⚠ requirements.txt not found"
        fi
    else
        print_error "✗ Virtual environment not found"
        print_status "Create with: python3 -m venv venv"
        return 1
    fi
}

# Check Node.js
check_nodejs() {
    print_status "Checking Node.js environment..."
    
    if command_exists node; then
        node_version=$(node --version)
        print_success "✓ Node.js found: $node_version"
    else
        print_error "✗ Node.js not found"
        return 1
    fi
    
    if command_exists npm; then
        npm_version=$(npm --version)
        print_success "✓ npm found: $npm_version"
    else
        print_error "✗ npm not found"
        return 1
    fi
    
    # Check frontend dependencies
    if [ -d "frontend" ]; then
        cd frontend
        if [ -f "package.json" ]; then
            if [ -d "node_modules" ]; then
                print_success "✓ Frontend dependencies installed"
            else
                print_warning "⚠ Frontend dependencies not installed"
                print_status "Run: cd frontend && npm install"
            fi
        else
            print_error "✗ package.json not found in frontend directory"
        fi
        cd ..
    else
        print_error "✗ Frontend directory not found"
        return 1
    fi
}

# Check Redis
check_redis() {
    print_status "Checking Redis..."
    
    if command_exists redis-server; then
        redis_version=$(redis-server --version | cut -d' ' -f3 | cut -d'=' -f2)
        print_success "✓ Redis server found: $redis_version"
    else
        print_error "✗ Redis server not found"
        print_status "Install with:"
        print_status "  Ubuntu/Debian: sudo apt-get install redis-server"
        print_status "  macOS: brew install redis"
        return 1
    fi
    
    if command_exists redis-cli; then
        print_success "✓ Redis CLI found"
        
        # Check if Redis is running
        if redis-cli ping >/dev/null 2>&1; then
            print_success "✓ Redis is running"
        else
            print_warning "⚠ Redis is not running"
            print_status "Start with: redis-server --daemonize yes"
        fi
    else
        print_error "✗ Redis CLI not found"
        return 1
    fi
}

# Check configuration files
check_config() {
    print_status "Checking configuration files..."
    
    if [ -f ".env" ]; then
        print_success "✓ .env file found"
        
        # Check for required environment variables
        required_vars=("OPENAI_API_KEY" "AIRTABLE_API_KEY" "AIRTABLE_BASE_ID")
        missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" .env 2>/dev/null; then
                missing_vars+=("$var")
            fi
        done
        
        if [ ${#missing_vars[@]} -eq 0 ]; then
            print_success "✓ All required environment variables found"
        else
            print_warning "⚠ Missing environment variables: ${missing_vars[*]}"
        fi
    else
        print_error "✗ .env file not found"
        print_status "Create .env file with required variables"
        return 1
    fi
    
    # Check other config files
    if [ -f "frontend/.env" ]; then
        print_success "✓ Frontend .env file found"
    else
        print_warning "⚠ Frontend .env file not found"
    fi
}

# Check ports
check_ports() {
    print_status "Checking port availability..."
    
    if lsof -i :8000 >/dev/null 2>&1; then
        print_warning "⚠ Port 8000 is in use (Backend port)"
        lsof -i :8000 | head -2
    else
        print_success "✓ Port 8000 is available (Backend)"
    fi
    
    if lsof -i :3000 >/dev/null 2>&1; then
        print_warning "⚠ Port 3000 is in use (Frontend port)"
        lsof -i :3000 | head -2
    else
        print_success "✓ Port 3000 is available (Frontend)"
    fi
    
    if lsof -i :6379 >/dev/null 2>&1; then
        print_success "✓ Port 6379 is in use (Redis - good)"
    else
        print_warning "⚠ Port 6379 is not in use (Redis not running)"
    fi
}

# Check system resources
check_resources() {
    print_status "Checking system resources..."
    
    # Check available memory
    if command_exists free; then
        available_mem=$(free -h | awk '/^Mem:/ {print $7}')
        print_status "Available memory: $available_mem"
    fi
    
    # Check disk space
    if command_exists df; then
        disk_usage=$(df -h . | awk 'NR==2 {print $4}')
        print_status "Available disk space: $disk_usage"
    fi
    
    # Check CPU load
    if [ -f "/proc/loadavg" ]; then
        load_avg=$(cat /proc/loadavg | cut -d' ' -f1-3)
        print_status "Load average: $load_avg"
    fi
}

# Main execution
main() {
    echo
    print_status "=== Paper Agent Environment Check ==="
    echo
    
    errors=0
    
    check_python || ((errors++))
    echo
    
    check_nodejs || ((errors++))
    echo
    
    check_redis || ((errors++))
    echo
    
    check_config || ((errors++))
    echo
    
    check_ports
    echo
    
    check_resources
    echo
    
    if [ $errors -eq 0 ]; then
        print_success "=== Environment Check Passed ==="
        print_status "Your system is ready to run Paper Agent!"
        print_status "Run './start.sh' to start all services"
    else
        print_error "=== Environment Check Failed ==="
        print_error "Found $errors error(s). Please fix them before starting."
    fi
    
    echo
}

# Run main function
main "$@"
