#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.agent import PaperAgent

async def test_agent():
    """Test the agent with a simple message."""
    try:
        print("🧪 Initializing agent...")
        agent = PaperAgent()
        
        print("🧪 Testing simple message...")
        result = await agent.process_message(
            message="Hello, can you help me?",
            session_id="test-session",
            paper_id="test-paper"
        )
        
        print(f"🧪 Result: {result}")
        
        print("\n🧪 Testing web search and paper update request...")
        result2 = await agent.process_message(
            message="Could you do a websearch on molten carbonate fuel cells and update the paper with the results?",
            session_id="test-session-2",
            paper_id="recPwNZXLI4mrSYTI"
        )

        print(f"🧪 Result2: {result2}")
        print(f"🧪 Paper updated: {result2.get('paper_updated', False)}")
        print(f"🧪 Updated paper ID: {result2.get('updated_paper_id')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent())
