#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.agent import PaperAgent

async def test_agent():
    """Test the agent with a simple message."""
    try:
        print("🧪 Initializing agent...")
        agent = PaperAgent()
        
        print("🧪 Testing simple message...")
        result = await agent.process_message(
            message="Hello, can you help me?",
            session_id="test-session",
            paper_id="test-paper"
        )
        
        print(f"🧪 Result: {result}")
        
        print("\n🧪 Testing web search request...")
        result2 = await agent.process_message(
            message="Could you do a websearch on molten carbonate fuel cells?",
            session_id="test-session-2",
            paper_id="test-paper-2"
        )
        
        print(f"🧪 Result2: {result2}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent())
