"""Test Deep Research API using curl commands to debug authentication."""

import subprocess
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def run_curl_command(command):
    """Run a curl command and return the result."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "error": "Request timed out after 30 seconds"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def test_deep_research_bearer():
    """Test Deep Research API with Bearer authentication."""
    print("🔬 Testing Deep Research API with Bearer Authentication...")
    
    api_key = os.getenv("DEEP_RESEARCH_API_KEY", "12345")
    
    # Prepare the curl command with Bearer auth
    curl_command = f'''curl -X POST "https://router.w-post.com/api/research" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {api_key}" \\
  -d '{{
    "query": "artificial intelligence applications",
    "depth": 1,
    "breadth": 1,
    "outputType": "summary",
    "priority": "normal"
  }}' \\
  --max-time 30 \\
  --verbose'''
    
    print(f"   Command: {curl_command}")
    print("   Executing...")
    
    result = run_curl_command(curl_command)
    
    if result.get("success"):
        print("✅ Bearer Auth: Request completed")
        print(f"   Response: {result['stdout']}")
        
        # Try to parse JSON response
        try:
            response_data = json.loads(result['stdout'])
            print(f"   Parsed JSON: {response_data}")
            return True, response_data
        except json.JSONDecodeError:
            print(f"   Raw response (not JSON): {result['stdout']}")
            return False, result['stdout']
    else:
        print("❌ Bearer Auth: Request failed")
        print(f"   Error: {result.get('error', 'Unknown error')}")
        print(f"   Stderr: {result.get('stderr', '')}")
        return False, None


def test_deep_research_x_api_key():
    """Test Deep Research API with X-API-Key authentication."""
    print("\n🔬 Testing Deep Research API with X-API-Key Authentication...")
    
    api_key = os.getenv("DEEP_RESEARCH_API_KEY", "12345")
    
    # Prepare the curl command with X-API-Key auth
    curl_command = f'''curl -X POST "https://router.w-post.com/api/research" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {api_key}" \\
  -d '{{
    "query": "artificial intelligence applications",
    "depth": 1,
    "breadth": 1,
    "outputType": "summary",
    "priority": "normal"
  }}' \\
  --max-time 30 \\
  --verbose'''
    
    print(f"   Command: {curl_command}")
    print("   Executing...")
    
    result = run_curl_command(curl_command)
    
    if result.get("success"):
        print("✅ X-API-Key: Request completed")
        print(f"   Response: {result['stdout']}")
        
        # Try to parse JSON response
        try:
            response_data = json.loads(result['stdout'])
            print(f"   Parsed JSON: {response_data}")
            return True, response_data
        except json.JSONDecodeError:
            print(f"   Raw response (not JSON): {result['stdout']}")
            return False, result['stdout']
    else:
        print("❌ X-API-Key: Request failed")
        print(f"   Error: {result.get('error', 'Unknown error')}")
        print(f"   Stderr: {result.get('stderr', '')}")
        return False, None


def test_deep_research_minimal():
    """Test with minimal request body."""
    print("\n🔬 Testing Deep Research API with Minimal Request...")
    
    api_key = os.getenv("DEEP_RESEARCH_API_KEY", "12345")
    
    # Try with minimal body
    curl_command = f'''curl -X POST "https://router.w-post.com/api/research" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {api_key}" \\
  -d '{{
    "query": "AI applications",
    "depth": 1,
    "breadth": 1
  }}' \\
  --max-time 30 \\
  --verbose'''
    
    print(f"   Command: {curl_command}")
    print("   Executing...")
    
    result = run_curl_command(curl_command)
    
    if result.get("success"):
        print("✅ Minimal Request: Completed")
        print(f"   Response: {result['stdout']}")
        return True, result['stdout']
    else:
        print("❌ Minimal Request: Failed")
        print(f"   Error: {result.get('error', 'Unknown error')}")
        print(f"   Stderr: {result.get('stderr', '')}")
        return False, None


def test_api_endpoint_variations():
    """Test different API endpoint variations."""
    print("\n🔬 Testing API Endpoint Variations...")
    
    api_key = os.getenv("DEEP_RESEARCH_API_KEY", "12345")
    
    endpoints = [
        "https://router.w-post.com/api/research",
        "https://router.w-post.com/v1/research", 
        "https://router.w-post.com/research",
        "https://api.w-post.com/research"
    ]
    
    for endpoint in endpoints:
        print(f"\n   Testing endpoint: {endpoint}")
        
        curl_command = f'''curl -X POST "{endpoint}" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {api_key}" \\
  -d '{{"query": "test", "depth": 1, "breadth": 1}}' \\
  --max-time 15 \\
  --connect-timeout 10'''
        
        result = run_curl_command(curl_command)
        
        if result.get("success"):
            print(f"   ✅ {endpoint}: Response received")
            print(f"      {result['stdout'][:100]}...")
        else:
            print(f"   ❌ {endpoint}: Failed")
            if "Could not resolve host" in result.get('stderr', ''):
                print(f"      Host resolution failed")
            elif "Connection refused" in result.get('stderr', ''):
                print(f"      Connection refused")
            else:
                print(f"      {result.get('stderr', '')[:100]}...")


def update_backend_with_working_auth():
    """Update the backend code with the working authentication method."""
    print("\n🔧 Updating Backend Code...")
    
    # This would update the research.py file with the correct auth method
    print("   To update the backend:")
    print("   1. If Bearer auth works: Keep current code")
    print("   2. If X-API-Key works: Update headers in research.py")
    print("   3. If different endpoint works: Update URL in config.py")


def main():
    """Test Deep Research API with different authentication methods."""
    print("🚀 Deep Research API Authentication Testing")
    print("=" * 60)
    
    api_key = os.getenv("DEEP_RESEARCH_API_KEY")
    if not api_key:
        print("❌ DEEP_RESEARCH_API_KEY not found in environment")
        print("   Using default key '12345' for testing...")
        api_key = "12345"
    else:
        print(f"✅ Using API key: {api_key[:8]}...{api_key[-4:]}")
    
    print(f"🎯 Testing with depth=1, breadth=1 as requested")
    
    # Test different authentication methods
    bearer_success, bearer_response = test_deep_research_bearer()
    x_api_success, x_api_response = test_deep_research_x_api_key()
    
    # If both fail, try minimal request and endpoint variations
    if not bearer_success and not x_api_success:
        print("\n🔍 Both auth methods failed, trying alternatives...")
        test_deep_research_minimal()
        test_api_endpoint_variations()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Bearer Authentication: {'✅ SUCCESS' if bearer_success else '❌ FAILED'}")
    print(f"   X-API-Key Authentication: {'✅ SUCCESS' if x_api_success else '❌ FAILED'}")
    
    if bearer_success:
        print("\n🎉 Bearer authentication is working!")
        print("   Your current backend code should work correctly.")
        if isinstance(bearer_response, dict) and 'taskId' in bearer_response:
            print(f"   Task ID received: {bearer_response['taskId']}")
    elif x_api_success:
        print("\n🎉 X-API-Key authentication is working!")
        print("   Need to update backend to use X-API-Key instead of Bearer.")
        if isinstance(x_api_response, dict) and 'taskId' in x_api_response:
            print(f"   Task ID received: {x_api_response['taskId']}")
    else:
        print("\n⚠️  Neither authentication method worked with the current endpoint.")
        print("   Possible issues:")
        print("   1. API endpoint has changed")
        print("   2. API key is incorrect")
        print("   3. Request format has changed")
        print("   4. Service is temporarily down")
    
    update_backend_with_working_auth()


if __name__ == "__main__":
    main()
