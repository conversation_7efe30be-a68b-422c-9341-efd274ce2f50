"""Validate the project structure and basic syntax."""

import os
import ast
import sys


def validate_python_syntax(file_path):
    """Validate Python syntax of a file."""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        ast.parse(content)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)


def check_project_structure():
    """Check if all required files and directories exist."""
    required_structure = {
        'files': [
            'main.py',
            'requirements.txt',
            'README.md',
            '.env.example',
            'test_backend.py',
            'validate_structure.py'
        ],
        'directories': [
            'app',
            'app/core',
            'app/models',
            'app/tools',
            'app/api',
            'app/services',
            'app/utils'
        ],
        'python_files': [
            'main.py',
            'app/__init__.py',
            'app/core/__init__.py',
            'app/core/config.py',
            'app/core/memory.py',
            'app/core/agent.py',
            'app/models/__init__.py',
            'app/models/state.py',
            'app/tools/__init__.py',
            'app/tools/research.py',
            'app/tools/airtable.py',
            'app/tools/visualization.py',
            'app/api/__init__.py',
            'app/api/routes.py',
            'app/services/__init__.py',
            'app/services/paper_service.py',
            'app/utils/__init__.py'
        ]
    }
    
    print("🔍 Checking project structure...")
    
    # Check files
    missing_files = []
    for file_path in required_structure['files']:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    # Check directories
    missing_dirs = []
    for dir_path in required_structure['directories']:
        if not os.path.isdir(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"✅ {dir_path}/")
    
    # Check Python syntax
    syntax_errors = []
    for py_file in required_structure['python_files']:
        if os.path.exists(py_file):
            valid, error = validate_python_syntax(py_file)
            if valid:
                print(f"✅ {py_file} (syntax OK)")
            else:
                syntax_errors.append((py_file, error))
                print(f"❌ {py_file} (syntax error)")
    
    # Summary
    print("\n📊 Validation Summary:")
    print(f"✅ Files present: {len(required_structure['files']) - len(missing_files)}/{len(required_structure['files'])}")
    print(f"✅ Directories present: {len(required_structure['directories']) - len(missing_dirs)}/{len(required_structure['directories'])}")
    print(f"✅ Python files with valid syntax: {len(required_structure['python_files']) - len(syntax_errors)}/{len(required_structure['python_files'])}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
    
    if missing_dirs:
        print(f"\n❌ Missing directories: {missing_dirs}")
    
    if syntax_errors:
        print(f"\n❌ Syntax errors:")
        for file_path, error in syntax_errors:
            print(f"   {file_path}: {error}")
    
    return len(missing_files) == 0 and len(missing_dirs) == 0 and len(syntax_errors) == 0


def check_requirements():
    """Check requirements.txt content."""
    print("\n📦 Checking requirements.txt...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'langgraph',
        'langchain',
        'langchain-openai',
        'httpx',
        'redis',
        'python-dotenv',
        'pydantic',
        'pyairtable',
        'sse-starlette',
        'markdown',
        'pygments'
    ]
    
    try:
        with open('requirements.txt', 'r') as f:
            content = f.read().lower()
        
        missing_packages = []
        for package in required_packages:
            if package.lower() not in content:
                missing_packages.append(package)
            else:
                print(f"✅ {package}")
        
        if missing_packages:
            print(f"\n❌ Missing packages: {missing_packages}")
            return False
        else:
            print("✅ All required packages present")
            return True
            
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False


def check_env_example():
    """Check .env.example content."""
    print("\n🔧 Checking .env.example...")
    
    required_vars = [
        'OPENAI_API_KEY',
        'FIRECRAWL_API_KEY',
        'DEEP_RESEARCH_API_KEY',
        'AIRTABLE_API_KEY',
        'AIRTABLE_BASE_ID'
    ]
    
    try:
        with open('.env.example', 'r') as f:
            content = f.read()
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
            else:
                print(f"✅ {var}")
        
        if missing_vars:
            print(f"\n❌ Missing environment variables: {missing_vars}")
            return False
        else:
            print("✅ All required environment variables present")
            return True
            
    except FileNotFoundError:
        print("❌ .env.example not found")
        return False


def main():
    """Main validation function."""
    print("🚀 Paper Agent Backend Validation")
    print("=" * 50)
    
    structure_ok = check_project_structure()
    requirements_ok = check_requirements()
    env_ok = check_env_example()
    
    print("\n" + "=" * 50)
    print("🎯 Final Results:")
    
    if structure_ok and requirements_ok and env_ok:
        print("✅ All validations passed!")
        print("\n📝 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Copy .env.example to .env and add your API keys")
        print("3. Start Redis server")
        print("4. Run the server: python main.py")
        return 0
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
