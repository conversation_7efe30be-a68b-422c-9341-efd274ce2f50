"""Test all tools with actual API calls."""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_search_tools():
    """Test FireCrawl search tool."""
    print("🔍 Testing FireCrawl Search Tool...")
    
    try:
        from app.tools.research import firecrawl_search_tool
        
        # Test search
        result = firecrawl_search_tool._run(
            query="artificial intelligence research papers",
            limit=3
        )
        
        if result.get("success"):
            print("✅ FireCrawl Search: SUCCESS")
            print(f"   Query: {result['query']}")
            print(f"   Results count: {result.get('count', 0)}")
            if result.get('results'):
                print(f"   Sample result: {str(result['results'])[:100]}...")
        else:
            print("❌ FireCrawl Search: FAILED")
            print(f"   Error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ FireCrawl Search: EXCEPTION - {e}")


async def test_deep_research_tools():
    """Test Deep Research tools."""
    print("\n🔬 Testing Deep Research Tools...")
    
    try:
        from app.tools.research import deep_research_tool, deep_research_status_tool
        
        # Test deep research initiation
        print("   Testing deep research initiation...")
        result = deep_research_tool._run(
            query="machine learning in healthcare",
            depth=1,
            breadth=1,
            output_type="summary",
            priority="normal"
        )
        
        if result.get("success"):
            print("✅ Deep Research Initiation: SUCCESS")
            print(f"   Task ID: {result.get('task_id')}")
            
            # Test status check if we got a task ID
            task_id = result.get('task_id')
            if task_id:
                print("   Testing status check...")
                status_result = deep_research_status_tool._run(task_id=task_id)
                
                if status_result.get("success"):
                    print("✅ Deep Research Status: SUCCESS")
                    print(f"   Status: {status_result.get('status')}")
                    print(f"   Progress: {status_result.get('progress', 0)}%")
                else:
                    print("❌ Deep Research Status: FAILED")
                    print(f"   Error: {status_result.get('error')}")
        else:
            print("❌ Deep Research Initiation: FAILED")
            print(f"   Error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Deep Research: EXCEPTION - {e}")


async def test_airtable_tools():
    """Test Airtable tools."""
    print("\n📊 Testing Airtable Tools...")
    
    try:
        from app.tools.airtable import airtable_overview_tool, airtable_create_update_tool
        
        # Test overview
        print("   Testing Airtable overview...")
        overview_result = airtable_overview_tool._run(
            table_name="Papers",
            max_records=5
        )
        
        if overview_result.get("success"):
            print("✅ Airtable Overview: SUCCESS")
            print(f"   Records found: {overview_result.get('record_count', 0)}")
            if overview_result.get('records'):
                print(f"   Sample record fields: {list(overview_result['records'][0].get('fields', {}).keys())}")
        else:
            print("❌ Airtable Overview: FAILED")
            print(f"   Error: {overview_result.get('error')}")
        
        # Test create/update
        print("   Testing Airtable create...")
        create_result = airtable_create_update_tool._run(
            table_name="Papers",
            record_data={
                "title": "Test Paper from LangGraph Agent",
                "content": "This is a test paper created by the LangGraph backend",
                "status": "draft",
                "test_field": "backend_test"
            },
            paper_id="test-paper-123"
        )
        
        if create_result.get("success"):
            print("✅ Airtable Create: SUCCESS")
            print(f"   Record ID: {create_result.get('record_id')}")
            print(f"   Operation: {create_result.get('operation')}")
        else:
            print("❌ Airtable Create: FAILED")
            print(f"   Error: {create_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Airtable: EXCEPTION - {e}")


async def test_chart_tools():
    """Test QuickChart visualization tools."""
    print("\n📈 Testing Chart Tools...")
    
    try:
        from app.tools.visualization import (
            line_chart_tool, bar_chart_tool, pie_chart_tool, 
            polar_chart_tool, doughnut_chart_tool
        )
        
        # Test data
        test_data = {
            "title": "Test Chart from Backend",
            "labels": ["Research", "Writing", "Review", "Revision"],
            "data": [25.5, 35.2, 20.1, 19.2]
        }
        
        charts_to_test = [
            ("Line Chart", line_chart_tool),
            ("Bar Chart", bar_chart_tool),
            ("Pie Chart", pie_chart_tool),
            ("Polar Chart", polar_chart_tool),
            ("Doughnut Chart", doughnut_chart_tool)
        ]
        
        for chart_name, chart_tool in charts_to_test:
            print(f"   Testing {chart_name}...")
            result = chart_tool._run(**test_data)
            
            if result.get("success"):
                print(f"✅ {chart_name}: SUCCESS")
                print(f"   Chart URL: {result.get('chart_url')[:60]}...")
                print(f"   Data points: {result.get('data_points')}")
            else:
                print(f"❌ {chart_name}: FAILED")
                print(f"   Error: {result.get('error')}")
                
    except Exception as e:
        print(f"❌ Charts: EXCEPTION - {e}")


async def test_agent_integration():
    """Test the full agent with a simple message."""
    print("\n🤖 Testing Agent Integration...")
    
    try:
        from app.core.agent import PaperAgent
        
        agent = PaperAgent()
        print("✅ Agent initialized successfully")
        print(f"   Available tools: {len(agent.tools)}")
        
        # Test a simple message processing
        print("   Testing message processing...")
        result = await agent.process_message(
            message="Hello! Can you tell me what tools you have available?",
            stream=False
        )
        
        if result.get("success"):
            print("✅ Agent Message Processing: SUCCESS")
            print(f"   Session ID: {result.get('session_id')}")
            print(f"   Response preview: {result.get('message', '')[:100]}...")
        else:
            print("❌ Agent Message Processing: FAILED")
            print(f"   Error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Agent Integration: EXCEPTION - {e}")


async def main():
    """Run all tests."""
    print("🚀 Testing Paper Agent Backend Tools")
    print("=" * 60)
    
    # Check environment variables
    required_vars = [
        "OPENAI_API_KEY",
        "FIRECRAWL_API_KEY", 
        "DEEP_RESEARCH_API_KEY",
        "AIRTABLE_API_KEY",
        "AIRTABLE_BASE_ID"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please check your .env file")
        return
    
    print("✅ All required environment variables found")
    print()
    
    # Run tests
    await test_search_tools()
    await test_deep_research_tools()
    await test_airtable_tools()
    await test_chart_tools()
    await test_agent_integration()
    
    print("\n" + "=" * 60)
    print("🎯 Tool Testing Complete!")
    print("\nIf all tests passed, your backend is ready to use!")
    print("Next: Start the server with 'python main.py'")


if __name__ == "__main__":
    asyncio.run(main())
