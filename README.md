# Paper Agent Backend

A LangGraph-based backend for the Paper Research Agent, providing enhanced state management, session handling, and streaming capabilities compared to the original N8N implementation.

## 🚀 Features

### Core Improvements over N8N
- **Stateful Workflows**: Persistent paper ID tracking and session management
- **Real-time Streaming**: WebSocket/SSE support for intermediate updates
- **Enhanced Memory**: Conversation history and context retention
- **Paper Rendering**: Live markdown rendering with syntax highlighting
- **Flexible Control Flow**: Complex conditional logic and tool orchestration

### Available Tools
- **Research Tools**
  - FireCrawl API for web search
  - Deep Research API for comprehensive analysis
  - Research status tracking
- **Data Management**
  - Airtable integration (CRUD operations)
  - Automatic paper ID context
- **Visualization**
  - Line, bar, pie, polar, and doughnut charts
  - QuickChart integration

## 📋 Prerequisites

- Python 3.8+
- Redis (for session management)
- API keys for external services

## 🛠️ Installation

1. **Clone and setup**
   ```bash
   cd paper_ui
   pip install -r requirements.txt
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Required API Keys**
   ```env
   OPENAI_API_KEY=your_openai_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key
   FIRECRAWL_API_KEY=your_firecrawl_api_key
   DEEP_RESEARCH_API_KEY=your_deep_research_api_key
   AIRTABLE_API_KEY=your_airtable_api_key
   AIRTABLE_BASE_ID=your_airtable_base_id
   ```

4. **Start Redis** (if not already running)
   ```bash
   redis-server
   ```

## 🚀 Running the Server

```bash
python main.py
```

The server will start on `http://localhost:8000`

- API Documentation: `http://localhost:8000/docs`
- Health Check: `http://localhost:8000/health`

## 🧪 Testing

Run the test script to verify functionality:

```bash
python test_backend.py
```

## 📡 API Endpoints

### Chat & Streaming
- `POST /api/v1/chat` - Standard chat
- `POST /api/v1/chat/stream` - Streaming chat with SSE

### Session Management
- `POST /api/v1/sessions` - Create session
- `GET /api/v1/sessions/{session_id}` - Get session info
- `GET /api/v1/sessions/{session_id}/history` - Get conversation history

### Paper Management
- `GET /api/v1/papers` - List papers
- `POST /api/v1/papers` - Create paper
- `GET /api/v1/papers/{paper_id}` - Get paper
- `PUT /api/v1/papers/{paper_id}` - Update paper
- `GET /api/v1/papers/{paper_id}/render` - Get rendered paper
- `PUT /api/v1/papers/{paper_id}/content` - Update content with rendering
- `GET /api/v1/papers/search` - Search papers
- `GET /api/v1/papers/{paper_id}/export` - Export paper

## 🔧 Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   LangGraph      │    │   Tools         │
│   - REST API    │◄──►│   - Agent        │◄──►│   - Research    │
│   - Streaming   │    │   - State Mgmt   │    │   - Airtable    │
│   - Sessions    │    │   - Memory       │    │   - Charts      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Redis         │    │   Paper Service  │    │   External APIs │
│   - Sessions    │    │   - Rendering    │    │   - FireCrawl   │
│   - Memory      │    │   - Storage      │    │   - Research    │
│   - Cache       │    │   - Export       │    │   - Airtable    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔄 Migration from N8N

This backend replaces your N8N workflow with the following improvements:

| N8N Limitation | LangGraph Solution |
|----------------|-------------------|
| No state persistence | Persistent paper IDs and session data |
| Limited session handling | Full conversation memory |
| Webhook-only UI | Real-time streaming updates |
| No markdown rendering | Live paper rendering |
| Limited control flow | Complex conditional workflows |

## 🛠️ Development

### Project Structure
```
app/
├── core/           # Core functionality
│   ├── agent.py    # Main LangGraph agent
│   ├── config.py   # Configuration
│   └── memory.py   # Session & memory management
├── models/         # Data models
│   └── state.py    # Agent state definitions
├── tools/          # Tool implementations
│   ├── research.py # Research tools
│   ├── airtable.py # Airtable integration
│   └── visualization.py # Chart tools
├── api/            # API routes
│   └── routes.py   # FastAPI endpoints
└── services/       # Business logic
    └── paper_service.py # Paper management
```

### Adding New Tools

1. Create tool in appropriate module under `app/tools/`
2. Import and add to `app/core/agent.py`
3. Tool will be automatically available to the agent

## 🔮 Next Steps

1. **React Frontend**: Build streaming UI with real-time updates
2. **WebSocket Support**: Add WebSocket for bidirectional communication
3. **Database Integration**: Replace Redis with PostgreSQL for persistence
4. **Authentication**: Add user authentication and authorization
5. **Deployment**: Docker containerization and cloud deployment

## 📝 License

MIT License - see LICENSE file for details
