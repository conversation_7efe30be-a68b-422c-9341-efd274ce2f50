"""Agent state models for LangGraph."""

from typing import Dict, List, Optional, Any, TypedDict
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage


class PaperInfo(BaseModel):
    """Information about a research paper."""
    paper_id: str
    title: Optional[str] = None
    content: Optional[str] = None
    status: str = "draft"  # draft, in_progress, completed
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    airtable_record_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ResearchTask(BaseModel):
    """Information about a research task."""
    task_id: str
    query: str
    status: str = "pending"  # pending, running, completed, failed
    result: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None


class SessionInfo(BaseModel):
    """Session information."""
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentState(TypedDict):
    """State for the Paper Agent workflow."""
    # Core conversation
    messages: List[BaseMessage]
    
    # Session management
    session_info: SessionInfo
    
    # Current context
    current_paper: Optional[PaperInfo]
    current_research_task: Optional[ResearchTask]
    
    # Memory and context
    conversation_summary: Optional[str]
    research_context: Dict[str, Any]
    airtable_context: Dict[str, Any]
    
    # Tool results cache
    tool_results: Dict[str, Any]
    
    # Workflow control
    next_action: Optional[str]
    error_message: Optional[str]
    
    # Streaming control
    streaming_enabled: bool
    intermediate_steps: List[Dict[str, Any]]


class ChatMessage(BaseModel):
    """Chat message model for API."""
    role: str  # user, assistant, system
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    message: str
    session_id: Optional[str] = None
    paper_id: Optional[str] = None
    stream: bool = True
    cursor_position: Optional[Dict[str, int]] = None  # {"line": 1, "column": 1}


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    message: str
    session_id: str
    paper_id: Optional[str] = None
    intermediate_steps: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
