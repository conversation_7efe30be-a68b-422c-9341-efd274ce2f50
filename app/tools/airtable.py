"""Airtable integration tools for the Paper Agent."""

from typing import Dict, Any, Optional, List
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from pyairtable import Api

from app.core.config import settings


class AirtableOverviewInput(BaseModel):
    """Input for Airtable overview tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    view_name: Optional[str] = Field(default=None, description="Specific view to query")
    max_records: int = Field(default=100, description="Maximum number of records to return")
    filter_formula: Optional[str] = Field(default=None, description="Airtable filter formula")


class AirtableOverviewTool(BaseTool):
    """Tool for getting an overview of Airtable records."""

    name: str = "airtable_overview"
    description: str = """Get an overview of records from an Airtable table.
    Use this to see existing papers, research data, or any other records in the database.
    Can filter by specific criteria or views."""
    args_schema: type[BaseModel] = AirtableOverviewInput
    
    def _run(
        self, 
        table_name: str = "Papers", 
        view_name: Optional[str] = None,
        max_records: int = 100,
        filter_formula: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get Airtable records overview."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            kwargs = {"max_records": max_records}
            if view_name:
                kwargs["view"] = view_name
            if filter_formula:
                kwargs["formula"] = filter_formula
            
            records = table.all(**kwargs)
            
            # Format records for better readability
            formatted_records = []
            for record in records:
                formatted_records.append({
                    "id": record["id"],
                    "fields": record["fields"],
                    "created_time": record.get("createdTime")
                })
            
            return {
                "success": True,
                "table_name": table_name,
                "record_count": len(formatted_records),
                "records": formatted_records
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name
            }


class AirtableCreateUpdateInput(BaseModel):
    """Input for Airtable create/update tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_data: Dict[str, Any] = Field(description="Record data to create or update")
    record_id: Optional[str] = Field(default=None, description="Record ID for updates (leave empty for create)")
    paper_id: Optional[str] = Field(default=None, description="Paper ID to associate with this record")


class AirtableCreateUpdateTool(BaseTool):
    """Tool for creating or updating Airtable records."""

    name: str = "airtable_create_update"
    description: str = """Create a new record or update an existing record in Airtable.
    Automatically includes paper ID context when available. If record_id is provided, updates the record.
    If no record_id is provided, creates a new record."""
    args_schema: type[BaseModel] = AirtableCreateUpdateInput
    
    def _run(
        self, 
        table_name: str = "Papers",
        record_data: Dict[str, Any] = {},
        record_id: Optional[str] = None,
        paper_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create or update Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Add paper_id to record data if provided and field exists
            if paper_id and "paper_id" not in record_data:
                record_data["paper_id"] = paper_id

            # Note: Removed automatic timestamp fields as they don't exist in the Airtable schema
            # Users should include timestamp fields manually if needed
            
            if record_id:
                # Update existing record
                record = table.update(record_id, record_data)
                operation = "updated"
            else:
                # Create new record
                record = table.create(record_data)
                operation = "created"
            
            return {
                "success": True,
                "operation": operation,
                "record_id": record["id"],
                "table_name": table_name,
                "record_data": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "operation": "update" if record_id else "create"
            }


class AirtableDeleteInput(BaseModel):
    """Input for Airtable delete tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_id: str = Field(description="ID of the record to delete")


class AirtableDeleteTool(BaseTool):
    """Tool for deleting Airtable records."""

    name: str = "airtable_delete"
    description: str = """Delete a record from Airtable using its record ID.
    Use this carefully as deletions cannot be undone."""
    args_schema: type[BaseModel] = AirtableDeleteInput
    
    def _run(self, table_name: str = "Papers", record_id: str = "") -> Dict[str, Any]:
        """Delete Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Get record info before deletion for confirmation
            record = table.get(record_id)
            
            # Delete the record
            table.delete(record_id)
            
            return {
                "success": True,
                "operation": "deleted",
                "record_id": record_id,
                "table_name": table_name,
                "deleted_record": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "record_id": record_id
            }


# Tool instances
airtable_overview_tool = AirtableOverviewTool()
airtable_create_update_tool = AirtableCreateUpdateTool()
airtable_delete_tool = AirtableDeleteTool()


class PaperUpdateInput(BaseModel):
    """Input for paper update tool."""
    paper_id: str = Field(description="ID of the paper to update")
    content_to_add: str = Field(description="Content to add to the paper (e.g., chart, figure, text)")
    target_section: Optional[str] = Field(default=None, description="Section name to insert content into (e.g., 'Introduction', 'Results', 'Methods'). Case-insensitive with fuzzy matching.")
    target_line: Optional[int] = Field(default=None, description="Specific line number to insert content at (1-based). Takes precedence over target_section.")
    insertion_mode: str = Field(default="append", description="How to insert content: 'append' (add to end of section/paper), 'prepend' (add to beginning), 'replace' (replace section content)")
    # Legacy fields for backward compatibility
    insertion_point: str = Field(default="end", description="Legacy field. Use target_section or target_line instead.")
    cursor_line: Optional[int] = Field(default=None, description="Legacy field. Use target_line instead.")
    cursor_column: Optional[int] = Field(default=None, description="Legacy field. Not used in new implementation.")


class PaperUpdateTool(BaseTool):
    """Tool for updating paper content in Airtable."""

    name: str = "update_paper_content"
    description: str = """Update a paper's content by adding new content (like charts, figures, or text) at a specific location.

    INSERTION OPTIONS:
    1. Section-based: Use target_section='Introduction' to add content to a specific section
    2. Line-based: Use target_line=45 to insert content at a specific line number
    3. Combined: Use both for precise placement within a section
    4. Default: Content is appended to the end of the paper

    EXAMPLES:
    - target_section='Results' → Insert in Results section
    - target_line=67 → Insert at line 67
    - target_section='Methods', target_line=45 → Insert at line 45 within Methods section
    - insertion_mode='prepend' → Add to beginning instead of end

    Perfect for adding charts, figures, or research content exactly where needed."""
    args_schema: type[BaseModel] = PaperUpdateInput

    def _run(
        self,
        paper_id: str,
        content_to_add: str,
        target_section: Optional[str] = None,
        target_line: Optional[int] = None,
        insertion_mode: str = "append",
        # Legacy parameters for backward compatibility
        insertion_point: str = "end",
        cursor_line: Optional[int] = None,
        cursor_column: Optional[int] = None
    ) -> Dict[str, Any]:
        """Update paper content."""
        print(f"🔧 update_paper_content called with:")
        print(f"   📄 paper_id: {paper_id}")
        print(f"   📝 content_to_add: {content_to_add[:100]}...")
        print(f"   🎯 target_section: {target_section}")
        print(f"   📍 target_line: {target_line}")
        print(f"   🔄 insertion_mode: {insertion_mode}")

        try:
            # First, get the current paper content
            overview_result = airtable_overview_tool._run(table_name="Table 1", max_records=100)

            if not overview_result.get("success"):
                return {
                    "success": False,
                    "error": "Failed to retrieve papers from Airtable"
                }

            # Find the paper by record ID
            paper_record = None
            for record in overview_result.get("records", []):
                if record.get("id") == paper_id:
                    paper_record = record
                    break

            if not paper_record:
                return {
                    "success": False,
                    "error": f"Paper with ID {paper_id} not found"
                }

            # Get current content
            current_content = paper_record["fields"].get("text", "")

            # Handle legacy parameters
            if cursor_line and not target_line:
                target_line = cursor_line

            # Insert new content at the specified location
            updated_content = self._insert_content_smart(
                current_content,
                content_to_add,
                target_section,
                target_line,
                insertion_mode
            )

            # Update the paper in Airtable
            update_result = airtable_create_update_tool._run(
                table_name="Table 1",
                record_data={"text": updated_content},
                record_id=paper_record["id"],
                paper_id=paper_id
            )

            if update_result.get("success"):
                # Trigger WebSocket notification for paper update
                self._notify_paper_update(paper_id, updated_content)

                # Determine insertion description
                if target_line:
                    insertion_desc = f"at line {target_line}"
                elif target_section:
                    insertion_desc = f"in '{target_section}' section"
                else:
                    insertion_desc = "at end of paper"

                return {
                    "success": True,
                    "message": f"Successfully updated paper {paper_id} ({insertion_desc})",
                    "target_section": target_section,
                    "target_line": target_line,
                    "insertion_mode": insertion_mode,
                    "content_added": content_to_add[:100] + "..." if len(content_to_add) > 100 else content_to_add,
                    "paper_updated": True,
                    "updated_paper_id": paper_id
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to update paper: {update_result.get('error')}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error updating paper: {str(e)}"
            }

    def _insert_content_smart(
        self,
        current_content: str,
        new_content: str,
        target_section: Optional[str] = None,
        target_line: Optional[int] = None,
        insertion_mode: str = "append"
    ) -> str:
        """Smart content insertion with section-based and line-based targeting."""
        lines = current_content.split('\n')

        print(f"🔍 Smart insertion logic:")
        print(f"   📄 Content lines: {len(lines)}")
        print(f"   🎯 target_section: {target_section}")
        print(f"   📍 target_line: {target_line}")
        print(f"   🔄 insertion_mode: {insertion_mode}")

        # Priority 1: Line-based insertion (most precise)
        if target_line is not None:
            print(f"   ✅ Using line-based insertion at line {target_line}")
            return self._insert_at_line(lines, new_content, target_line, insertion_mode)

        # Priority 2: Section-based insertion
        if target_section:
            print(f"   ✅ Using section-based insertion in '{target_section}'")
            return self._insert_in_section(lines, new_content, target_section, insertion_mode)

        # Priority 3: Default to end
        print(f"   ✅ Using default insertion at end")
        return self._insert_at_end(current_content, new_content)

    def _insert_at_line(self, lines: list, new_content: str, target_line: int, insertion_mode: str) -> str:
        """Insert content at a specific line number."""
        # Convert to 0-based indexing
        line_index = target_line - 1

        # Validate line number
        if line_index < 0:
            line_index = 0
        elif line_index > len(lines):
            line_index = len(lines)

        if insertion_mode == "replace" and line_index < len(lines):
            # Replace the line
            lines[line_index] = new_content
        elif insertion_mode == "prepend":
            # Insert before the line
            lines.insert(line_index, new_content)
            lines.insert(line_index + 1, "")  # Add spacing
        else:  # append (default)
            # Insert after the line
            lines.insert(line_index + 1, "")  # Add spacing
            lines.insert(line_index + 2, new_content)
            lines.insert(line_index + 3, "")  # Add spacing

        return '\n'.join(lines)

    def _insert_in_section(self, lines: list, new_content: str, target_section: str, insertion_mode: str) -> str:
        """Insert content in a specific section."""
        section_start, section_end = self._find_section_bounds(lines, target_section)

        if section_start is None:
            # Section not found, create it at the end
            return self._create_new_section(lines, new_content, target_section)

        if insertion_mode == "replace":
            # Replace section content (keep header)
            del lines[section_start + 1:section_end]
            lines.insert(section_start + 1, "")
            lines.insert(section_start + 2, new_content)
            lines.insert(section_start + 3, "")
        elif insertion_mode == "prepend":
            # Insert at beginning of section
            lines.insert(section_start + 1, "")
            lines.insert(section_start + 2, new_content)
            lines.insert(section_start + 3, "")
        else:  # append (default)
            # Insert at end of section
            lines.insert(section_end, "")
            lines.insert(section_end + 1, new_content)
            lines.insert(section_end + 2, "")

        return '\n'.join(lines)

    def _find_section_bounds(self, lines: list, target_section: str) -> tuple:
        """Find the start and end line indices of a section."""
        target_lower = target_section.lower()
        section_start = None
        section_end = len(lines)

        # Find section header (fuzzy matching)
        for i, line in enumerate(lines):
            stripped = line.strip().lower()
            if stripped.startswith('#') and target_lower in stripped:
                section_start = i
                break

        if section_start is None:
            return None, None

        # Find next section header or end of document
        for i in range(section_start + 1, len(lines)):
            if lines[i].strip().startswith('#'):
                section_end = i
                break

        return section_start, section_end

    def _create_new_section(self, lines: list, new_content: str, section_name: str) -> str:
        """Create a new section with the content."""
        section_header = f"## {section_name.title()}"
        lines.extend(["", "", section_header, "", new_content, ""])
        return '\n'.join(lines)

    def _insert_at_end(self, current_content: str, new_content: str) -> str:
        """Insert content at the end of the document."""
        return current_content + "\n\n" + new_content

    def _notify_paper_update(self, paper_id: str, updated_content: str) -> None:
        """Send WebSocket notification about paper update."""
        try:
            # Import here to avoid circular imports
            import asyncio
            from app.api.routes import manager

            # Create the notification message
            notification = {
                "type": "paper_updated",
                "paper_id": paper_id,
                "content": updated_content,
                "timestamp": __import__('datetime').datetime.now().isoformat(),
                "source": "agent_update"
            }

            # Send notification asynchronously
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're in an async context, schedule the coroutine
                    asyncio.create_task(manager.send_paper_update(paper_id, notification))
                else:
                    # If not in async context, run it
                    loop.run_until_complete(manager.send_paper_update(paper_id, notification))
            except RuntimeError:
                # No event loop running, create a new one
                asyncio.run(manager.send_paper_update(paper_id, notification))

            print(f"📡 Sent paper update notification for paper: {paper_id}")

        except Exception as e:
            print(f"⚠️ Failed to send WebSocket notification: {str(e)}")
            # Don't fail the whole operation if WebSocket notification fails


paper_update_tool = PaperUpdateTool()


class SaveUserChangesInput(BaseModel):
    """Input for save user changes tool."""
    reason: str = Field(default="Saving before AI update", description="Reason for saving user changes")


class SaveUserChangesTool(BaseTool):
    """Tool for saving user changes before AI makes updates."""

    name: str = "save_user_changes"
    description: str = """Save any unsaved user changes in the editor before making AI updates.
    This ensures that user's work is preserved before the AI modifies the paper content.
    IMPORTANT: This tool only saves existing changes - it does NOT add new content to the paper.
    After calling this tool, you MUST call update_paper_content to actually add your research/content to the paper."""
    args_schema: type[BaseModel] = SaveUserChangesInput

    def _run(self, reason: str = "Saving before AI update") -> Dict[str, Any]:
        """Save user changes."""
        try:
            # This is a signal to the frontend to save any unsaved changes
            # The actual saving happens on the frontend side
            return {
                "success": True,
                "message": f"User changes saved: {reason}. NEXT: You must now call update_paper_content to add your research/content to the paper.",
                "action": "save_triggered",
                "next_step_required": "update_paper_content"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Error triggering save: {str(e)}"
            }


save_user_changes_tool = SaveUserChangesTool()
