"""Airtable integration tools for the Paper Agent."""

from typing import Dict, Any, Optional, List
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from pyairtable import Api

from app.core.config import settings


class AirtableOverviewInput(BaseModel):
    """Input for Airtable overview tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    view_name: Optional[str] = Field(default=None, description="Specific view to query")
    max_records: int = Field(default=100, description="Maximum number of records to return")
    filter_formula: Optional[str] = Field(default=None, description="Airtable filter formula")


class AirtableOverviewTool(BaseTool):
    """Tool for getting an overview of Airtable records."""

    name: str = "airtable_overview"
    description: str = """Get an overview of records from an Airtable table.
    Use this to see existing papers, research data, or any other records in the database.
    Can filter by specific criteria or views."""
    args_schema: type[BaseModel] = AirtableOverviewInput
    
    def _run(
        self, 
        table_name: str = "Papers", 
        view_name: Optional[str] = None,
        max_records: int = 100,
        filter_formula: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get Airtable records overview."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            kwargs = {"max_records": max_records}
            if view_name:
                kwargs["view"] = view_name
            if filter_formula:
                kwargs["formula"] = filter_formula
            
            records = table.all(**kwargs)
            
            # Format records for better readability
            formatted_records = []
            for record in records:
                formatted_records.append({
                    "id": record["id"],
                    "fields": record["fields"],
                    "created_time": record.get("createdTime")
                })
            
            return {
                "success": True,
                "table_name": table_name,
                "record_count": len(formatted_records),
                "records": formatted_records
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name
            }


class AirtableCreateUpdateInput(BaseModel):
    """Input for Airtable create/update tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_data: Dict[str, Any] = Field(description="Record data to create or update")
    record_id: Optional[str] = Field(default=None, description="Record ID for updates (leave empty for create)")
    paper_id: Optional[str] = Field(default=None, description="Paper ID to associate with this record")


class AirtableCreateUpdateTool(BaseTool):
    """Tool for creating or updating Airtable records."""

    name: str = "airtable_create_update"
    description: str = """Create a new record or update an existing record in Airtable.
    Automatically includes paper ID context when available. If record_id is provided, updates the record.
    If no record_id is provided, creates a new record."""
    args_schema: type[BaseModel] = AirtableCreateUpdateInput
    
    def _run(
        self, 
        table_name: str = "Papers",
        record_data: Dict[str, Any] = {},
        record_id: Optional[str] = None,
        paper_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create or update Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Add paper_id to record data if provided and field exists
            if paper_id and "paper_id" not in record_data:
                record_data["paper_id"] = paper_id

            # Note: Removed automatic timestamp fields as they don't exist in the Airtable schema
            # Users should include timestamp fields manually if needed
            
            if record_id:
                # Update existing record
                record = table.update(record_id, record_data)
                operation = "updated"
            else:
                # Create new record
                record = table.create(record_data)
                operation = "created"
            
            return {
                "success": True,
                "operation": operation,
                "record_id": record["id"],
                "table_name": table_name,
                "record_data": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "operation": "update" if record_id else "create"
            }


class AirtableDeleteInput(BaseModel):
    """Input for Airtable delete tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_id: str = Field(description="ID of the record to delete")


class AirtableDeleteTool(BaseTool):
    """Tool for deleting Airtable records."""

    name: str = "airtable_delete"
    description: str = """Delete a record from Airtable using its record ID.
    Use this carefully as deletions cannot be undone."""
    args_schema: type[BaseModel] = AirtableDeleteInput
    
    def _run(self, table_name: str = "Papers", record_id: str = "") -> Dict[str, Any]:
        """Delete Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Get record info before deletion for confirmation
            record = table.get(record_id)
            
            # Delete the record
            table.delete(record_id)
            
            return {
                "success": True,
                "operation": "deleted",
                "record_id": record_id,
                "table_name": table_name,
                "deleted_record": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "record_id": record_id
            }


# Tool instances
airtable_overview_tool = AirtableOverviewTool()
airtable_create_update_tool = AirtableCreateUpdateTool()
airtable_delete_tool = AirtableDeleteTool()


class PaperUpdateInput(BaseModel):
    """Input for paper update tool."""
    paper_id: str = Field(description="ID of the paper to update")
    content_to_add: str = Field(description="Content to add to the paper (e.g., chart, figure, text)")
    insertion_point: str = Field(default="end", description="Where to insert content: 'end', 'after_introduction', 'before_conclusion', or 'after:SECTION_NAME'")


class PaperUpdateTool(BaseTool):
    """Tool for updating paper content in Airtable."""

    name: str = "update_paper_content"
    description: str = """Update a paper's content by adding new content (like charts, figures, or text) at a specific location.
    This tool reads the current paper content from Airtable, inserts the new content at the specified location,
    and updates the paper in Airtable. Perfect for adding charts or figures to papers."""
    args_schema: type[BaseModel] = PaperUpdateInput

    def _run(
        self,
        paper_id: str,
        content_to_add: str,
        insertion_point: str = "end"
    ) -> Dict[str, Any]:
        """Update paper content."""
        try:
            # First, get the current paper content
            overview_result = airtable_overview_tool._run(table_name="Table 1", max_records=100)

            if not overview_result.get("success"):
                return {
                    "success": False,
                    "error": "Failed to retrieve papers from Airtable"
                }

            # Find the paper
            paper_record = None
            for record in overview_result.get("records", []):
                fields = record.get("fields", {})
                if fields.get("paper_id") == paper_id:
                    paper_record = record
                    break

            if not paper_record:
                return {
                    "success": False,
                    "error": f"Paper with ID {paper_id} not found"
                }

            # Get current content
            current_content = paper_record["fields"].get("text", "")

            # Insert new content at the specified location
            updated_content = self._insert_content(current_content, content_to_add, insertion_point)

            # Update the paper in Airtable
            update_result = airtable_create_update_tool._run(
                table_name="Table 1",
                record_data={"text": updated_content},
                record_id=paper_record["id"],
                paper_id=paper_id
            )

            if update_result.get("success"):
                return {
                    "success": True,
                    "message": f"Successfully updated paper {paper_id}",
                    "insertion_point": insertion_point,
                    "content_added": content_to_add[:100] + "..." if len(content_to_add) > 100 else content_to_add
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to update paper: {update_result.get('error')}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error updating paper: {str(e)}"
            }

    def _insert_content(self, current_content: str, new_content: str, insertion_point: str) -> str:
        """Insert new content at the specified location."""
        lines = current_content.split('\n')

        if insertion_point == "end":
            # Add to the end
            return current_content + "\n\n" + new_content

        elif insertion_point == "after_introduction":
            # Find the end of introduction section
            for i, line in enumerate(lines):
                if line.strip().lower().startswith("## ") and "introduction" not in line.lower():
                    # Insert before the next section
                    lines.insert(i, "\n" + new_content + "\n")
                    return '\n'.join(lines)
            # If no section found, add after first ## heading
            for i, line in enumerate(lines):
                if line.strip().startswith("## "):
                    lines.insert(i + 1, "\n" + new_content + "\n")
                    return '\n'.join(lines)
            # Fallback to end
            return current_content + "\n\n" + new_content

        elif insertion_point == "before_conclusion":
            # Find conclusion section
            for i, line in enumerate(lines):
                if line.strip().lower().startswith("## ") and ("conclusion" in line.lower() or "summary" in line.lower()):
                    lines.insert(i, new_content + "\n\n")
                    return '\n'.join(lines)
            # Fallback to end
            return current_content + "\n\n" + new_content

        elif insertion_point.startswith("after:"):
            # Insert after a specific section
            section_name = insertion_point[6:].lower()
            for i, line in enumerate(lines):
                if line.strip().lower().startswith("## ") and section_name in line.lower():
                    # Find the next section or end
                    next_section = len(lines)
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip().startswith("## "):
                            next_section = j
                            break
                    lines.insert(next_section, "\n" + new_content + "\n")
                    return '\n'.join(lines)
            # Fallback to end
            return current_content + "\n\n" + new_content

        else:
            # Default to end
            return current_content + "\n\n" + new_content


paper_update_tool = PaperUpdateTool()
