"""Airtable integration tools for the Paper Agent."""

from typing import Dict, Any, Optional, List
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from pyairtable import Api

from app.core.config import settings


class AirtableOverviewInput(BaseModel):
    """Input for Airtable overview tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    view_name: Optional[str] = Field(default=None, description="Specific view to query")
    max_records: int = Field(default=100, description="Maximum number of records to return")
    filter_formula: Optional[str] = Field(default=None, description="Airtable filter formula")


class AirtableOverviewTool(BaseTool):
    """Tool for getting an overview of Airtable records."""
    
    name: str = "airtable_overview"
    description: str = """Get an overview of records from an Airtable table.
    Use this to see existing papers, research data, or any other records in the database.
    Can filter by specific criteria or views."""
    args_schema = AirtableOverviewInput
    
    def _run(
        self, 
        table_name: str = "Papers", 
        view_name: Optional[str] = None,
        max_records: int = 100,
        filter_formula: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get Airtable records overview."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            kwargs = {"max_records": max_records}
            if view_name:
                kwargs["view"] = view_name
            if filter_formula:
                kwargs["formula"] = filter_formula
            
            records = table.all(**kwargs)
            
            # Format records for better readability
            formatted_records = []
            for record in records:
                formatted_records.append({
                    "id": record["id"],
                    "fields": record["fields"],
                    "created_time": record.get("createdTime")
                })
            
            return {
                "success": True,
                "table_name": table_name,
                "record_count": len(formatted_records),
                "records": formatted_records
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name
            }


class AirtableCreateUpdateInput(BaseModel):
    """Input for Airtable create/update tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_data: Dict[str, Any] = Field(description="Record data to create or update")
    record_id: Optional[str] = Field(default=None, description="Record ID for updates (leave empty for create)")
    paper_id: Optional[str] = Field(default=None, description="Paper ID to associate with this record")


class AirtableCreateUpdateTool(BaseTool):
    """Tool for creating or updating Airtable records."""
    
    name: str = "airtable_create_update"
    description: str = """Create a new record or update an existing record in Airtable.
    Automatically includes paper ID context when available. If record_id is provided, updates the record.
    If no record_id is provided, creates a new record."""
    args_schema = AirtableCreateUpdateInput
    
    def _run(
        self, 
        table_name: str = "Papers",
        record_data: Dict[str, Any] = {},
        record_id: Optional[str] = None,
        paper_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create or update Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Add paper_id to record data if provided
            if paper_id and "paper_id" not in record_data:
                record_data["paper_id"] = paper_id
            
            # Add timestamp
            from datetime import datetime
            if not record_id:  # Creating new record
                record_data["created_at"] = datetime.now().isoformat()
            record_data["updated_at"] = datetime.now().isoformat()
            
            if record_id:
                # Update existing record
                record = table.update(record_id, record_data)
                operation = "updated"
            else:
                # Create new record
                record = table.create(record_data)
                operation = "created"
            
            return {
                "success": True,
                "operation": operation,
                "record_id": record["id"],
                "table_name": table_name,
                "record_data": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "operation": "update" if record_id else "create"
            }


class AirtableDeleteInput(BaseModel):
    """Input for Airtable delete tool."""
    table_name: str = Field(default="Papers", description="Name of the Airtable table")
    record_id: str = Field(description="ID of the record to delete")


class AirtableDeleteTool(BaseTool):
    """Tool for deleting Airtable records."""
    
    name: str = "airtable_delete"
    description: str = """Delete a record from Airtable using its record ID.
    Use this carefully as deletions cannot be undone."""
    args_schema = AirtableDeleteInput
    
    def _run(self, table_name: str = "Papers", record_id: str = "") -> Dict[str, Any]:
        """Delete Airtable record."""
        try:
            api = Api(settings.airtable_api_key)
            table = api.table(settings.airtable_base_id, table_name)
            
            # Get record info before deletion for confirmation
            record = table.get(record_id)
            
            # Delete the record
            table.delete(record_id)
            
            return {
                "success": True,
                "operation": "deleted",
                "record_id": record_id,
                "table_name": table_name,
                "deleted_record": record["fields"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "table_name": table_name,
                "record_id": record_id
            }


# Tool instances
airtable_overview_tool = AirtableOverviewTool()
airtable_create_update_tool = AirtableCreateUpdateTool()
airtable_delete_tool = AirtableDeleteTool()
