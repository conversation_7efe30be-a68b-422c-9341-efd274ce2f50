"""Visualization tools using QuickChart for the Paper Agent."""

import json
import urllib.parse
from typing import Dict, Any, List, Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from app.core.config import settings


class ChartDataInput(BaseModel):
    """Base input for chart tools."""
    title: str = Field(description="Chart title")
    labels: List[str] = Field(description="Chart labels")
    data: List[float] = Field(description="Chart data values")
    colors: Optional[List[str]] = Field(default=None, description="Custom colors for chart elements")
    width: int = Field(default=500, description="Chart width in pixels")
    height: int = Field(default=300, description="Chart height in pixels")


class LineChartInput(ChartDataInput):
    """Input for line chart tool."""
    x_axis_label: Optional[str] = Field(default=None, description="X-axis label")
    y_axis_label: Optional[str] = Field(default=None, description="Y-axis label")
    show_points: bool = Field(default=True, description="Show data points on line")


class LineChartTool(BaseTool):
    """Tool for creating line charts."""

    name: str = "create_line_chart"
    description: str = """Create a line chart visualization.
    Perfect for showing trends over time or relationships between variables."""
    args_schema: type[BaseModel] = LineChartInput
    
    def _run(
        self,
        title: str,
        labels: List[str],
        data: List[float],
        colors: Optional[List[str]] = None,
        width: int = 500,
        height: int = 300,
        x_axis_label: Optional[str] = None,
        y_axis_label: Optional[str] = None,
        show_points: bool = True
    ) -> Dict[str, Any]:
        """Create line chart."""
        try:
            chart_config = {
                "type": "line",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "label": title,
                        "data": data,
                        "borderColor": colors[0] if colors else "rgb(75, 192, 192)",
                        "backgroundColor": colors[0] if colors else "rgba(75, 192, 192, 0.2)",
                        "pointRadius": 4 if show_points else 0,
                        "fill": False
                    }]
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": title
                        }
                    },
                    "scales": {
                        "x": {
                            "title": {
                                "display": bool(x_axis_label),
                                "text": x_axis_label or ""
                            }
                        },
                        "y": {
                            "title": {
                                "display": bool(y_axis_label),
                                "text": y_axis_label or ""
                            }
                        }
                    }
                }
            }
            
            chart_url = self._generate_chart_url(chart_config, width, height)
            
            return {
                "success": True,
                "chart_type": "line",
                "chart_url": chart_url,
                "title": title,
                "data_points": len(data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chart_type": "line"
            }
    
    def _generate_chart_url(self, config: Dict, width: int, height: int) -> str:
        """Generate QuickChart URL."""
        chart_json = json.dumps(config)
        encoded_chart = urllib.parse.quote(chart_json)
        return f"{settings.quickchart_url}?c={encoded_chart}&w={width}&h={height}"


class BarChartTool(BaseTool):
    """Tool for creating bar charts."""

    name: str = "create_bar_chart"
    description: str = """Create a bar chart visualization.
    Great for comparing different categories or showing distributions."""
    args_schema: type[BaseModel] = ChartDataInput
    
    def _run(
        self,
        title: str,
        labels: List[str],
        data: List[float],
        colors: Optional[List[str]] = None,
        width: int = 500,
        height: int = 300
    ) -> Dict[str, Any]:
        """Create bar chart."""
        try:
            # Generate colors if not provided
            if not colors:
                colors = [f"rgba({i*50 % 255}, {i*80 % 255}, {i*120 % 255}, 0.8)" for i in range(len(data))]
            
            chart_config = {
                "type": "bar",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "label": title,
                        "data": data,
                        "backgroundColor": colors
                    }]
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": title
                        }
                    }
                }
            }
            
            chart_url = self._generate_chart_url(chart_config, width, height)
            
            return {
                "success": True,
                "chart_type": "bar",
                "chart_url": chart_url,
                "title": title,
                "data_points": len(data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chart_type": "bar"
            }
    
    def _generate_chart_url(self, config: Dict, width: int, height: int) -> str:
        """Generate QuickChart URL."""
        chart_json = json.dumps(config)
        encoded_chart = urllib.parse.quote(chart_json)
        return f"{settings.quickchart_url}?c={encoded_chart}&w={width}&h={height}"


class PieChartTool(BaseTool):
    """Tool for creating pie charts."""

    name: str = "create_pie_chart"
    description: str = """Create a pie chart visualization.
    Perfect for showing proportions and percentages of a whole."""
    args_schema: type[BaseModel] = ChartDataInput
    
    def _run(
        self,
        title: str,
        labels: List[str],
        data: List[float],
        colors: Optional[List[str]] = None,
        width: int = 500,
        height: int = 300
    ) -> Dict[str, Any]:
        """Create pie chart."""
        try:
            # Generate colors if not provided
            if not colors:
                colors = [f"hsl({i*360/len(data)}, 70%, 60%)" for i in range(len(data))]
            
            chart_config = {
                "type": "pie",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "data": data,
                        "backgroundColor": colors
                    }]
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": title
                        },
                        "legend": {
                            "position": "right"
                        }
                    }
                }
            }
            
            chart_url = self._generate_chart_url(chart_config, width, height)
            
            return {
                "success": True,
                "chart_type": "pie",
                "chart_url": chart_url,
                "title": title,
                "data_points": len(data)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chart_type": "pie"
            }
    
    def _generate_chart_url(self, config: Dict, width: int, height: int) -> str:
        """Generate QuickChart URL."""
        chart_json = json.dumps(config)
        encoded_chart = urllib.parse.quote(chart_json)
        return f"{settings.quickchart_url}?c={encoded_chart}&w={width}&h={height}"


class PolarChartTool(BaseTool):
    """Tool for creating polar area charts."""

    name: str = "create_polar_chart"
    description: str = """Create a polar area chart visualization.
    Similar to pie charts but with variable radius based on data values."""
    args_schema: type[BaseModel] = ChartDataInput

    def _run(
        self,
        title: str,
        labels: List[str],
        data: List[float],
        colors: Optional[List[str]] = None,
        width: int = 500,
        height: int = 300
    ) -> Dict[str, Any]:
        """Create polar chart."""
        try:
            # Generate colors if not provided
            if not colors:
                colors = [f"hsla({i*360/len(data)}, 70%, 60%, 0.8)" for i in range(len(data))]

            chart_config = {
                "type": "polarArea",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "data": data,
                        "backgroundColor": colors
                    }]
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": title
                        }
                    }
                }
            }

            chart_url = self._generate_chart_url(chart_config, width, height)

            return {
                "success": True,
                "chart_type": "polar",
                "chart_url": chart_url,
                "title": title,
                "data_points": len(data)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chart_type": "polar"
            }

    def _generate_chart_url(self, config: Dict, width: int, height: int) -> str:
        """Generate QuickChart URL."""
        chart_json = json.dumps(config)
        encoded_chart = urllib.parse.quote(chart_json)
        return f"{settings.quickchart_url}?c={encoded_chart}&w={width}&h={height}"


class DoughnutChartTool(BaseTool):
    """Tool for creating doughnut charts."""

    name: str = "create_doughnut_chart"
    description: str = """Create a doughnut chart visualization.
    Similar to pie charts but with a hollow center, great for showing proportions."""
    args_schema: type[BaseModel] = ChartDataInput

    def _run(
        self,
        title: str,
        labels: List[str],
        data: List[float],
        colors: Optional[List[str]] = None,
        width: int = 500,
        height: int = 300
    ) -> Dict[str, Any]:
        """Create doughnut chart."""
        try:
            # Generate colors if not provided
            if not colors:
                colors = [f"hsl({i*360/len(data)}, 65%, 55%)" for i in range(len(data))]

            chart_config = {
                "type": "doughnut",
                "data": {
                    "labels": labels,
                    "datasets": [{
                        "data": data,
                        "backgroundColor": colors
                    }]
                },
                "options": {
                    "responsive": True,
                    "plugins": {
                        "title": {
                            "display": True,
                            "text": title
                        },
                        "legend": {
                            "position": "right"
                        }
                    }
                }
            }

            chart_url = self._generate_chart_url(chart_config, width, height)

            return {
                "success": True,
                "chart_type": "doughnut",
                "chart_url": chart_url,
                "title": title,
                "data_points": len(data)
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chart_type": "doughnut"
            }

    def _generate_chart_url(self, config: Dict, width: int, height: int) -> str:
        """Generate QuickChart URL."""
        chart_json = json.dumps(config)
        encoded_chart = urllib.parse.quote(chart_json)
        return f"{settings.quickchart_url}?c={encoded_chart}&w={width}&h={height}"


# Tool instances
line_chart_tool = LineChartTool()
bar_chart_tool = BarChartTool()
pie_chart_tool = PieChartTool()
polar_chart_tool = PolarChartTool()
doughnut_chart_tool = DoughnutChartTool()
