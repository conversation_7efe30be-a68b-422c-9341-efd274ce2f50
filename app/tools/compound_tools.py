"""Compound tools that combine multiple operations into single tools."""

from typing import Dict, Any, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
import requests
import json

from .research import firecrawl_search_tool
from .airtable import save_user_changes_tool, paper_update_tool


def web_search_with_scraping(query: str, limit: int = 3, api_key: str = "12345") -> Dict[str, Any]:
    """Search and scrape web content using the combined w-post.com search API."""
    try:
        print(f"   🔍 Searching and scraping: {query}")

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

        payload = {
            "query": query,
            "limit": limit,
            "scrapeOptions": {
                "formats": ["markdown"],
                "onlyMainContent": True
            }
        }

        response = requests.post(
            "https://web.w-post.com/v1/search",
            headers=headers,
            json=payload,
            timeout=35
        )

        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Search+Scrape completed: {len(result.get('results', []))} results")
            return {
                "success": True,
                "query": query,
                "results": result.get('results', []),
                "count": len(result.get('results', []))
            }
        else:
            print(f"   ❌ Search+Scrape failed: {response.status_code}")
            return {
                "success": False,
                "error": f"HTTP {response.status_code}",
                "results": []
            }

    except Exception as e:
        print(f"   ❌ Search+Scrape error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "results": []
        }


class ResearchAndUpdateInput(BaseModel):
    """Input for research and update tool."""
    query: str = Field(description="Search query for research")
    paper_id: str = Field(description="ID of the paper to update")
    limit: int = Field(default=5, description="Number of search results to include")


class ResearchAndUpdateTool(BaseTool):
    """Tool that performs web search and automatically updates the paper."""
    
    name: str = "research_and_update_paper"
    description: str = """Perform web search and automatically update the paper with results.
    This tool combines three operations:
    1. Performs web search using the query
    2. Saves any unsaved user changes
    3. Updates the paper with formatted search results
    
    Use this when users ask to "search and update the paper" or similar requests."""
    
    def _run(
        self,
        query: str,
        paper_id: str,
        limit: int = 5
    ) -> Dict[str, Any]:
        """Execute the compound research and update operation."""
        try:
            print(f"🔍 Starting compound research and update: query='{query}', paper_id='{paper_id}'")
            
            # Step 1: Perform web search with automatic scraping
            print("   🔍 Step 1: Performing web search with scraping...")
            search_result = web_search_with_scraping(query=query, limit=min(3, limit))

            if not search_result.get("success"):
                return {
                    "success": False,
                    "error": f"Search+Scrape failed: {search_result.get('error', 'Unknown error')}"
                }
            
            # Step 2: Save user changes
            print("   💾 Step 2: Saving user changes...")
            save_result = save_user_changes_tool._run(
                reason=f"Saving before updating paper with research on: {query}"
            )
            
            if not save_result.get("success"):
                print(f"   ⚠️ Warning: Save failed: {save_result.get('error')}")
            
            # Step 3: Format and update paper
            print("   📝 Step 3: Updating paper with research results...")
            
            # Format search results - handle nested structure
            raw_results = search_result.get("results", {})
            print(f"   🔍 Raw results type: {type(raw_results)}")
            print(f"   🔍 Raw results keys: {raw_results.keys() if isinstance(raw_results, dict) else 'Not a dict'}")

            # Extract the actual results array - try multiple possible keys
            if isinstance(raw_results, dict):
                if 'data' in raw_results:
                    search_results = raw_results['data']
                elif 'results' in raw_results:
                    search_results = raw_results['results']
                else:
                    search_results = []
            elif isinstance(raw_results, list):
                search_results = raw_results
            else:
                search_results = []

            print(f"   🔍 Final search results type: {type(search_results)}, length: {len(search_results) if hasattr(search_results, '__len__') else 'N/A'}")

            if not search_results:
                return {
                    "success": False,
                    "error": "No search results found to add to paper"
                }

            # Step 3: Process search results (already scraped)
            print("   📝 Step 3: Processing search results with scraped content...")

            search_results = search_result.get("results", [])
            if not search_results:
                return {
                    "success": False,
                    "error": "No search results found to add to paper"
                }

            # Process the already-scraped results
            processed_content = []
            for i, result in enumerate(search_results, 1):
                print(f"   🔍 Processing result {i}: {result.keys() if isinstance(result, dict) else type(result)}")

                title = result.get('title', f"Search Result {i}")
                url = result.get('url', '')
                description = result.get('description', '')
                scraped_content = result.get('content', '')  # This is the scraped markdown content

                processed_content.append({
                    'title': title,
                    'url': url,
                    'description': description,
                    'content': scraped_content[:2000] if scraped_content else description,  # Limit content length
                    'scraped': bool(scraped_content)
                })

            # Create comprehensive research summary
            content_to_add = self._create_research_summary(query, processed_content)
            
            # Update paper (append to end by default)
            update_result = paper_update_tool._run(
                paper_id=paper_id,
                content_to_add=content_to_add
                # No target_section or target_line = append to end
            )
            
            if not update_result.get("success"):
                return {
                    "success": False,
                    "error": f"Paper update failed: {update_result.get('error', 'Unknown error')}"
                }
            
            print(f"   ✅ Compound operation completed successfully!")
            
            # Send WebSocket notification for auto-refresh
            self._notify_paper_update(paper_id)

            return {
                "success": True,
                "message": f"Successfully researched '{query}' and updated paper with {len(results_to_process)} results",
                "search_results_count": len(results_to_process),
                "paper_updated": True,
                "updated_paper_id": paper_id
            }
            
        except Exception as e:
            import traceback
            print(f"   ❌ Compound operation failed: {str(e)}")
            print(f"   📋 Full traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Compound operation failed: {str(e)}"
            }

    def _notify_paper_update(self, paper_id: str) -> None:
        """Send WebSocket notification about paper update."""
        try:
            # Import here to avoid circular imports
            import asyncio
            from app.api.routes import manager

            # Create the notification message
            notification = {
                "type": "paper_updated",
                "paper_id": paper_id,
                "timestamp": __import__('datetime').datetime.now().isoformat(),
                "source": "compound_research_tool",
                "action": "refresh_content"
            }

            # Send notification asynchronously
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're in an async context, schedule the coroutine
                    asyncio.create_task(manager.send_paper_update(paper_id, notification))
                else:
                    # If not in async context, run it
                    loop.run_until_complete(manager.send_paper_update(paper_id, notification))
            except RuntimeError:
                # No event loop running, create a new one
                asyncio.run(manager.send_paper_update(paper_id, notification))

            print(f"📡 Sent compound tool paper update notification for paper: {paper_id}")

        except Exception as e:
            print(f"⚠️ Failed to send WebSocket notification: {str(e)}")
            # Don't fail the whole operation if WebSocket notification fails

    def _create_research_summary(self, query: str, scraped_content: list) -> str:
        """Create a comprehensive research summary from scraped content."""
        content_to_add = f"\n\n## Research: {query.title()}\n\n"
        content_to_add += f"*Comprehensive research conducted on: {query}*\n\n"

        # Add summary section
        content_to_add += "### Summary\n\n"

        # Extract key insights from scraped content
        key_points = []
        for item in scraped_content:
            if item.get('scraped') and item.get('content'):
                # Extract first few sentences as key points
                content = item['content']
                sentences = content.split('. ')[:3]  # First 3 sentences
                if sentences:
                    key_point = '. '.join(sentences)
                    if len(key_point) > 50:  # Only add substantial content
                        key_points.append(key_point[:300] + "..." if len(key_point) > 300 else key_point)

        if key_points:
            for point in key_points[:3]:  # Top 3 key points
                content_to_add += f"- {point}\n\n"
        else:
            content_to_add += "Research findings compiled from multiple sources.\n\n"

        # Add detailed sources section
        content_to_add += "### Sources\n\n"

        for i, item in enumerate(scraped_content, 1):
            title = item.get('title', f"Source {i}")
            url = item.get('url', '')
            description = item.get('description', '')
            content = item.get('content', '')
            scraped = item.get('scraped', False)

            content_to_add += f"#### {i}. {title}\n\n"

            if scraped and content:
                # Use scraped content (first paragraph)
                paragraphs = content.split('\n\n')
                first_paragraph = paragraphs[0] if paragraphs else content[:500]
                content_to_add += f"{first_paragraph[:500]}{'...' if len(first_paragraph) > 500 else ''}\n\n"
            elif description:
                # Fallback to description
                content_to_add += f"{description}\n\n"

            if url:
                content_to_add += f"**Source:** [{url}]({url})\n\n"

            content_to_add += "---\n\n"

        return content_to_add


# Create the tool instance
research_and_update_tool = ResearchAndUpdateTool()
