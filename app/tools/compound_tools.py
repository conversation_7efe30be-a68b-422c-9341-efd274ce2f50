"""Compound tools that combine multiple operations into single tools."""

from typing import Dict, Any, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field

from .research import firecrawl_search_tool
from .airtable import save_user_changes_tool, paper_update_tool


class ResearchAndUpdateInput(BaseModel):
    """Input for research and update tool."""
    query: str = Field(description="Search query for research")
    paper_id: str = Field(description="ID of the paper to update")
    limit: int = Field(default=5, description="Number of search results to include")


class ResearchAndUpdateTool(BaseTool):
    """Tool that performs web search and automatically updates the paper."""
    
    name: str = "research_and_update_paper"
    description: str = """Perform web search and automatically update the paper with results.
    This tool combines three operations:
    1. Performs web search using the query
    2. Saves any unsaved user changes
    3. Updates the paper with formatted search results
    
    Use this when users ask to "search and update the paper" or similar requests."""
    
    def _run(
        self,
        query: str,
        paper_id: str,
        limit: int = 5
    ) -> Dict[str, Any]:
        """Execute the compound research and update operation."""
        try:
            print(f"🔍 Starting compound research and update: query='{query}', paper_id='{paper_id}'")
            
            # Step 1: Perform web search
            print("   📡 Step 1: Performing web search...")
            search_result = firecrawl_search_tool._run(query=query, limit=limit)
            
            if not search_result.get("success"):
                return {
                    "success": False,
                    "error": f"Search failed: {search_result.get('error', 'Unknown error')}"
                }
            
            # Step 2: Save user changes
            print("   💾 Step 2: Saving user changes...")
            save_result = save_user_changes_tool._run(
                reason=f"Saving before updating paper with research on: {query}"
            )
            
            if not save_result.get("success"):
                print(f"   ⚠️ Warning: Save failed: {save_result.get('error')}")
            
            # Step 3: Format and update paper
            print("   📝 Step 3: Updating paper with research results...")
            
            # Format search results
            search_results = search_result.get("results", [])
            if not search_results:
                return {
                    "success": False,
                    "error": "No search results found to add to paper"
                }
            
            # Create formatted content
            content_to_add = f"\n\n## Research: {query.title()}\n\n"
            content_to_add += f"*Research conducted on {query}*\n\n"
            
            for i, result in enumerate(search_results[:limit], 1):
                title = result.get('title', 'No title')
                url = result.get('url', '')
                content = result.get('content', '')
                snippet = content[:200] + "..." if content and len(content) > 200 else content
                
                content_to_add += f"### {i}. {title}\n\n"
                if snippet:
                    content_to_add += f"{snippet}\n\n"
                content_to_add += f"**Source:** [{url}]({url})\n\n"
            
            content_to_add += "---\n\n"
            
            # Update paper
            update_result = paper_update_tool._run(
                paper_id=paper_id,
                content_to_add=content_to_add,
                insertion_point="current_position"
            )
            
            if not update_result.get("success"):
                return {
                    "success": False,
                    "error": f"Paper update failed: {update_result.get('error', 'Unknown error')}"
                }
            
            print(f"   ✅ Compound operation completed successfully!")
            
            # Send WebSocket notification for auto-refresh
            self._notify_paper_update(paper_id)

            return {
                "success": True,
                "message": f"Successfully researched '{query}' and updated paper with {len(search_results)} results",
                "search_results_count": len(search_results),
                "paper_updated": True,
                "updated_paper_id": paper_id
            }
            
        except Exception as e:
            print(f"   ❌ Compound operation failed: {str(e)}")
            return {
                "success": False,
                "error": f"Compound operation failed: {str(e)}"
            }

    def _notify_paper_update(self, paper_id: str) -> None:
        """Send WebSocket notification about paper update."""
        try:
            # Import here to avoid circular imports
            import asyncio
            from app.api.websockets import manager

            # Create the notification message
            notification = {
                "type": "paper_updated",
                "paper_id": paper_id,
                "timestamp": __import__('datetime').datetime.now().isoformat(),
                "source": "compound_research_tool",
                "action": "refresh_content"
            }

            # Send notification asynchronously
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're in an async context, schedule the coroutine
                    asyncio.create_task(manager.send_paper_update(paper_id, notification))
                else:
                    # If not in async context, run it
                    loop.run_until_complete(manager.send_paper_update(paper_id, notification))
            except RuntimeError:
                # No event loop running, create a new one
                asyncio.run(manager.send_paper_update(paper_id, notification))

            print(f"📡 Sent compound tool paper update notification for paper: {paper_id}")

        except Exception as e:
            print(f"⚠️ Failed to send WebSocket notification: {str(e)}")
            # Don't fail the whole operation if WebSocket notification fails


# Create the tool instance
research_and_update_tool = ResearchAndUpdateTool()
