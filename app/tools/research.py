"""Research tools for the Paper Agent."""

import json
import httpx
from typing import Dict, Any, Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from app.core.config import settings
from app.models.state import AgentState, ResearchTask


class FireCrawlSearchInput(BaseModel):
    """Input for FireCrawl search tool."""
    query: str = Field(description="Search query for web search")
    limit: int = Field(default=5, description="Number of results to return (must be a number)")


class FireCrawlSearchTool(BaseTool):
    """Tool for searching the web using FireCrawl API."""

    name: str = "firecrawl_search"
    description: str = """Search the web for information using FireCrawl API.
    Use this tool to find current information, research papers, articles, and web content.
    The limit parameter must be a number, not a string."""
    args_schema: type[BaseModel] = FireCrawlSearchInput
    
    def _run(self, query: str, limit: int = 5) -> Dict[str, Any]:
        """Execute the search."""
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.firecrawl_api_key}"
            }
            
            payload = {
                "query": query,
                "limit": limit
            }
            
            with httpx.Client() as client:
                response = client.post(
                    settings.firecrawl_search_url,
                    headers=headers,
                    json=payload,
                    timeout=30.0
                )
                response.raise_for_status()
                
                result = response.json()
                return {
                    "success": True,
                    "query": query,
                    "results": result,
                    "count": len(result.get("results", []))
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }


class DeepResearchInput(BaseModel):
    """Input for Deep Research tool."""
    query: str = Field(description="Research topic or question")
    depth: int = Field(default=1, description="Research depth (1-3)")
    breadth: int = Field(default=1, description="Research breadth (1-3)")
    output_type: str = Field(default="report", description="Output type: answer or report")
    priority: str = Field(default="normal", description="Priority: low, normal, or high")


class DeepResearchTool(BaseTool):
    """Tool for conducting deep research on topics."""

    name: str = "deep_research"
    description: str = """Conduct comprehensive research on a topic.
    This tool initiates a deep research task that analyzes multiple sources and provides detailed insights.
    Returns a task ID that can be used to check status and get results.
    Output types: 'answer' for direct answers, 'report' for comprehensive reports."""
    args_schema: type[BaseModel] = DeepResearchInput
    
    def _run(
        self, 
        query: str, 
        depth: int = 1, 
        breadth: int = 1, 
        output_type: str = "report", 
        priority: str = "normal"
    ) -> Dict[str, Any]:
        """Execute deep research."""
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {settings.deep_research_api_key}"
            }
            
            payload = {
                "query": query,
                "depth": depth,
                "breadth": breadth,
                "outputType": output_type,
                "priority": priority
            }
            
            with httpx.Client() as client:
                response = client.post(
                    settings.deep_research_url,
                    headers=headers,
                    json=payload,
                    timeout=60.0
                )
                response.raise_for_status()
                
                result = response.json()
                task_id = result.get("taskId") or result.get("task_id")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "query": query,
                    "status": "initiated",
                    "message": "Deep research task started successfully"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "query": query
            }


class DeepResearchStatusInput(BaseModel):
    """Input for Deep Research Status tool."""
    task_id: str = Field(description="Task ID from deep research initiation")


class DeepResearchStatusTool(BaseTool):
    """Tool for checking the status of deep research tasks."""

    name: str = "deep_research_status"
    description: str = """Check the status of a deep research task using its task ID.
    Use this after initiating deep research to get the current status and results when completed."""
    args_schema: type[BaseModel] = DeepResearchStatusInput
    
    def _run(self, task_id: str) -> Dict[str, Any]:
        """Check research task status."""
        try:
            headers = {
                "Authorization": f"Bearer {settings.deep_research_api_key}"
            }
            
            url = f"{settings.deep_research_url}/{task_id}"
            
            with httpx.Client() as client:
                response = client.get(
                    url,
                    headers=headers,
                    timeout=30.0
                )
                response.raise_for_status()
                
                result = response.json()
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "status": result.get("status", "unknown"),
                    "progress": result.get("progress", 0),
                    "result": result.get("result"),
                    "created_at": result.get("created_at"),
                    "completed_at": result.get("completed_at")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "task_id": task_id
            }


# Tool instances
firecrawl_search_tool = FireCrawlSearchTool()
deep_research_tool = DeepResearchTool()
deep_research_status_tool = DeepResearchStatusTool()
