"""API routes for the Paper Agent."""

import json
import asyncio
from typing import Op<PERSON>, Dict, Any
from fastapi import API<PERSON>outer, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from app.models.state import ChatRequest, ChatResponse, ChatMessage
from app.core.agent import PaperAgent
from app.core.memory import Session<PERSON>anager, PaperMemory
from app.services.paper_service import PaperService

router = APIRouter()

# Global agent instance
agent = PaperAgent()
session_manager = SessionManager()
paper_memory = PaperMemory()
paper_service = PaperService()


@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Standard chat endpoint without streaming."""
    try:
        result = await agent.process_message(
            message=request.message,
            session_id=request.session_id,
            paper_id=request.paper_id,
            stream=False,
            cursor_position=request.cursor_position
        )
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return ChatResponse(
            message=result["message"],
            session_id=result["session_id"],
            paper_id=result.get("paper_id"),
            intermediate_steps=result.get("intermediate_steps", []),
            metadata={"processing_time": "N/A"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """Streaming chat endpoint using Server-Sent Events."""
    
    async def event_generator():
        try:
            # Create or get session
            session_id = request.session_id
            if not session_id:
                session_id = session_manager.create_session()
            
            # Send session info
            yield {
                "event": "session",
                "data": json.dumps({
                    "session_id": session_id,
                    "paper_id": request.paper_id
                })
            }
            
            # Send start event
            yield {
                "event": "start",
                "data": json.dumps({
                    "message": "Processing your request...",
                    "timestamp": "2024-01-01T00:00:00"
                })
            }
            
            # Process message with streaming
            result = await agent.process_message(
                message=request.message,
                session_id=session_id,
                paper_id=request.paper_id,
                stream=True,
                cursor_position=request.cursor_position
            )
            
            # Send intermediate steps
            for step in result.get("intermediate_steps", []):
                yield {
                    "event": "step",
                    "data": json.dumps(step)
                }
                await asyncio.sleep(0.1)  # Small delay for better UX
            
            # Send final response
            yield {
                "event": "response",
                "data": json.dumps({
                    "message": result["message"],
                    "session_id": result["session_id"],
                    "paper_id": result.get("paper_id"),
                    "success": result["success"]
                })
            }
            
            # Send completion event
            yield {
                "event": "complete",
                "data": json.dumps({
                    "status": "completed",
                    "timestamp": "2024-01-01T00:00:00",
                    "paper_updated": result.get("paper_updated", False),
                    "updated_paper_id": result.get("updated_paper_id")
                })
            }
            
        except Exception as e:
            yield {
                "event": "error",
                "data": json.dumps({
                    "error": str(e),
                    "timestamp": "2024-01-01T00:00:00"
                })
            }
    
    return EventSourceResponse(event_generator())


@router.get("/sessions/{session_id}")
async def get_session(session_id: str):
    """Get session information."""
    session_info = session_manager.get_session(session_id)
    if not session_info:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "session_id": session_info.session_id,
        "created_at": session_info.created_at,
        "last_activity": session_info.last_activity,
        "metadata": session_info.metadata
    }


@router.get("/sessions/{session_id}/history")
async def get_conversation_history(session_id: str):
    """Get conversation history for a session."""
    history = session_manager.get_conversation_history(session_id)
    
    formatted_history = []
    for msg in history:
        formatted_history.append({
            "role": "user" if msg.__class__.__name__ == "HumanMessage" else "assistant",
            "content": msg.content,
            "timestamp": "2024-01-01T00:00:00"  # Would be actual timestamp in production
        })
    
    return {
        "session_id": session_id,
        "messages": formatted_history
    }


@router.post("/sessions")
async def create_session(user_id: Optional[str] = None):
    """Create a new session."""
    session_id = session_manager.create_session(user_id)
    return {
        "session_id": session_id,
        "created_at": "2024-01-01T00:00:00"
    }


@router.get("/papers")
async def list_papers(session_id: Optional[str] = None):
    """List papers for a session."""
    # Use the new method that fetches from both memory and Airtable
    papers = paper_service.list_all_papers(session_id)

    return {
        "papers": [
            {
                "paper_id": paper.paper_id,
                "title": paper.title,
                "status": paper.status,
                "created_at": paper.created_at,
                "updated_at": paper.updated_at
            }
            for paper in papers
        ]
    }


@router.get("/papers/{paper_id}")
async def get_paper(paper_id: str):
    """Get paper information."""
    paper = paper_service.get_paper(paper_id)
    if not paper:
        raise HTTPException(status_code=404, detail="Paper not found")
    
    return {
        "paper_id": paper.paper_id,
        "title": paper.title,
        "content": paper.content,
        "status": paper.status,
        "created_at": paper.created_at,
        "updated_at": paper.updated_at,
        "airtable_record_id": paper.airtable_record_id,
        "metadata": paper.metadata
    }


@router.put("/papers/{paper_id}")
async def update_paper(paper_id: str, title: Optional[str] = None, content: Optional[str] = None):
    """Update paper information."""
    # Use paper service to get paper with full metadata
    paper = paper_service.get_paper(paper_id)
    if not paper:
        # Create new paper
        from app.models.state import PaperInfo
        paper = PaperInfo(
            paper_id=paper_id,
            title=title or f"Paper {paper_id}",
            content=content or "",
            status="draft"
        )
    else:
        # Update existing paper
        if title:
            paper.title = title
        if content:
            paper.content = content
        from datetime import datetime
        paper.updated_at = datetime.now()

    # Save to memory
    paper_memory.store_paper_info(paper)

    # Also save to Airtable if content is provided
    print(f"🔍 Save debug - content provided: {content is not None}, metadata: {paper.metadata}")
    if content and paper.metadata.get("airtable_record_id"):
        try:
            print(f"🔍 Attempting Airtable save for record: {paper.metadata.get('airtable_record_id')}")
            from app.tools.airtable import airtable_create_update_tool
            airtable_record_id = paper.metadata.get("airtable_record_id")
            combined_content = f"# {paper.title}\n\n{content}" if content else f"# {paper.title}"

            print(f"🔍 Combined content length: {len(combined_content)}")
            airtable_result = airtable_create_update_tool._run(
                table_name="Table 1",
                record_data={"text": combined_content},
                record_id=airtable_record_id,
                paper_id=paper_id
            )

            print(f"🔍 Airtable result: {airtable_result}")
            if not airtable_result.get("success"):
                print(f"❌ Failed to update paper in Airtable: {airtable_result.get('error')}")
            else:
                print(f"✅ Successfully updated paper in Airtable")
        except Exception as e:
            print(f"💥 Exception updating paper in Airtable: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print(f"🔍 Skipping Airtable save - content: {content is not None}, airtable_record_id: {paper.metadata.get('airtable_record_id')}")
    
    return {
        "paper_id": paper.paper_id,
        "title": paper.title,
        "content": paper.content,
        "status": paper.status,
        "updated_at": paper.updated_at
    }


@router.post("/papers")
async def create_paper(title: str, content: str = "", session_id: Optional[str] = None):
    """Create a new paper."""
    paper = paper_service.create_paper(title, content, session_id)
    return {
        "paper_id": paper.paper_id,
        "title": paper.title,
        "status": paper.status,
        "created_at": paper.created_at
    }


@router.get("/papers/{paper_id}/render")
async def get_paper_rendered(paper_id: str):
    """Get paper with rendered content."""
    result = paper_service.get_paper_with_rendering(paper_id)
    if not result["success"]:
        raise HTTPException(status_code=404, detail=result["error"])
    return result


@router.put("/papers/{paper_id}/content")
async def update_paper_content(paper_id: str, content: str):
    """Update paper content with real-time rendering."""
    result = paper_service.update_paper_content(paper_id, content)
    if not result["success"]:
        raise HTTPException(status_code=404, detail=result["error"])
    return result


@router.get("/papers/search")
async def search_papers(query: str, session_id: Optional[str] = None):
    """Search papers by content or title."""
    papers = paper_service.search_papers(query, session_id)
    return {"papers": papers}


@router.get("/papers/{paper_id}/export")
async def export_paper(paper_id: str, format: str = "markdown"):
    """Export paper in various formats."""
    result = paper_service.export_paper(paper_id, format)
    if not result["success"]:
        raise HTTPException(status_code=404, detail=result["error"])
    return result


@router.get("/charts")
async def list_charts():
    """List all generated charts."""
    # For demo purposes, return some sample charts
    sample_charts = [
        {
            "id": "chart-1",
            "type": "line",
            "title": "Research Trends Over Time",
            "url": "https://via.placeholder.com/600x400/3b82f6/ffffff?text=Line+Chart",
            "data": {"labels": ["2020", "2021", "2022", "2023"], "values": [10, 25, 40, 60]},
            "created_at": "2024-01-01T00:00:00"
        },
        {
            "id": "chart-2",
            "type": "bar",
            "title": "Publication Distribution by Field",
            "url": "https://via.placeholder.com/600x400/10b981/ffffff?text=Bar+Chart",
            "data": {"labels": ["AI", "ML", "NLP", "CV"], "values": [45, 30, 20, 35]},
            "created_at": "2024-01-01T00:00:00"
        },
        {
            "id": "chart-3",
            "type": "pie",
            "title": "Research Methodology Distribution",
            "url": "https://via.placeholder.com/600x400/f59e0b/ffffff?text=Pie+Chart",
            "data": {"labels": ["Experimental", "Theoretical", "Survey"], "values": [50, 30, 20]},
            "created_at": "2024-01-01T00:00:00"
        }
    ]

    return {"charts": sample_charts}


@router.get("/editor/cursor-position")
async def get_cursor_position():
    """Get the current cursor position from the editor."""
    # This will be set by the frontend when the cursor moves
    # For now, we'll return a placeholder that the agent can use
    return {
        "line": None,
        "column": None,
        "message": "Cursor position will be provided by frontend during tool execution"
    }


@router.post("/editor/save-before-update")
async def save_before_update():
    """Trigger frontend to save any unsaved changes before AI updates."""
    # This endpoint can be called by the AI to ensure user changes are saved
    # before making updates to the paper
    return {
        "success": True,
        "message": "Frontend should save any unsaved changes"
    }


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "paper-agent-backend",
        "version": "1.0.0"
    }
