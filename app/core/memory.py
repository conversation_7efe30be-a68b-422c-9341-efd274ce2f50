"""Memory management for the Paper Agent."""

import json
import redis
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain.memory import ConversationSummaryBufferMemory
from langchain_openai import ChatOpenAI

from app.core.config import settings
from app.models.state import SessionInfo, PaperInfo, AgentState


class SessionManager:
    """Manages user sessions and conversation history."""

    def __init__(self):
        try:
            self.redis_client = redis.from_url(settings.redis_url)
            # Test Redis connection
            self.redis_client.ping()
            self.use_redis = True
        except (redis.ConnectionError, redis.TimeoutError):
            # Fallback to in-memory storage for development
            self.use_redis = False
            self._memory_store = {}
            self._conversation_store = {}

        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            model="gpt-4o",
            temperature=0
        )
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create a new session."""
        import uuid
        session_id = str(uuid.uuid4())

        session_info = SessionInfo(
            session_id=session_id,
            user_id=user_id
        )

        if self.use_redis:
            # Store session info in Redis
            self.redis_client.setex(
                f"session:{session_id}",
                timedelta(hours=settings.session_expire_hours),
                session_info.model_dump_json()
            )
        else:
            # Store in memory
            self._memory_store[f"session:{session_id}"] = session_info

        return session_id
    
    def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        if self.use_redis:
            session_data = self.redis_client.get(f"session:{session_id}")
            if session_data:
                return SessionInfo.model_validate_json(session_data)
        else:
            return self._memory_store.get(f"session:{session_id}")
        return None
    
    def update_session_activity(self, session_id: str):
        """Update last activity timestamp."""
        session = self.get_session(session_id)
        if session:
            session.last_activity = datetime.now()
            if self.use_redis:
                self.redis_client.setex(
                    f"session:{session_id}",
                    timedelta(hours=settings.session_expire_hours),
                    session.model_dump_json()
                )
            else:
                self._memory_store[f"session:{session_id}"] = session
    
    def store_conversation_history(self, session_id: str, messages: List[BaseMessage], paper_id: Optional[str] = None):
        """Store conversation history, optionally paper-specific."""
        messages_data = []
        for msg in messages:
            messages_data.append({
                "type": msg.__class__.__name__,
                "content": msg.content,
                "timestamp": datetime.now().isoformat()
            })

        # Always use paper-specific key if paper_id is provided, otherwise use session-only key
        key = f"conversation:{paper_id}" if paper_id else f"conversation:{session_id}"

        if self.use_redis:
            self.redis_client.setex(
                key,
                timedelta(hours=settings.session_expire_hours),
                json.dumps(messages_data)
            )
        else:
            self._conversation_store[key] = messages_data
    
    def get_conversation_history(self, session_id: str, paper_id: Optional[str] = None) -> List[BaseMessage]:
        """Get conversation history, paper-specific if paper_id provided."""
        # Always use paper-specific key if paper_id is provided
        key = f"conversation:{paper_id}" if paper_id else f"conversation:{session_id}"

        if self.use_redis:
            history_data = self.redis_client.get(key)
            if history_data:
                messages_data = json.loads(history_data)
            else:
                messages_data = []
        else:
            messages_data = self._conversation_store.get(key, [])

        messages = []
        for msg_data in messages_data:
            if msg_data["type"] == "HumanMessage":
                messages.append(HumanMessage(content=msg_data["content"]))
            elif msg_data["type"] == "AIMessage":
                messages.append(AIMessage(content=msg_data["content"]))
            elif msg_data["type"] == "SystemMessage":
                messages.append(SystemMessage(content=msg_data["content"]))

        return messages

    def clear_paper_conversation(self, session_id: str, paper_id: str):
        """Clear conversation history for a specific paper."""
        key = f"conversation:{paper_id}"
        if self.use_redis:
            self.redis_client.delete(key)
        else:
            self._conversation_store.pop(key, None)

    def get_paper_conversation_keys(self, session_id: str) -> List[str]:
        """Get all paper-specific conversation keys for a session."""
        pattern = f"conversation:{session_id}:*"
        if self.use_redis:
            return [key.decode() for key in self.redis_client.keys(pattern)]
        else:
            return [key for key in self._conversation_store.keys() if key.startswith(f"conversation:{session_id}:")]


class PaperMemory:
    """Manages paper-specific memory and context."""

    def __init__(self):
        try:
            self.redis_client = redis.from_url(settings.redis_url)
            # Test Redis connection
            self.redis_client.ping()
            self.use_redis = True
        except (redis.ConnectionError, redis.TimeoutError):
            # Fallback to in-memory storage for development
            self.use_redis = False
            self._paper_store = {}
    
    def store_paper_info(self, paper_info: PaperInfo):
        """Store paper information."""
        if self.use_redis:
            self.redis_client.setex(
                f"paper:{paper_info.paper_id}",
                timedelta(days=30),  # Papers stored for 30 days
                paper_info.model_dump_json()
            )
        else:
            self._paper_store[f"paper:{paper_info.paper_id}"] = paper_info
    
    def get_paper_info(self, paper_id: str) -> Optional[PaperInfo]:
        """Get paper information."""
        if self.use_redis:
            paper_data = self.redis_client.get(f"paper:{paper_id}")
            if paper_data:
                return PaperInfo.model_validate_json(paper_data)
        else:
            return self._paper_store.get(f"paper:{paper_id}")
        return None
    
    def update_paper_content(self, paper_id: str, content: str):
        """Update paper content."""
        paper_info = self.get_paper_info(paper_id)
        if paper_info:
            paper_info.content = content
            paper_info.updated_at = datetime.now()
            self.store_paper_info(paper_info)
    
    def list_user_papers(self, session_id: str) -> List[PaperInfo]:
        """List papers associated with a session."""
        papers = []

        if self.use_redis:
            # Use Redis pattern matching
            paper_keys = self.redis_client.keys(f"paper:*")
            for key in paper_keys:
                paper_data = self.redis_client.get(key)
                if paper_data:
                    papers.append(PaperInfo.model_validate_json(paper_data))
        else:
            # Use in-memory storage
            for key, paper_info in self._paper_store.items():
                if key.startswith("paper:"):
                    papers.append(paper_info)

        return papers

    def clear_all_papers(self):
        """Clear all papers from memory cache."""
        if self.use_redis:
            try:
                # Get all paper keys
                keys = self.redis_client.keys("paper:*")
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                print(f"Error clearing papers from Redis: {e}")
        else:
            # Clear in-memory storage
            keys_to_remove = [key for key in self._paper_store.keys() if key.startswith("paper:")]
            for key in keys_to_remove:
                del self._paper_store[key]

    def invalidate_paper_cache(self, paper_id: str):
        """Invalidate cache for a specific paper to force refresh from Airtable."""
        if self.use_redis:
            try:
                self.redis_client.delete(f"paper:{paper_id}")
            except Exception as e:
                print(f"Error invalidating paper cache: {e}")
        else:
            self._paper_store.pop(f"paper:{paper_id}", None)


class ConversationMemory:
    """Manages conversation memory with summarization."""

    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            model="gpt-4o",
            temperature=0
        )
        try:
            self.redis_client = redis.from_url(settings.redis_url)
            # Test Redis connection
            self.redis_client.ping()
            self.use_redis = True
        except (redis.ConnectionError, redis.TimeoutError):
            # Fallback to in-memory storage for development
            self.use_redis = False
            self._memory_store = {}
    
    def get_memory_for_session(self, session_id: str) -> ConversationSummaryBufferMemory:
        """Get conversation memory for a session."""
        memory = ConversationSummaryBufferMemory(
            llm=self.llm,
            max_token_limit=2000,
            return_messages=True
        )

        # Load existing summary if available
        if self.use_redis:
            summary_data = self.redis_client.get(f"summary:{session_id}")
            if summary_data:
                summary_info = json.loads(summary_data)
                memory.moving_summary_buffer = summary_info.get("summary", "")
        else:
            summary_info = self._memory_store.get(f"summary:{session_id}")
            if summary_info:
                memory.moving_summary_buffer = summary_info.get("summary", "")

        return memory
    
    def save_memory_summary(self, session_id: str, memory: ConversationSummaryBufferMemory):
        """Save conversation summary."""
        summary_data = {
            "summary": memory.moving_summary_buffer,
            "updated_at": datetime.now().isoformat()
        }

        if self.use_redis:
            self.redis_client.setex(
                f"summary:{session_id}",
                timedelta(hours=settings.session_expire_hours),
                json.dumps(summary_data)
            )
        else:
            self._memory_store[f"summary:{session_id}"] = summary_data
