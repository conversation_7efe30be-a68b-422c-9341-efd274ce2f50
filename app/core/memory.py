"""Memory management for the Paper Agent."""

import json
import redis
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain.memory import ConversationSummaryBufferMemory
from langchain_openai import ChatOpenAI

from app.core.config import settings
from app.models.state import SessionInfo, PaperInfo, AgentState


class SessionManager:
    """Manages user sessions and conversation history."""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.redis_url)
        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            model="gpt-3.5-turbo",
            temperature=0
        )
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create a new session."""
        import uuid
        session_id = str(uuid.uuid4())
        
        session_info = SessionInfo(
            session_id=session_id,
            user_id=user_id
        )
        
        # Store session info in Redis
        self.redis_client.setex(
            f"session:{session_id}",
            timedelta(hours=settings.session_expire_hours),
            session_info.model_dump_json()
        )
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        session_data = self.redis_client.get(f"session:{session_id}")
        if session_data:
            return SessionInfo.model_validate_json(session_data)
        return None
    
    def update_session_activity(self, session_id: str):
        """Update last activity timestamp."""
        session = self.get_session(session_id)
        if session:
            session.last_activity = datetime.now()
            self.redis_client.setex(
                f"session:{session_id}",
                timedelta(hours=settings.session_expire_hours),
                session.model_dump_json()
            )
    
    def store_conversation_history(self, session_id: str, messages: List[BaseMessage]):
        """Store conversation history."""
        messages_data = []
        for msg in messages:
            messages_data.append({
                "type": msg.__class__.__name__,
                "content": msg.content,
                "timestamp": datetime.now().isoformat()
            })
        
        self.redis_client.setex(
            f"conversation:{session_id}",
            timedelta(hours=settings.session_expire_hours),
            json.dumps(messages_data)
        )
    
    def get_conversation_history(self, session_id: str) -> List[BaseMessage]:
        """Get conversation history."""
        history_data = self.redis_client.get(f"conversation:{session_id}")
        if not history_data:
            return []
        
        messages = []
        for msg_data in json.loads(history_data):
            if msg_data["type"] == "HumanMessage":
                messages.append(HumanMessage(content=msg_data["content"]))
            elif msg_data["type"] == "AIMessage":
                messages.append(AIMessage(content=msg_data["content"]))
        
        return messages


class PaperMemory:
    """Manages paper-specific memory and context."""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.redis_url)
    
    def store_paper_info(self, paper_info: PaperInfo):
        """Store paper information."""
        self.redis_client.setex(
            f"paper:{paper_info.paper_id}",
            timedelta(days=30),  # Papers stored for 30 days
            paper_info.model_dump_json()
        )
    
    def get_paper_info(self, paper_id: str) -> Optional[PaperInfo]:
        """Get paper information."""
        paper_data = self.redis_client.get(f"paper:{paper_id}")
        if paper_data:
            return PaperInfo.model_validate_json(paper_data)
        return None
    
    def update_paper_content(self, paper_id: str, content: str):
        """Update paper content."""
        paper_info = self.get_paper_info(paper_id)
        if paper_info:
            paper_info.content = content
            paper_info.updated_at = datetime.now()
            self.store_paper_info(paper_info)
    
    def list_user_papers(self, session_id: str) -> List[PaperInfo]:
        """List papers associated with a session."""
        # This would typically query a database
        # For now, we'll use Redis pattern matching
        paper_keys = self.redis_client.keys(f"paper:*")
        papers = []
        
        for key in paper_keys:
            paper_data = self.redis_client.get(key)
            if paper_data:
                papers.append(PaperInfo.model_validate_json(paper_data))
        
        return papers


class ConversationMemory:
    """Manages conversation memory with summarization."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            model="gpt-3.5-turbo",
            temperature=0
        )
        self.redis_client = redis.from_url(settings.redis_url)
    
    def get_memory_for_session(self, session_id: str) -> ConversationSummaryBufferMemory:
        """Get conversation memory for a session."""
        memory = ConversationSummaryBufferMemory(
            llm=self.llm,
            max_token_limit=2000,
            return_messages=True
        )
        
        # Load existing summary if available
        summary_data = self.redis_client.get(f"summary:{session_id}")
        if summary_data:
            summary_info = json.loads(summary_data)
            memory.moving_summary_buffer = summary_info.get("summary", "")
        
        return memory
    
    def save_memory_summary(self, session_id: str, memory: ConversationSummaryBufferMemory):
        """Save conversation summary."""
        summary_data = {
            "summary": memory.moving_summary_buffer,
            "updated_at": datetime.now().isoformat()
        }
        
        self.redis_client.setex(
            f"summary:{session_id}",
            timedelta(hours=settings.session_expire_hours),
            json.dumps(summary_data)
        )
