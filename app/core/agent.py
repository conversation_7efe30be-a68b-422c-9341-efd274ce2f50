"""Main LangGraph agent for the Paper Agent."""

import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>bda
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from app.models.state import AgentState, PaperInfo, SessionInfo, ResearchTask
from app.core.memory import SessionManager, PaperMemory, ConversationMemory
from app.core.config import settings

# Import all tools
from app.tools.research import firecrawl_search_tool, deep_research_tool, deep_research_status_tool
from app.tools.airtable import airtable_overview_tool, airtable_create_update_tool, airtable_delete_tool, paper_update_tool
from app.tools.visualization import (
    line_chart_tool, bar_chart_tool, pie_chart_tool, 
    polar_chart_tool, doughnut_chart_tool
)


class PaperAgent:
    """Main Paper Agent using LangGraph."""
    
    def __init__(self):
        self.session_manager = SessionManager()
        self.paper_memory = PaperMemory()
        self.conversation_memory = ConversationMemory()
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            api_key=settings.openai_api_key,
            model="gpt-4o",
            temperature=0.1,
            streaming=True
        )
        
        # Collect all tools
        self.tools = [
            firecrawl_search_tool,
            deep_research_tool,
            deep_research_status_tool,
            airtable_overview_tool,
            airtable_create_update_tool,
            airtable_delete_tool,
            paper_update_tool,

            line_chart_tool,
            bar_chart_tool,
            pie_chart_tool,
            polar_chart_tool,
            doughnut_chart_tool
        ]
        
        # Create tool executor function
        def execute_tool(tool_call):
            """Execute a tool call."""
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]

            # Find the tool
            for tool in self.tools:
                if tool.name == tool_name:
                    return tool._run(**tool_args)

            return {"error": f"Tool {tool_name} not found"}

        self.tool_executor = execute_tool
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tools", self._tool_node)
        workflow.add_node("memory_update", self._memory_update_node)
        
        # Set entry point
        workflow.set_entry_point("agent")
        
        # Add edges
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "continue": "tools",
                "end": "memory_update"
            }
        )
        workflow.add_edge("tools", "agent")
        workflow.add_edge("memory_update", END)
        
        return workflow.compile()
    
    def _agent_node(self, state: AgentState) -> AgentState:
        """Main agent reasoning node."""
        try:
            # Get conversation history
            messages = state["messages"]
            
            # Add system message with context
            system_message = self._create_system_message(state)
            full_messages = [system_message] + messages
            
            # Get LLM response
            response = self.llm.bind_tools(self.tools).invoke(full_messages)
            
            # Update state
            state["messages"].append(response)
            
            # Add intermediate step for streaming
            if state.get("streaming_enabled", True):
                state["intermediate_steps"].append({
                    "type": "agent_response",
                    "content": response.content,
                    "timestamp": datetime.now().isoformat()
                })
            
            return state
            
        except Exception as e:
            state["error_message"] = str(e)
            return state
    
    def _tool_node(self, state: AgentState) -> AgentState:
        """Tool execution node."""
        try:
            last_message = state["messages"][-1]
            
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                # Execute tools
                tool_results = []
                
                for tool_call in last_message.tool_calls:
                    # Add intermediate step for streaming
                    if state.get("streaming_enabled", True):
                        state["intermediate_steps"].append({
                            "type": "tool_call",
                            "tool_name": tool_call["name"],
                            "args": tool_call["args"],
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    # Execute tool
                    # For paper update tool, automatically add cursor position if available
                    if tool_call["name"] == "update_paper_content" and state.get("cursor_position"):
                        cursor_pos = state["cursor_position"]
                        if "cursor_line" not in tool_call["args"]:
                            tool_call["args"]["cursor_line"] = cursor_pos.get("line")
                        if "cursor_column" not in tool_call["args"]:
                            tool_call["args"]["cursor_column"] = cursor_pos.get("column")

                    result = self.tool_executor(tool_call)
                    tool_results.append(result)
                    
                    # Store result in cache
                    state["tool_results"][tool_call["id"]] = result
                    
                    # Add intermediate step for streaming
                    if state.get("streaming_enabled", True):
                        state["intermediate_steps"].append({
                            "type": "tool_result",
                            "tool_name": tool_call["name"],
                            "result": result,
                            "timestamp": datetime.now().isoformat()
                        })
                
                # Add tool results to messages
                for tool_call, result in zip(last_message.tool_calls, tool_results):
                    tool_message = ToolMessage(
                        content=str(result),
                        tool_call_id=tool_call["id"]
                    )
                    state["messages"].append(tool_message)
            
            return state
            
        except Exception as e:
            state["error_message"] = str(e)
            return state
    
    def _memory_update_node(self, state: AgentState) -> AgentState:
        """Update memory and session information."""
        try:
            session_info = state["session_info"]
            
            # Update session activity
            self.session_manager.update_session_activity(session_info.session_id)
            
            # Store conversation history
            self.session_manager.store_conversation_history(
                session_info.session_id, 
                state["messages"]
            )
            
            # Update paper info if available
            if state.get("current_paper"):
                self.paper_memory.store_paper_info(state["current_paper"])
            
            return state
            
        except Exception as e:
            state["error_message"] = str(e)
            return state
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if we should continue with tools or end."""
        last_message = state["messages"][-1]
        
        # If there's an error, end
        if state.get("error_message"):
            return "end"
        
        # If the last message has tool calls, continue to tools
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        
        # Otherwise, end
        return "end"
    
    def _create_system_message(self, state: AgentState) -> SystemMessage:
        """Create system message with current context."""
        context_parts = [
            "You are a helpful research assistant specializing in academic papers and research.",
            "You have access to various tools for research, data management, and visualization.",
            "",
            "IMPORTANT: When users ask you to create charts, visualizations, or figures, you MUST use the appropriate chart tools:",
            "- create_line_chart: For line charts showing trends or relationships",
            "- create_bar_chart: For bar charts comparing categories",
            "- create_pie_chart: For pie charts showing proportions",
            "- create_polar_chart: For polar area charts",
            "- create_doughnut_chart: For doughnut charts",
            "",
            "When creating charts, extract the data from the user's request and call the appropriate tool immediately.",
            "Do not just describe what you would do - actually execute the tool to create the chart.",
            "",
            "IMPORTANT: When users ask you to add charts or figures to a paper, follow these steps:",
            "1. First create the chart using the appropriate chart tool",
            "2. Then use the 'update_paper_content' tool to add the chart to the paper",
            "3. Format the chart as markdown: ![Chart Title](chart_url)",
            "4. Choose appropriate insertion points: 'current_position', 'end', 'after_introduction', 'before_conclusion', or 'after:SECTION_NAME'",
        ]
        
        # Add paper context
        if state.get("current_paper"):
            paper = state["current_paper"]
            context_parts.append(f"Current paper: {paper.title} (ID: {paper.paper_id})")
            if paper.content:
                context_parts.append(f"Paper content preview: {paper.content[:200]}...")
        
        # Add research context
        if state.get("research_context"):
            context_parts.append("Recent research context available.")
        
        # Add session context
        session_info = state["session_info"]
        context_parts.append(f"Session ID: {session_info.session_id}")

        # Add cursor position context if available
        if state.get("cursor_position"):
            cursor_pos = state["cursor_position"]
            context_parts.append(f"Current cursor position: Line {cursor_pos['line']}, Column {cursor_pos['column']}")
            context_parts.append("When adding content to papers, use 'current_position' as the insertion_point to insert at the cursor location.")
        
        system_content = "\n".join(context_parts)
        return SystemMessage(content=system_content)
    
    async def process_message(
        self,
        message: str,
        session_id: Optional[str] = None,
        paper_id: Optional[str] = None,
        stream: bool = True,
        cursor_position: Optional[Dict[str, int]] = None
    ) -> Dict[str, Any]:
        """Process a user message through the agent."""
        try:
            # Create or get session
            if not session_id:
                session_id = self.session_manager.create_session()
            
            session_info = self.session_manager.get_session(session_id)
            if not session_info:
                session_id = self.session_manager.create_session()
                session_info = self.session_manager.get_session(session_id)
            
            # Get conversation history
            conversation_history = self.session_manager.get_conversation_history(session_id)
            
            # Get paper info if provided
            current_paper = None
            if paper_id:
                current_paper = self.paper_memory.get_paper_info(paper_id)
                if not current_paper:
                    # Create new paper
                    current_paper = PaperInfo(
                        paper_id=paper_id,
                        title=f"Paper {paper_id}",
                        status="draft"
                    )
                    self.paper_memory.store_paper_info(current_paper)
            
            # Create initial state
            initial_state: AgentState = {
                "messages": conversation_history + [HumanMessage(content=message)],
                "session_info": session_info,
                "current_paper": current_paper,
                "current_research_task": None,
                "conversation_summary": None,
                "research_context": {},
                "airtable_context": {},
                "tool_results": {},
                "next_action": None,
                "error_message": None,
                "streaming_enabled": stream,
                "intermediate_steps": [],
                "cursor_position": cursor_position
            }
            
            # Run the graph
            final_state = await self.graph.ainvoke(initial_state)
            
            # Extract response
            last_message = final_state["messages"][-1]
            response_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
            
            return {
                "success": True,
                "message": response_content,
                "session_id": session_id,
                "paper_id": paper_id,
                "intermediate_steps": final_state.get("intermediate_steps", []),
                "error": final_state.get("error_message")
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id,
                "paper_id": paper_id
            }
