"""Configuration management for the Paper Agent."""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Keys
    openai_api_key: str
    openrouter_api_key: Optional[str] = None
    firecrawl_api_key: Optional[str] = None
    deep_research_api_key: Optional[str] = None
    airtable_api_key: Optional[str] = None
    airtable_base_id: Optional[str] = None
    
    # Database
    database_url: str = "sqlite:///./paper_agent.db"
    redis_url: str = "redis://localhost:6379"
    
    # Server Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # Session Configuration
    session_secret_key: str
    session_expire_hours: int = 24
    
    # API URLs
    firecrawl_search_url: str = "https://web.w-post.com/v1/search"
    deep_research_url: str = "https://router.w-post.com/api/research"
    quickchart_url: str = "https://quickchart.io/chart"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
