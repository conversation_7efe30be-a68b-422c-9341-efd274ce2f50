"""Paper management and rendering service."""

import markdown
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import HtmlFormatter

from app.models.state import PaperInfo
from app.core.memory import PaperMemory


class PaperRenderer:
    """Handles markdown rendering and paper formatting."""
    
    def __init__(self):
        self.md = markdown.Markdown(
            extensions=[
                'markdown.extensions.codehilite',
                'markdown.extensions.fenced_code',
                'markdown.extensions.tables',
                'markdown.extensions.toc',
                'markdown.extensions.footnotes'
            ],
            extension_configs={
                'markdown.extensions.codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': True
                }
            }
        )
    
    def render_markdown(self, content: str) -> Dict[str, Any]:
        """Render markdown content to HTML."""
        try:
            # Reset markdown instance
            self.md.reset()
            
            # Convert markdown to HTML
            html_content = self.md.convert(content)
            
            # Extract table of contents if available
            toc = getattr(self.md, 'toc', '')
            
            # Extract metadata if present
            metadata = getattr(self.md, 'Meta', {})
            
            return {
                "success": True,
                "html": html_content,
                "toc": toc,
                "metadata": metadata,
                "word_count": len(content.split()),
                "char_count": len(content)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "html": f"<p>Error rendering markdown: {str(e)}</p>"
            }
    
    def extract_citations(self, content: str) -> List[Dict[str, Any]]:
        """Extract citations from markdown content."""
        # Simple citation pattern matching
        citation_patterns = [
            r'\[@([^\]]+)\]',  # [@author2023]
            r'\(([^)]+\d{4}[^)]*)\)',  # (Author, 2023)
            r'([A-Z][a-z]+ et al\., \d{4})',  # Author et al., 2023
        ]
        
        citations = []
        for pattern in citation_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                citations.append({
                    "text": match,
                    "type": "reference",
                    "position": content.find(match)
                })
        
        return citations
    
    def generate_paper_outline(self, content: str) -> Dict[str, Any]:
        """Generate an outline from paper content."""
        lines = content.split('\n')
        outline = []
        current_section = None
        
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('# ').strip()
                
                section = {
                    "level": level,
                    "title": title,
                    "subsections": []
                }
                
                if level == 1:
                    outline.append(section)
                    current_section = section
                elif current_section and level == 2:
                    current_section["subsections"].append(section)
        
        return {
            "outline": outline,
            "section_count": len(outline),
            "total_headings": sum(1 + len(s.get("subsections", [])) for s in outline)
        }


class PaperService:
    """Service for managing papers and their content."""

    def __init__(self):
        self.paper_memory = PaperMemory()
        self.renderer = PaperRenderer()

        # Import Airtable tools for external storage
        try:
            from app.tools.airtable import airtable_create_update_tool
            self.airtable_tool = airtable_create_update_tool
            self.airtable_enabled = True
        except ImportError:
            self.airtable_tool = None
            self.airtable_enabled = False
    
    def create_paper(
        self,
        title: str,
        content: str = "",
        session_id: Optional[str] = None
    ) -> PaperInfo:
        """Create a new paper."""
        import uuid
        paper_id = str(uuid.uuid4())

        paper = PaperInfo(
            paper_id=paper_id,
            title=title,
            content=content,
            status="draft",
            metadata={
                "session_id": session_id,
                "created_by": "paper_agent"
            }
        )

        # Store in memory first
        self.paper_memory.store_paper_info(paper)

        # Also create in Airtable if available
        if self.airtable_enabled and self.airtable_tool:
            try:
                # Prepare data for Airtable (using discovered field names)
                # Combine title and content into the 'text' field since that's what Airtable expects
                combined_content = f"# {title}\n\n{content}" if content else f"# {title}"

                airtable_data = {
                    "paper_id": paper_id,
                    "text": combined_content
                }

                # Create record in Airtable
                airtable_result = self.airtable_tool._run(
                    table_name="Table 1",  # Using the discovered table name
                    record_data=airtable_data,
                    paper_id=paper_id
                )

                if airtable_result.get("success"):
                    # Store Airtable record ID in paper metadata
                    paper.metadata["airtable_record_id"] = airtable_result.get("record_id")
                    self.paper_memory.store_paper_info(paper)  # Update with Airtable ID
                else:
                    # Log error but don't fail the paper creation
                    print(f"Warning: Failed to create paper in Airtable: {airtable_result.get('error')}")

            except Exception as e:
                # Log error but don't fail the paper creation
                print(f"Warning: Exception creating paper in Airtable: {str(e)}")

        return paper

    def list_all_papers(self, session_id: Optional[str] = None) -> List[PaperInfo]:
        """List all papers from Airtable as the source of truth."""
        papers = []

        # Use Airtable as the primary source of truth
        # Clear memory cache first to avoid stale data
        self.paper_memory.clear_all_papers()

        # Get papers from Airtable if available
        if self.airtable_enabled and self.airtable_tool:
            try:
                from app.tools.airtable import airtable_overview_tool
                airtable_result = airtable_overview_tool._run(
                    table_name="Table 1",
                    max_records=100
                )

                if airtable_result.get("success"):
                    airtable_records = airtable_result.get("records", [])

                    for record in airtable_records:
                        fields = record.get("fields", {})
                        text_content = fields.get("text", "")

                        # Use Airtable record ID as paper_id for uniqueness
                        paper_id = record.get("id")

                        # Try to get title from dedicated title field, fallback to extracting from content
                        title = fields.get("title") or fields.get("Title") or fields.get("paper_title")

                        if not title:
                            # Extract title from text content (first heading) as fallback
                            lines = text_content.split('\n')
                            title = "Untitled Paper"

                            for line in lines:
                                line = line.strip()
                                if line.startswith('# '):
                                    title = line[2:].strip()
                                    break
                                elif line.startswith('## '):
                                    title = line[3:].strip()
                                    break
                                elif line.startswith('### '):
                                    title = line[4:].strip()
                                    break

                        if paper_id and text_content:  # Only process if we have content
                            # Create PaperInfo from Airtable data
                            paper = PaperInfo(
                                paper_id=paper_id,
                                title=title,
                                content=text_content,
                                status="draft",
                                metadata={
                                    "airtable_record_id": record.get("id"),
                                    "source": "airtable"
                                }
                            )
                            papers.append(paper)
                            # Store in memory for faster access
                            self.paper_memory.store_paper_info(paper)

            except Exception as e:
                print(f"Warning: Failed to fetch papers from Airtable: {str(e)}")

        # If no papers from Airtable, fallback to memory (but this should be rare)
        if not papers:
            memory_papers = self.paper_memory.list_user_papers(session_id or "")
            papers.extend(memory_papers)

        return papers

    def get_paper(self, paper_id: str, force_refresh: bool = False) -> Optional[PaperInfo]:
        """Get a single paper from both memory and Airtable."""
        # If force_refresh is True, invalidate cache first
        if force_refresh:
            self.paper_memory.invalidate_paper_cache(paper_id)

        # First try to get from memory
        paper = self.paper_memory.get_paper_info(paper_id)
        if paper and not force_refresh:
            return paper

        # If not found in memory or force refresh, try Airtable
        if self.airtable_enabled and self.airtable_tool:
            try:
                from app.tools.airtable import airtable_overview_tool
                airtable_result = airtable_overview_tool._run(
                    table_name="Table 1",
                    max_records=100
                )

                if airtable_result.get("success"):
                    airtable_records = airtable_result.get("records", [])

                    for record in airtable_records:
                        fields = record.get("fields", {})
                        record_id = record.get("id")

                        if record_id == paper_id:
                            text_content = fields.get("text", "")

                            # Try to get title from dedicated title field, fallback to extracting from content
                            title = fields.get("title") or fields.get("Title") or fields.get("paper_title")

                            if not title:
                                # Extract title from text content (first heading) as fallback
                                lines = text_content.split('\n')
                                title = "Untitled Paper"

                                for line in lines:
                                    line = line.strip()
                                    if line.startswith('# '):
                                        title = line[2:].strip()
                                        break
                                    elif line.startswith('## '):
                                        title = line[3:].strip()
                                        break
                                    elif line.startswith('### '):
                                        title = line[4:].strip()
                                        break

                            # Create PaperInfo from Airtable data
                            paper = PaperInfo(
                                paper_id=paper_id,
                                title=title,
                                content=text_content,
                                status="draft",
                                metadata={
                                    "airtable_record_id": record.get("id"),
                                    "source": "airtable"
                                }
                            )

                            # Store in memory for future access
                            self.paper_memory.store_paper_info(paper)
                            return paper

            except Exception as e:
                print(f"Warning: Failed to fetch paper from Airtable: {str(e)}")

        return None
    
    def update_paper_content(
        self,
        paper_id: str,
        content: str,
        auto_save: bool = True
    ) -> Dict[str, Any]:
        """Update paper content with optional auto-save."""
        paper = self.paper_memory.get_paper_info(paper_id)
        if not paper:
            return {
                "success": False,
                "error": "Paper not found"
            }
        
        # Update content
        paper.content = content
        paper.updated_at = datetime.now()
        
        if auto_save:
            self.paper_memory.store_paper_info(paper)

            # Also update in Airtable if available and record exists
            if self.airtable_enabled and self.airtable_tool:
                airtable_record_id = paper.metadata.get("airtable_record_id")
                if airtable_record_id:
                    try:
                        # Update record in Airtable (using correct field name)
                        # Combine title and content for the 'text' field
                        combined_content = f"# {paper.title}\n\n{content}" if content else f"# {paper.title}"

                        airtable_result = self.airtable_tool._run(
                            table_name="Table 1",
                            record_data={
                                "text": combined_content
                            },
                            record_id=airtable_record_id,
                            paper_id=paper_id
                        )

                        if not airtable_result.get("success"):
                            print(f"Warning: Failed to update paper in Airtable: {airtable_result.get('error')}")

                    except Exception as e:
                        print(f"Warning: Exception updating paper in Airtable: {str(e)}")
        
        # Render the updated content
        rendered = self.renderer.render_markdown(content)
        
        # Extract additional information
        citations = self.renderer.extract_citations(content)
        outline = self.renderer.generate_paper_outline(content)
        
        return {
            "success": True,
            "paper_id": paper_id,
            "rendered": rendered,
            "citations": citations,
            "outline": outline,
            "updated_at": paper.updated_at.isoformat()
        }
    
    def get_paper_with_rendering(self, paper_id: str) -> Dict[str, Any]:
        """Get paper with rendered content."""
        paper = self.paper_memory.get_paper_info(paper_id)
        if not paper:
            return {
                "success": False,
                "error": "Paper not found"
            }
        
        # Render content if available
        rendered = None
        citations = []
        outline = {}
        
        if paper.content:
            rendered = self.renderer.render_markdown(paper.content)
            citations = self.renderer.extract_citations(paper.content)
            outline = self.renderer.generate_paper_outline(paper.content)
        
        return {
            "success": True,
            "paper": {
                "paper_id": paper.paper_id,
                "title": paper.title,
                "content": paper.content,
                "status": paper.status,
                "created_at": paper.created_at.isoformat(),
                "updated_at": paper.updated_at.isoformat(),
                "metadata": paper.metadata
            },
            "rendered": rendered,
            "citations": citations,
            "outline": outline
        }
    
    def search_papers(
        self, 
        query: str, 
        session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Search papers by content or title."""
        # Get all papers (in production, this would be more efficient)
        all_papers = self.paper_memory.list_user_papers(session_id or "")
        
        matching_papers = []
        query_lower = query.lower()
        
        for paper in all_papers:
            # Search in title and content
            title_match = query_lower in (paper.title or "").lower()
            content_match = query_lower in (paper.content or "").lower()
            
            if title_match or content_match:
                matching_papers.append({
                    "paper_id": paper.paper_id,
                    "title": paper.title,
                    "status": paper.status,
                    "created_at": paper.created_at.isoformat(),
                    "updated_at": paper.updated_at.isoformat(),
                    "relevance_score": 1.0 if title_match else 0.5
                })
        
        # Sort by relevance
        matching_papers.sort(key=lambda x: x["relevance_score"], reverse=True)
        
        return matching_papers
    
    def export_paper(self, paper_id: str, format: str = "markdown") -> Dict[str, Any]:
        """Export paper in various formats."""
        paper = self.paper_memory.get_paper_info(paper_id)
        if not paper:
            return {
                "success": False,
                "error": "Paper not found"
            }
        
        if format == "markdown":
            return {
                "success": True,
                "format": "markdown",
                "content": paper.content,
                "filename": f"{paper.title.replace(' ', '_')}.md"
            }
        elif format == "html":
            rendered = self.renderer.render_markdown(paper.content or "")
            return {
                "success": True,
                "format": "html",
                "content": rendered["html"],
                "filename": f"{paper.title.replace(' ', '_')}.html"
            }
        else:
            return {
                "success": False,
                "error": f"Unsupported format: {format}"
            }
