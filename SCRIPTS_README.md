# Paper Agent Management Scripts

This directory contains several utility scripts to help you manage the Paper Agent system easily.

## 📋 Available Scripts

### 1. `check-env.sh` - Environment Check
Verifies that all required dependencies and configurations are in place.

```bash
./check-env.sh
```

**What it checks:**
- ✅ Python 3 and pip3 installation
- ✅ Virtual environment setup
- ✅ Python package dependencies
- ✅ Node.js and npm installation
- ✅ Frontend dependencies (node_modules)
- ✅ Redis server and CLI
- ✅ Configuration files (.env)
- ✅ Port availability (3000, 8000, 6379)
- ✅ System resources (memory, disk, CPU)

### 2. `start.sh` - Start All Services
Starts the backend, frontend, and checks Red<PERSON> in the correct order.

```bash
./start.sh
```

**What it does:**
- 🔍 Checks and starts Redis if needed
- 🐍 Starts Python backend on port 8000
- ⚛️ Starts React frontend on port 3000
- 📊 Shows status of all services
- 🔗 Provides quick access links

**Services started:**
- **Backend**: http://localhost:8000
- **Frontend**: http://localhost:3000
- **API Health**: http://localhost:8000/api/v1/health

### 3. `manage.sh` - Service Management
Provides various management commands for running services.

```bash
./manage.sh [COMMAND]
```

**Available commands:**
- `status` - Show status of all services (default)
- `stop` - Stop all services
- `restart` - Restart all services
- `logs` - Check recent logs
- `test` - Test all connections
- `redis` - Show detailed Redis information
- `help` - Show help message

**Examples:**
```bash
./manage.sh status    # Show current status
./manage.sh stop      # Stop all services
./manage.sh test      # Test all connections
./manage.sh redis     # Detailed Redis info
```

## 🚀 Quick Start Guide

### First Time Setup

1. **Check your environment:**
   ```bash
   ./check-env.sh
   ```

2. **Fix any issues reported by the environment check**

3. **Start all services:**
   ```bash
   ./start.sh
   ```

4. **Verify everything is working:**
   ```bash
   ./manage.sh test
   ```

### Daily Usage

**Start the system:**
```bash
./start.sh
```

**Check status:**
```bash
./manage.sh status
```

**Stop the system:**
```bash
./manage.sh stop
```

**Restart if needed:**
```bash
./manage.sh restart
```

## 🔧 Troubleshooting

### Common Issues

**1. Redis not running:**
```bash
# Check Redis status
./manage.sh redis

# Start Redis manually
redis-server --daemonize yes
```

**2. Ports already in use:**
```bash
# Check what's using the ports
./manage.sh status

# Stop conflicting services
./manage.sh stop
```

**3. Missing dependencies:**
```bash
# Check what's missing
./check-env.sh

# Install Python dependencies
source venv/bin/activate
pip install -r requirements.txt

# Install frontend dependencies
cd frontend && npm install
```

**4. Environment variables missing:**
```bash
# Check .env file
cat .env

# Create .env file with required variables:
# OPENAI_API_KEY=your_key_here
# AIRTABLE_API_KEY=your_key_here
# AIRTABLE_BASE_ID=your_base_id_here
```

### Service-Specific Issues

**Backend Issues:**
- Check if virtual environment is activated
- Verify Python dependencies are installed
- Check .env file for required API keys
- Look at backend logs for specific errors

**Frontend Issues:**
- Verify Node.js and npm are installed
- Check if node_modules directory exists
- Run `npm install` in frontend directory if needed
- Check for port conflicts on 3000

**Redis Issues:**
- Install Redis if not present
- Start Redis service: `redis-server --daemonize yes`
- Check Redis logs: `./manage.sh redis`

## 📊 Service Information

### Default Ports
- **Frontend (React)**: 3000
- **Backend (FastAPI)**: 8000
- **Redis**: 6379

### Key URLs
- **Application**: http://localhost:3000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/v1/health
- **WebSocket**: ws://localhost:8000/api/v1/ws

### File Structure
```
paper_ui/
├── start.sh              # Start all services
├── manage.sh             # Manage services
├── check-env.sh          # Environment check
├── main.py               # Backend entry point
├── requirements.txt      # Python dependencies
├── .env                  # Environment variables
├── frontend/
│   ├── package.json      # Frontend dependencies
│   └── src/              # React source code
└── app/                  # Backend source code
```

## 🔍 Monitoring

### Real-time Status
```bash
# Watch status continuously
watch -n 5 './manage.sh status'
```

### Connection Testing
```bash
# Test all connections
./manage.sh test

# Test specific service
curl http://localhost:8000/api/v1/health
curl http://localhost:3000
redis-cli ping
```

### Log Monitoring
```bash
# Check recent logs
./manage.sh logs

# Monitor backend logs (if running in foreground)
tail -f backend.log

# Monitor Redis logs
redis-cli monitor
```

## 💡 Tips

1. **Always run `check-env.sh` first** when setting up on a new system
2. **Use `manage.sh status`** to quickly check if everything is running
3. **Run `manage.sh test`** after starting to verify connections
4. **Keep Redis running** in the background for session persistence
5. **Use `manage.sh stop`** to cleanly shut down all services

## 🆘 Getting Help

If you encounter issues:

1. Run `./check-env.sh` to verify your setup
2. Run `./manage.sh test` to test connections
3. Check the specific error messages in the script output
4. Verify your .env file has all required variables
5. Ensure all dependencies are installed correctly

For more detailed debugging, you can run the backend and frontend manually to see detailed error messages.
