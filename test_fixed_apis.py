"""Test the fixed Deep Research API and debug Airtable issues."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def test_deep_research_with_bearer():
    """Test Deep Research API with Bearer authentication."""
    print("🔬 Testing Deep Research API with Bearer Auth...")
    
    try:
        from app.tools.research import deep_research_tool, deep_research_status_tool
        
        # Test with a simple query
        print("   Testing deep research initiation...")
        result = deep_research_tool._run(
            query="artificial intelligence applications",
            depth=1,
            breadth=1,
            output_type="summary",
            priority="normal"
        )
        
        print(f"   Result: {result}")
        
        if result.get("success"):
            print("✅ Deep Research API: SUCCESS with Bearer auth!")
            print(f"   Task ID: {result.get('task_id')}")
            
            # Test status check
            task_id = result.get('task_id')
            if task_id:
                print("   Testing status check...")
                status_result = deep_research_status_tool._run(task_id=task_id)
                print(f"   Status Result: {status_result}")
                
                if status_result.get("success"):
                    print("✅ Deep Research Status: SUCCESS")
                    print(f"   Status: {status_result.get('status')}")
                else:
                    print("❌ Deep Research Status: FAILED")
                    print(f"   Error: {status_result.get('error')}")
        else:
            print("❌ Deep Research API: STILL FAILING")
            print(f"   Error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Deep Research: EXCEPTION - {e}")


def debug_airtable_config():
    """Debug Airtable configuration and test different approaches."""
    print("\n📊 Debugging Airtable Configuration...")
    
    # Check environment variables
    api_key = os.getenv("AIRTABLE_API_KEY")
    base_id = os.getenv("AIRTABLE_BASE_ID")
    
    print(f"   API Key present: {'✅' if api_key else '❌'}")
    print(f"   Base ID present: {'✅' if base_id else '❌'}")
    
    if api_key:
        print(f"   API Key format: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else api_key}")
    if base_id:
        print(f"   Base ID format: {base_id}")
    
    if not api_key or not base_id:
        print("❌ Missing Airtable credentials")
        return False
    
    # Test direct pyairtable connection
    try:
        print("\n   Testing direct pyairtable connection...")
        from pyairtable import Api
        
        api = Api(api_key)
        print("✅ API object created successfully")
        
        # Try to get base info
        try:
            table = api.table(base_id, "Papers")
            print("✅ Table object created successfully")
            
            # Try a simple operation - list tables or get schema
            print("   Attempting to fetch records...")
            records = table.all(max_records=1)
            print(f"✅ Successfully fetched {len(records)} records")
            
            if records:
                print(f"   Sample record fields: {list(records[0].get('fields', {}).keys())}")
            
            return True
            
        except Exception as table_error:
            print(f"❌ Table access failed: {table_error}")
            
            # Try different table names
            common_table_names = ["Papers", "Table 1", "Main", "Data", "Records"]
            print("   Trying common table names...")
            
            for table_name in common_table_names:
                try:
                    test_table = api.table(base_id, table_name)
                    test_records = test_table.all(max_records=1)
                    print(f"✅ Found working table: '{table_name}'")
                    return True
                except:
                    print(f"   ❌ '{table_name}' not found")
            
            return False
            
    except Exception as e:
        print(f"❌ Airtable API connection failed: {e}")
        return False


def test_airtable_with_fixes():
    """Test Airtable tools with potential fixes."""
    print("\n📊 Testing Airtable Tools...")
    
    try:
        from app.tools.airtable import airtable_overview_tool, airtable_create_update_tool
        
        # Test 1: Try with default table name
        print("   Testing with 'Papers' table...")
        result1 = airtable_overview_tool._run(
            table_name="Papers",
            max_records=3
        )
        
        if result1.get("success"):
            print("✅ Airtable Overview (Papers): SUCCESS")
            print(f"   Records found: {result1.get('record_count', 0)}")
            return True
        else:
            print(f"❌ Papers table failed: {result1.get('error')}")
        
        # Test 2: Try with common alternative table names
        alternative_names = ["Table 1", "Main", "Data"]
        for table_name in alternative_names:
            print(f"   Testing with '{table_name}' table...")
            result = airtable_overview_tool._run(
                table_name=table_name,
                max_records=3
            )
            
            if result.get("success"):
                print(f"✅ Airtable Overview ({table_name}): SUCCESS")
                print(f"   Records found: {result.get('record_count', 0)}")
                
                # Test create operation on working table
                print(f"   Testing create on '{table_name}'...")
                create_result = airtable_create_update_tool._run(
                    table_name=table_name,
                    record_data={
                        "Title": "Test from LangGraph Backend",
                        "Content": "Testing the fixed backend",
                        "Status": "test"
                    }
                )
                
                if create_result.get("success"):
                    print(f"✅ Airtable Create ({table_name}): SUCCESS")
                    print(f"   Record ID: {create_result.get('record_id')}")
                else:
                    print(f"❌ Create failed: {create_result.get('error')}")
                
                return True
            else:
                print(f"   ❌ {table_name} failed: {result.get('error')}")
        
        return False
        
    except Exception as e:
        print(f"❌ Airtable Tools: EXCEPTION - {e}")
        return False


def suggest_airtable_fixes():
    """Suggest fixes for Airtable issues."""
    print("\n🔧 Airtable Troubleshooting Suggestions:")
    
    print("\n1. **Check API Key Format:**")
    print("   - Should start with 'pat' (Personal Access Token)")
    print("   - Or start with 'key' (deprecated but might still work)")
    print("   - Get from: https://airtable.com/create/tokens")
    
    print("\n2. **Check Base ID:**")
    print("   - Should start with 'app' followed by alphanumeric")
    print("   - Get from: Base URL or API documentation")
    print("   - Example: appXXXXXXXXXXXXXX")
    
    print("\n3. **Check Table Name:**")
    print("   - Must match exactly (case-sensitive)")
    print("   - Common names: 'Papers', 'Table 1', 'Main'")
    print("   - Check in your Airtable base")
    
    print("\n4. **Check Permissions:**")
    print("   - API key needs read/write access to the base")
    print("   - Base must be shared with the API key owner")
    print("   - Check base permissions settings")
    
    print("\n5. **Alternative: Use Airtable.js (as you mentioned):**")
    print("   - Could create a Node.js microservice")
    print("   - Or use Airtable REST API directly")
    print("   - Might have better error messages")


def main():
    """Run the fixed API tests."""
    print("🚀 Testing Fixed APIs")
    print("=" * 50)
    
    # Check environment
    required_vars = ["DEEP_RESEARCH_API_KEY", "AIRTABLE_API_KEY", "AIRTABLE_BASE_ID"]
    missing = [var for var in required_vars if not os.getenv(var)]
    
    if missing:
        print(f"❌ Missing environment variables: {missing}")
        return
    
    print("✅ All required environment variables found")
    
    # Test Deep Research with Bearer auth
    test_deep_research_with_bearer()
    
    # Debug and test Airtable
    airtable_working = debug_airtable_config()
    if airtable_working:
        test_airtable_with_fixes()
    else:
        suggest_airtable_fixes()
    
    print("\n" + "=" * 50)
    print("🎯 Summary:")
    print("1. Deep Research API updated to use Bearer authentication")
    print("2. Airtable debugging completed")
    print("3. Check the suggestions above for Airtable fixes")
    
    print("\n💡 Next Steps:")
    print("1. If Deep Research works now: ✅ Great!")
    print("2. For Airtable: Check base ID, table name, and permissions")
    print("3. Consider the Airtable.js alternative if issues persist")


if __name__ == "__main__":
    main()
